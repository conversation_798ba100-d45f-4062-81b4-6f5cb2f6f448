using System.Linq;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using Newtonsoft.Json.Linq;

namespace JieNor.Framework.AppService.BillLock
{
    /// <summary>
    /// 单据编辑锁定汇总表：解锁
    /// </summary>
    [InjectService]
    [FormId("rpt_billlock")]
    [OperationNo("UnLock")]
    public class UnLock : AbstractOperationServicePlugIn
    {
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (this.SelectedRows.IsNullOrEmpty())
            {
                return;
            }

            var keys = this.SelectedRows.Select(s => s.PkValue);

            var billLockService = this.Container.GetService<IBillLockService>();
            billLockService.ReleaseLock(this.Context, keys);

            var tempTable = Context.Meta["rptTempTableName"];// this.GetQueryOrSimpleParam<string>("rptTempTableName", "");
            string sql = $"delete from {tempTable} where fid in ({keys.JoinEx(",", true)})";

            var dbService = this.Container.GetService<IDBServiceEx>();
            dbService.Execute(this.Context, sql);

            this.OperationContext.AddRefreshPageAction();
        }
    }
}
