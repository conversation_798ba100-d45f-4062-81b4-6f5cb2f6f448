using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.DTO.Order.DirectOrder;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MS.API.Controller.Order.DirectOrder
{
    public class PushDirectOrderShareCostBillController : BaseController<PushDirectOrderShareCostBillNoDTO>
    {
        public string FormId
        {
            get { return "ydj_order"; }
        }

        protected UserContext agentCtx { get; set; }

        /// <summary>
        /// 是否异步执行
        /// </summary>
        protected override bool IsAsync => false;

        /// <summary>
        /// 请求唯一主键
        /// </summary>
        protected override string UniquePrimaryKey => "OrderBillNo";

        /// <summary>
        /// 业务对象表单ID（用于异步操作日志记录）
        /// </summary>
        protected override string BizObjectFormId
        {
            get { return "ydj_order"; }
        }
        
        /// <summary>
        /// 销售合同数据包
        /// </summary>
        private List<DynamicObject> orderDys { get; set; }
        
        public override object Execute(PushDirectOrderShareCostBillNoDTO dto)
        {
            var resp = new BaseResponse<object>();

            if (!Valid(dto, resp)) return resp;

            if (orderDys != null && orderDys.Any() && this.agentCtx != null)
            {
                var option = GenerateOption(dto);

                var result = HttpGateway.InvokeBillOperation(this.agentCtx, this.FormId, orderDys, "pushsharecostbillno", option);
                
                if (result != null && result.IsSuccess && result.ComplexMessage.SuccessMessages.Any())
                {
                    var msgList = new List<string>();
                    foreach (var tempMessage in result.ComplexMessage.SuccessMessages)
                    {
                        msgList.Add(tempMessage);
                    }

                    if (result.ComplexMessage.ErrorMessages != null && result.ComplexMessage.ErrorMessages.Any())
                    {
                        foreach (var tempMessage in result.ComplexMessage.ErrorMessages)
                        {
                            msgList.Add(tempMessage);
                        }
                    }
                    resp.Code = 200;
                    resp.Message = string.Join(",",msgList.Select(x=>x));
                    resp.Success = true;

                    // 设置成功编码到集成操作日志
                    this.Request.SetBillNo(MSKey.SuccessNumber, dto.OrderBillNo);
                }
                else if(result != null && !result.IsSuccess)
                {
                    resp.Code = 400;
                    resp.Message = string.Join(",",result.ComplexMessage.ErrorMessages.Select(x=>x));
                    resp.Success = false;

                    // 设置失败编码到集成操作日志
                    this.Request.SetBillNo(MSKey.FailNumber, dto.OrderBillNo);
                }
                else
                {
                    resp.Code = 500;
                    resp.Message = "操作失败，未获取到有效的操作结果";
                    resp.Success = false;

                    // 设置失败编码到集成操作日志
                    this.Request.SetBillNo(MSKey.FailNumber, dto.OrderBillNo);
                }
            }
            
            return resp;
        }

        private bool Valid(PushDirectOrderShareCostBillNoDTO dto, BaseResponse<object> resp)
        {
            bool isTrue = true;

            if (dto.OrderBillNo.IsNullOrEmptyOrWhiteSpace())
            {
                isTrue = false;
                resp.Code = 400;
                resp.Message = "销售合同编码不能为空，请检查！";
                resp.Success = false;
                return isTrue;
            }

            if (dto.ShareCostBillNo.IsNullOrEmptyOrWhiteSpace())
            {
                isTrue = false;
                resp.Code = 400;
                resp.Message = "共享计提单号不能为空，请检查！";
                resp.Success = false;
                return isTrue;
            }
            
            var findOrderSqlStr = " select fid,fbillno,fmainorgid from t_ydj_order with(nolock ) where fbillno=@fbillno ";

            var sqlParams = new List<SqlParam>();

            sqlParams.Add(new SqlParam("@fbillno", DbType.String,dto.OrderBillNo));

            var findOrderDys = this.Context.ExecuteDynamicObject(findOrderSqlStr, sqlParams);
            
            if (findOrderDys == null || !findOrderDys.Any())
            {
                isTrue = false;
                resp.Code = 400;
                resp.Message = $"找不到对应的销售合同【{dto.OrderBillNo}】，请检查!";
                resp.Success = false;
                return isTrue;
            }
            
            if (findOrderDys != null && findOrderDys.Count > 1)
            {
                isTrue = false;
                resp.Code = 400;
                resp.Message = $"当前系统存在多个相同编码【{dto.OrderBillNo}】的销售合同，无法确定更新那个销售合同，请检查!";
                resp.Success = false;
                return isTrue;
            }
            
            var findOrderDy = findOrderDys.FirstOrDefault();

            var mainOgrId = Convert.ToString(findOrderDy["fmainorgid"]);
            

            if (mainOgrId != null && !mainOgrId.IsNullOrEmptyOrWhiteSpace())
            {
                this.agentCtx = this.Context.CreateAgentDBContext(mainOgrId);
            }
            else
            {
                isTrue = false;
                resp.Code = 400;
                resp.Message = "找不到对应的经销商，请检查!";
                resp.Success = false;
                return isTrue;
            }
            
            var orderSqlStr = @" fbillno=@billno and fmainorgid=@mainogrid ";

            if (this.agentCtx != null)
            {
                sqlParams.Clear();
                sqlParams.Add(new SqlParam("@billno",DbType.String,dto.OrderBillNo));
                sqlParams.Add(new SqlParam("@mainogrid",DbType.String,this.agentCtx.Company));
                orderDys = this.agentCtx.LoadBizDataByACLFilter(this.FormId, orderSqlStr,false,sqlParams).ToList();
                if (orderDys == null || !orderDys.Any())
                {
                    isTrue = false;
                    resp.Code = 400;
                    resp.Message = $"找不到对应的经销商下的编码为【{dto.OrderBillNo}】的销售合同，请检查是否删除了对应的销售合同";
                    return isTrue;
                }
            }
            
            return isTrue;
            
        }
        
        /// <summary>
        /// 生成Option参数
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        private Dictionary<string, object> GenerateOption(PushDirectOrderShareCostBillNoDTO dto)
        {
            var option = new Dictionary<string,object>();
            var dtoList = new List<PushDirectOrderShareCostBillNoDTO>(){dto};
            option.Add("shareCostDTOInfo",dtoList.ToJson());
            return option;
        }
        
        /// <summary>
        /// 创建分布式锁
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        protected override Dictionary<string, string> CreateDistributedLocks(PushDirectOrderShareCostBillNoDTO dto)
        {
            return new Dictionary<string, string>
            {
                { $"DistributedLock:{this.FormId}:{dto.OrderBillNo}", $"销售合同 {dto.OrderBillNo} 正在处理回传销售合同共享计提单号，请稍后再操作！" }
            };
        }
        
        /// <summary>
        /// 设置单据编码到请求上下文中（用于集成操作日志记录）
        /// </summary>
        protected override void SetNumbers()
        {
            var dto = this.Request.Dto as PushDirectOrderShareCostBillNoDTO;
            if (dto != null)
            {
                this.Request.SetBillNo(MSKey.BillNo, dto.OrderBillNo);
            }
        }
        
    }
}