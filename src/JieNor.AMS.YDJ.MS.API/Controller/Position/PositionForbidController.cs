using JieNor.AMS.YDJ.MS.API.DTO.Position;
using JieNor.AMS.YDJ.MS.API.Model;
using JieNor.AMS.YDJ.MS.API.Response;
using JieNor.AMS.YDJ.MS.API.Validation;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MS.API.Controller.Position
{
    /// <summary>
    /// 岗位：同步接口
    /// </summary>
    public class PositionForbidController : BaseAuthController<PositionForbidDto>
    {
        protected HtmlForm HtmlForm { get; set; }

        protected string FormId
        {
            get { return "ydj_position"; }
        }

        protected override bool IsAsync => false;

        protected override string UniquePrimaryKey => "id";

        protected override string BizObjectFormId => this.FormId;

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public override object Execute(PositionForbidDto dto)
        {
            this.HtmlForm = this.MetaModelService.LoadFormModel(this.Context, this.FormId);

            var resp = new BaseResponse<MuSiData>();

            if (!Valid(dto, resp)) return resp;

            resp.Code = 200;
            resp.Success = true;
            resp.Message = "操作成功！";
            resp.Data.Flag = MuSiFlag.SUCCESS.ToString();

            #region 查询相关数据
            // 根据外部Id查询所有的经销商Ids
            string sql = $@"select fid,ftranid,fstatus,fagentstatus from t_bas_agent with(nolock) where ftranid in ({dto.Data.Select(x => x.agentid)?.JoinEx(",", true)})";
            var agents = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            // 根据外部Id查询所有的用户Ids
            sql = $@"select fid,ftranid from t_sec_user with(nolock) where ftranid in ({dto.Data.Select(x => x.operatorid)?.JoinEx(",", true)})";
            var users = this.Context.ExecuteDynamicObject(sql, new List<SqlParam>());
            #endregion
            foreach (var group in dto.Data.GroupBy(s => new { agentid = s.agentid, operatorid = s.operatorid, status = s.status }))
            {
                try
                {
                    //判断经销商和操作人
                    var agentCtx = MusiAuthValidation.GetUserContext(this.Context, agents, Tuple.Create(group.Key.agentid, group?.FirstOrDefault().agentno), users, group.Key.operatorid, resp);
                    if (!resp.Success)
                    {
                        resp.Data.FailedNumbers.AddRange(group.Select(x => x.id));
                        resp.Data.FailedNumbers_Log.AddRange(group.Select(x => x.name));
                        continue;
                    }
                    var datas = agentCtx.LoadBizDataByNo(this.HtmlForm.Id, "ftranid", group.Select(x => x.id));

                    // 本批处理的转换数据
                    if (datas != null && datas.Count > 0)
                    {
                        var tranids = datas.Select(x => Convert.ToString(x["ftranid"]));
                        var Numbers = datas.Select(x => Convert.ToString(x["fnumber"]));
                        var operationNo = string.Empty;
                        switch (group.Key.status)
                        {
                            case "0":
                                operationNo = "forbid";
                                break;
                            case "1":
                                operationNo = "unforbid";
                                break;
                        }

                        var result = this.HttpGateway.InvokeBillOperation(agentCtx, this.HtmlForm.Id, datas, operationNo, new Dictionary<string, object>());
                        if (result.IsSuccess)
                        {
                            resp.Data.SucceedNumbers.AddRange(tranids);
                            resp.Data.SucceedNumbers_Log.AddRange(Numbers);
                        }
                        else
                        {
                            resp.Success = false;
                            resp.Data.FailedNumbers.AddRange(tranids);
                            resp.Data.FailedNumbers_Log.AddRange(Numbers);
                            resp.Data.ErrorMsgs.AddRange(result.ToString()?.Split(new string[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries));
                        }
                    }
                }
                catch (Exception e)
                {
                    this.LogService.Error(e);
                    resp.Success = false;
                    resp.Data.ErrorMsgs.Add(e.Message);
                }
            }

            if (!resp.Success)
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", resp.Data.ErrorMsgs);
                resp.Data.Flag = resp.Data.SucceedNumbers.Any()
                    ? MuSiFlag.PARTSUCCESS.ToString()
                    : MuSiFlag.FAIL.ToString();
            }

            return resp;
        }

        private bool Valid(PositionForbidDto dto, BaseResponse<MuSiData> resp)
        {
            if (dto.Data == null || dto.Data.Count == 0)
            {
                resp.Code = 400;
                resp.Message = "参数data不能为空！";
                resp.Success = false;
                resp.Data.ErrorMsgs.Add(resp.Message);

                return false;
            }

            List<string> errorMsgs = new List<string>();
            foreach (var data in dto.Data)
            {
                if (data.id.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数id不能为空！");
                }
                if (data.name.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数name不能为空！");
                }
                else if (data.agentid.IsNullOrEmptyOrWhiteSpace())
                {
                    errorMsgs.Add("参数agentid不能为空！");
                }
                //如果二级组织不为空，说明是二级分销用户组织操作新增修改
                if (!data.secagentid.IsNullOrEmptyOrWhiteSpace())
                {
                    data.agentid = data.secagentid;
                    data.agentno = data.secagentno;
                }
            }

            if (errorMsgs.Any())
            {
                resp.Code = 400;
                resp.Message = string.Join("\r\n", errorMsgs);
                resp.Success = false;

                resp.Data.ErrorMsgs = errorMsgs;
                resp.Data.Flag = MuSiFlag.FAIL.ToString();

                return false;
            }

            return true;
        }
    }
}
