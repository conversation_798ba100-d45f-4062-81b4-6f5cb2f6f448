///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/stk/stk_supaccount.js
*/
; (function () {
    var stk_supaccount = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）
        _child.prototype.entryId = 'freportlist';

        //页面视图初始化后事件
        _child.prototype.onViewInitialized = function (args) {
            this.setEntryHeight();
        };

        //浏览器窗口缩放时触发
        _child.prototype.onResize = function (args) {
            this.setEntryHeight();
        };

        //表格高度自适应
        _child.prototype.setEntryHeight = function () {
            var that = this;
            var wh = $(window).height();
            var searchHeight = that.Model.getEleMent({ id: '.search-box' }).outerHeight();
            var gridHeight = wh - searchHeight - 240;
            if (gridHeight < 150) gridHeight = 150;
            that.Model.setEntryHeight({ id: that.entryId, value: gridHeight });
        };

        //初始化事件
        _child.prototype.onInitialized = function (args) {
            var that = this;
            that.Model.refresh();
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.entryId:
                    e.result = { rownumbers: false, multiselect: false };
                    break;
            }
        };

        //表格双击事件
        _child.prototype.onEntryRowDblClick = function (e) {
            var that = this;
            //打开账户明细页面
            that.showDetail(e);
        }

        //表格按钮点击事件
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.entryId:
                    switch (e.btnid.toLowerCase()) {
                        case 'detail':
                            //打开账户明细页面
                            that.showDetail(e);
                            break;
                    }
                    break;
            }
        };

        //打开账户明细页面
        _child.prototype.showDetail = function (e) {
            var that = this;
            that.Model.showReport({
                formId: 'stk_supdetail',
                param: {
                    flag: true,
                    __gp__: JSON.stringify({
                        __entry: e.data.fsupplierid
                    })
                },
                cp: {
                    fdealerid: e.data.fsupplierid,
                    fdealername: e.data.fsuppliername
                }
            });
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                case 'dealersearch':
                    e.result = true;
                    that.Model.refresh({ pageIndex: 1 });
                    break;
            };
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode.toLowerCase()) {
                case 'test':

                    break;
            }
        };

        /**
         * @description 在刷新报表前触发：可以在该事件中收集报表查询参数
         * @param {object} args
         */
        _child.prototype.onBeforeRefresh = function (args) {
            var that = this;
            var fdealerid = that.Model.getSimpleValue({ id: 'fsettlemainid' });
            args.param = {
                fdealerid: fdealerid,
                params: JSON.stringify({
                    fgoodssum: 'settleaccount_type_01',
                    frebate: 'settleaccount_type_02',
                    fbond: 'settleaccount_type_03',
                    fafter: 'settleaccount_type_04',
                    fpk: 'settleaccount_type_05',
                    factivity: 'settleaccount_type_06'
                })
            };
        };

        return _child;
    })(BillPlugIn);
    window.stk_supaccount = window.stk_supaccount || stk_supaccount;
})();