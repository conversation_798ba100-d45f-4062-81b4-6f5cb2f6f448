/**
 * 属性选配-单件
 * @ sourceURL=/fw/js/ydj/sel/sel_propselectionsingle.js
 */
 ; (function () {
    var sel_propselectionsingle = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);

            that.cid = {
                'S648': '客户ID'
            };

            // 锁定的属性值
            that.lockProps = {
                'S648': '客户ID',
                'S173': '床垫其它定制'
            };

            // 默认属性值
            that.defaultPropValue = 'C0110';

            that.isAIBed = false;
        }
        __extends(_child, _super);

        //页面初始化后触发
        _child.prototype.onInitialized = function (e) {
            var that = this;

            that.procNonStandard();

            that.procAIBedDefaultPropValue();
        };

        //处理AI云床垫的默认属性值
        _child.prototype.procAIBedDefaultPropValue = function() {
            var that = this;
            var rowDatas = that.Model.getEntryData({ id: 'fentry' });

            for (var i = 0; i < rowDatas.length; i++) {
                var fselpropid = rowDatas[i].fselpropid;
                if (fselpropid && that.cid && that.cid.hasOwnProperty(fselpropid.fnumber) && that.cid[fselpropid.fnumber] === fselpropid.fname) {
                    that.isAIBed = true;
                    break;
                }
            }

            if (that.isAIBed) {
                for (var i = 0; i < rowDatas.length; i++) {
                    var fselpropid = rowDatas[i].fselpropid;
                    if (fselpropid && that.lockProps 
                        && that.lockProps.hasOwnProperty(fselpropid.fnumber) 
                        && that.lockProps[fselpropid.fnumber] === fselpropid.fname
                        ) {
                        // C0110
                        var value = that.Model.getValue({ id: 'fpropvalue', row: rowDatas[i].id });
                        if (value && value.number == that.defaultPropValue) {
                            that.Model.setValue({ id: 'fpropvalue', row: rowDatas[i].id, value: '' });
                        }
                    }
                }
            }
        }

        //处理非标逻辑
        _child.prototype.procNonStandard = function () {
            var that = this;

            //显示隐藏列
            var isNonStandard = that.Model.getValue({ id: 'fisnonstandard' });
            that.Model.setVisible({
                id: ['fallowcustom', 'fnonstandard'],
                value: isNonStandard,
                batch: true
            });
            that.Model.setVisible({
                id: ['frange'],
                value: !isNonStandard,
                batch: true
            });

            //动态设置对话框标题
            var caption = '标准定制 - 单件';
            if (isNonStandard) {
                caption = '非标定制 - 单件';
            }
            that.Model.setPageCaption({ caption: caption });
        };

        //平台在调用页面的 resizeForm 方法前触发
        _child.prototype.onBeforeResizeForm = function (args) {
            var that = this;
            var offset = 95; //偏移量
            if (args.dialogResize || args.source === 'scroll') {
                offset = 60;
            }
            var pageInfo = that.Model.getPageInfo();
            var baseInfoHeight = that.Model.getEleMent({ id: '.base-info' }).outerHeight(true);
            var entryHeight = pageInfo.height - baseInfoHeight - offset;
            that.Model.setEntryHeight({ id: 'fentry', value: entryHeight });
        };

        //获取字段编辑状态时触发（用于控制字段是否可编辑）
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var isNonStandard = that.Model.getValue({ id: 'fisnonstandard' });
            switch (e.id.toLowerCase()) { 
                case 'fnonstandard':
                    //如果不是非标产品 或者 不支持非标录入，则不允许编辑
                    var allowCustom = that.Model.getValue({ id: 'fallowcustom', row: e.row });
                    if (!isNonStandard || !allowCustom) {
                        e.result.enabled = false;
                    }
                    break;
                case 'frange':
                    if (isNonStandard) {
                        e.result.enabled = false;
                    }
                    if (e.value.fdatatype === '2' && (e.value.fmin > 0 || e.value.fmax > 0)) {
                        e.result.enabled = true;
                    } else {
                        e.result.enabled = false;
                    }
                    break;
            }
            
            if (that.isAIBed) {
                // AI云床垫的【客户ID】和【床垫其他定制】要锁定
                var fselpropid = that.Model.getValue({ id: "fselpropid", row: e.row });
                if (fselpropid && that.lockProps && that.lockProps.hasOwnProperty(fselpropid.fnumber) && that.lockProps[fselpropid.fnumber] === fselpropid.fname) {
                    e.result.enabled = false;
                }
            }
        };

        _child.prototype.onEntryCellClick = function (e) {
            var that = this;

            switch (e.fieldId.toLowerCase()) {
                case 'frange': 
                    var $target = $(e.e.target);
                    if ($target)
                    {
                        //单元格动态添加placeholder 样式
                        var pageId = that.Model.viewModel.pageId;
                        $(`#${pageId}`).find(`span[rowid=${e.row}]`).siblings('input').attr('placeholder', e.data.fmin + "~" + e.data.fmax);
                    } 
                    break;
            }
        };

        //设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            var fmin = that.Model.getSimpleValue({ id: 'fmin', row: e.row });
            var fmax = that.Model.getSimpleValue({ id: 'fmax', row: e.row });
            var dataType = that.Model.getSimpleValue({ id: 'fdatatype', row: e.row });
            switch (e.id.toLowerCase()) {
                case 'fnonstandard':
                    //如果属性数据类型是【数值】则非标值只能录入数值
                    if (dataType === '2' && isNaN(e.value)) {
                        var selProp = that.Model.getValue({ id: 'fselpropid', row: e.row });
                        var selPropName = $.trim(selProp && selProp.fname);
                        var oldValue = that.Model.getValue({ id: 'fnonstandard', row: e.row });
                        e.value = oldValue;
                        e.result = true;
                        yiDialog.warn('当前行属性【{0}】的数据类型是【数值】，非标值只允许录入数值！'.format(selPropName));
                    }
                    break; 
                case 'frange':
                    if (dataType === '2' && isNaN(e.value)) {
                        var selProp = that.Model.getValue({ id: 'fselpropid', row: e.row });
                        var selPropName = $.trim(selProp && selProp.fname);
                        var oldValue = that.Model.getValue({ id: 'frange', row: e.row });
                        e.value = oldValue;
                        e.result = true;
                        yiDialog.warn('当前行属性【{0}】的数据类型是【数值】，限定范围只允许录入数值！'.format(selPropName));
                        break;
                    }
                    if (e.value > fmax || e.value < fmin) {
                        e.value = '';
                        e.result = true;
                        yiDialog.warn('属性值不允许超出限定范围！');
                        break;
                    }
                    that.RangeFieldChanged(e);
                    break;

            }
        };

        //表单字段值变化时触发
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fpropvalue':
                    that.propValueFieldChanged(e);
                    break;
                case 'fnonstandard':
                    that.nonStandardFieldChanged(e);
                    break;
                //case 'frange':
                //    that.RangeFieldChanged(e);
                //    break;
            }
        };

        //处理【属性值】字段变化逻辑
        _child.prototype.propValueFieldChanged = function (e) {
            var that = this;

            //如果属性值不是由非标录入时生成的，则将非标值清空
            if (!e.value.fnosuitcreate) {
                that.Model.setValue({ id: 'fnonstandard', value: '', row: e.row });
            }

            //加载【属性值图片】字段值
            //that.loadPropValueImage(e);

            //加载【属性】字段相关的其他属性信息
            that.loadRelationProp(e);
        };

        _child.prototype.RangeFieldChanged = function (e) {
            var that = this;

            var propValueName = $.trim(e.value);
            if (!propValueName) return;
            var selPropId = that.Model.getSimpleValue({ id: 'fselpropid', row: e.row });
            var productId = that.Model.getSimpleValue({ id: 'fproductid' }); 
            var selCategoryId = that.Model.getSimpleValue({ id: 'fselcategoryid', row: e.row });
            var selProp = that.Model.getValue({ id: 'fselpropid', row: e.row });

            var propList = [];
            var entry = that.Model.getEntryData({ id: 'fentry' });
            for (var i = 0; i < entry.length; i++) { 
                //当前编辑的属性被认为是被约束的属性（主要用于匹配约束条件中的约束值公式）
                var isRestricted = entry[i].id === e.row;

                var selProp = entry[i].fselpropid;
                var propValueSrc = entry[i].fpropvaluesrc;
                var dataType = entry[i].fdatatype;
                var _propValue = entry[i].fpropvalue;
                var _propValueId = _propValue;
                var _propValueName = _propValue;
                var _propValueNumber = _propValue;
                if (propValueSrc.id === 'basedata' || propValueSrc.id === 'enumdata') {
                    if (_propValue) {
                        _propValueId = _propValue.id;
                        _propValueName = _propValue.fname;
                        _propValueNumber = _propValue.fnumber;
                    }
                }
                if (_propValueName || isRestricted) {
                    propList.push({
                        propId: selProp.id,
                        propName: selProp.fname,
                        propNumber: selProp.fnumber,
                        valueId: _propValueId,
                        valueName: _propValueName,
                        valueNumber: _propValueNumber,
                        isRestricted: isRestricted,
                        propValueDataType: dataType //属性值数据类型（字符、数值）
                    });
                }
            }

            var res = that.Model.invokeFormService({
                formId: 'sel_propvalue',
                opcode: 'loadorcreate',
                sync: true,
                param: {
                    simpleData: {
                        propId: selPropId,
                        propName: selProp.fname,
                        propNumber: selProp.fnumber,
                        propValueName: propValueName,
                        propList: JSON.stringify(propList),
                        selCategoryId, selCategoryId,
                        isrange: true,
                        productId: productId
                    }
                }
            });
            var srvData = res && res.operationResult && res.operationResult.srvData;

            //设置属性值
            var propValue = $.extend({ id: '', fname: '', fnumber: '' }, srvData || {});
            //后台返回空 则清空当前字段，但是下面几个方式都清不掉
            if (propValue.id =='') {
                e.value = '';
                e.result = true; 
            }
            that.Model.setValue({ id: 'fpropvalue', value: propValue, row: e.row });
        };

        //处理【非标值】字段变化逻辑
        _child.prototype.nonStandardFieldChanged = function (e) {
            var that = this;

            var propValueName = $.trim(e.value);
            if (!propValueName) return;

            var selPropId = that.Model.getSimpleValue({ id: 'fselpropid', row: e.row });
            var productId = that.Model.getSimpleValue({ id: 'fproductid' });
            ////加载或创建属性值基础资料
            //that.Model.invokeFormOperation({
            //    id: 'loadorcreate',
            //    opcode: 'loadorcreate',
            //    opctx: { row: e.row },
            //    param: {
            //        formId: 'sel_propvalue',
            //        propId: selPropId,
            //        propValueName: propValueName
            //    }
            //});

            //此处使用同步请求，避免录入非标值后点击确认时，非标值没有被处理的问题
            //同步加载或创建属性值基础资料
            var res = that.Model.invokeFormService({
                formId: 'sel_propvalue',
                opcode: 'loadorcreate',
                sync: true,
                param: {
                    simpleData: {
                        propId: selPropId,
                        propValueName: propValueName,
                        productId: productId
                    }
                }
            });
            var srvData = res && res.operationResult && res.operationResult.srvData;

            //设置属性值
            var propValue = $.extend({ id: '', fname: '', fnumber: '' }, srvData || {});
            that.Model.setValue({ id: 'fpropvalue', value: propValue, row: e.row });
        };

        //加载【属性值图片】字段值
        _child.prototype.loadPropValueImage = function (e) {
            var that = this;
            var propValueId = $.trim(e.value && e.value.id);
            if (!propValueId) {
                //清空【属性图片】字段值
                that.Model.setValue({ id: 'fimage', value: { id: '', name: '' }, row: e.row });
                return;
            }
            //加载属性值图片
            that.Model.invokeFormOperation({
                id: 'loadpropvalueimage',
                opcode: 'loadpropvalueimage',
                opctx: { row: e.row },
                param: {
                    formId: 'sel_propvalue',
                    propValueId: propValueId,
                }
            });
        };

        //加载【属性】字段相关的其他属性信息
        _child.prototype.loadRelationProp = function (e) {
            var that = this;

            var isNonStandard = that.Model.getValue({ id: 'fisnonstandard' });
            var enableConstraint = that.Model.getSimpleValue({ id: 'fenableconstraint', row: e.row })
            if (isNonStandard && !enableConstraint) return;

            //将当前属性启用选配约束
            that.Model.setValue({ id: 'fenableconstraint', value: true, row: e.row });

            if (!$.trim(e.value.fnumber)) return;

            var propObj = that.Model.getValue({ id: 'fselpropid', row: e.row });
            var propNumber = $.trim(propObj && propObj.fnumber);
            if (!propNumber) return;

            //同步加载与当前属性相关的其他属性信息
            var selCategoryId = that.Model.getSimpleValue({ id: 'fselcategoryid' });
            var res = that.Model.invokeFormService({
                formId: 'sel_propselectionsingle',
                opcode: 'loadrelationprop',
                sync: true,
                param: {
                    simpleData: {
                        selCategoryId: selCategoryId,
                        propNumber: propNumber
                    }
                }
            });
            var srvData = res && res.operationResult && res.operationResult.srvData;
            var relationPropNumbers = srvData && srvData.relationPropNumbers;
            if (!relationPropNumbers || relationPropNumbers.length < 1) return;

            var entry = that.Model.getEntryData({ id: 'fentry' });
            for (var i = 0; i < relationPropNumbers.length; i++) {
                var relPropNumber = $.trim(relationPropNumbers[i]).toLowerCase();
                for (var j = 0; j < entry.length; j++) {
                    var _propNumber = $.trim(entry[j].fselpropid.fnumber).toLowerCase();
                    if (_propNumber === relPropNumber) {
                        //将相关的属性启用选配约束
                        that.Model.setValue({ id: 'fenableconstraint', value: true, row: entry[j].id });
                        break;
                    }
                }
            }
        };

        //表单菜单点击时触发
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'tbpageconfirm':
                    that.pageConfirm(e);
                    break;
            }
        };

        //确定
        _child.prototype.pageConfirm = function (e) {
            var that = this;
            e.result = true; //该操作由本插件接管，不再触发平台标准操作

            var propList = [];
            var auxEntry = [];

            var entry = that.Model.getEntryData({ id: 'fentry' });
            if (!entry || entry.length <= 0) {
                yiDialog.warn('当前属性信息空，无法选配！');
                return;
            }
            var enableConstraint = false;
            for (var i = 0; i < entry.length; i++) {
                if (!enableConstraint && entry[i].fenableconstraint == true) {
                    enableConstraint = true;
                }
                var selProp = entry[i].fselpropid;
                var propValueSrc = entry[i].fpropvaluesrc;
                var propValue = entry[i].fpropvalue;
                var dataType = entry[i].fdatatype;
                var isControlMust = entry[i].fiscontrolmust;
                var selPropName = selProp && selProp.fname || '';
                var propValueId = propValue;
                var propValueName = propValue;
                var propValueNumber = propValue;
                if (propValueSrc.id === 'basedata' || propValueSrc.id === 'enumdata') {
                    if (propValue) {
                        propValueId = propValue.id;
                        propValueName = propValue.fname;
                        propValueNumber = propValue.fnumber;
                    }
                }
                if (isControlMust && !propValueId) {
                    yiDialog.warn('第{0}行的【{1}】属性值要求必录，请检查！'.format(i + 1, selPropName));
                    return;
                }
                if (propValueName) {
                    propList.push({
                        propId: selProp.id,
                        propName: selProp.fname,
                        propNumber: selProp.fnumber,
                        valueId: propValueId,
                        valueName: propValueName,
                        valueNumber: propValueNumber,
                        propValueDataType: dataType //属性值数据类型（字符、数值）
                    });
                    auxEntry.push({
                        fauxpropid: {
                            id: selProp.id,
                            fname: selProp.fname,
                            fnumber: selProp.fnumber
                        },
                        fvalueid: propValueId,
                        fvaluename: propValueName,
                        fvaluenumber: propValueNumber
                    });
                }
            }

            //标准选配时，需要检查当前选配的属性值是否符合要求
            var isNonStandard = that.Model.getValue({ id: 'fisnonstandard' });
            if (propList.length > 0) {

                var productId = that.Model.getSimpleValue({ id: 'fproductid' });
                var selCategoryId = that.Model.getSimpleValue({ id: 'fselcategoryid' });

                that.Model.invokeFormService({
                    formId: 'sel_propselectionsingle',
                    opcode: 'checkpropvalue',
                    param: {
                        simpleData: {
                            productId: productId,
                            isNonStandard: isNonStandard,
                            selCategoryId: selCategoryId,
                            propList: JSON.stringify(propList)
                        }
                    },
                    callback: function (r) {
                        var isSuccess = r && r.operationResult && r.operationResult.isSuccess;
                        if (!isSuccess) return;

                        that.confirmReturn(auxEntry);
                    }
                });
                return;
            }

            that.confirmReturn(auxEntry);
        };

        //确定返回
        _child.prototype.confirmReturn = function (auxEntry) {
            var that = this;

            //回填业务单据辅助属性值，并关闭对话框
            var cp = that.formContext.cp;
            var parentView = Index.getPage(cp.parentPageId);
            if (parentView) {
                var auxPropArgs = { id: cp.fieldKey, row: cp.flexRow, value: { fentity: auxEntry } };

                //对业务单据辅助属性字段设置
                parentView.Model.setValue(auxPropArgs);
            }

            //关闭对话框
            that.Model.close();
        };

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'fpropvalue':
                    that.propValueQueryFilterString(e);
                    break;
            }
        };

        //处理【属性值】字段的过滤条件
        _child.prototype.propValueQueryFilterString = function (e) {
            var that = this;

            //通过 simpleData 传递自定义的参数值，这些参数值可以被后端插件事件获取，用于实现一些复杂的过滤条件
            var productId = that.Model.getSimpleValue({ id: 'fproductid' });
            var isNonStandard = that.Model.getValue({ id: 'fisnonstandard' });
            var selCategoryId = that.Model.getSimpleValue({ id: 'fselcategoryid' });
            var selPropId = that.Model.getSimpleValue({ id: 'fselpropid', row: e.row });
            var enableConstraint = that.Model.getSimpleValue({ id: 'fenableconstraint', row: e.row }) === true;

            var propList = [];
            var entry = that.Model.getEntryData({ id: 'fentry' });
            for (var i = 0; i < entry.length; i++) {

                //当前编辑的属性被认为是被约束的属性（主要用于匹配约束条件中的约束值公式）
                var isRestricted = entry[i].id === e.row;

                var selProp = entry[i].fselpropid;
                var propValueSrc = entry[i].fpropvaluesrc;
                var dataType = entry[i].fdatatype;
                var _propValue = entry[i].fpropvalue;
                var _propValueId = _propValue;
                var _propValueName = _propValue;
                var _propValueNumber = _propValue;
                if (propValueSrc.id === 'basedata' || propValueSrc.id === 'enumdata') {
                    if (_propValue) {
                        _propValueId = _propValue.id;
                        _propValueName = _propValue.fname;
                        _propValueNumber = _propValue.fnumber;
                    }
                }
                if (_propValueName || isRestricted) {
                    propList.push({
                        propId: selProp.id,
                        propName: selProp.fname,
                        propNumber: selProp.fnumber,
                        valueId: _propValueId,
                        valueName: _propValueName,
                        valueNumber: _propValueNumber,
                        isRestricted: isRestricted,
                        propValueDataType: dataType //属性值数据类型（字符、数值）
                    });
                }
            }

            e.result.simpleData = {
                productId: productId,
                isNonStandard: isNonStandard,
                selCategoryId: selCategoryId,
                selPropId: selPropId,
                enableConstraint: enableConstraint,
                propList: JSON.stringify(propList)
            };
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'loadpropvalueimage':
                    //设置属性值图片
                    var imageId = $.trim(srvData && srvData.id);
                    var imageName = $.trim(srvData && srvData.name);
                    that.Model.setValue({ id: 'fimage', value: { id: imageId, name: imageName }, row: e.opctx.row });
                    break;
                case 'loadorcreate':
                    //设置属性值
                    var propValue = $.extend({ id: '', fname: '', fnumber: '' }, srvData || {});
                    that.Model.setValue({ id: 'fpropvalue', value: propValue, row: e.opctx.row });
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.sel_propselectionsingle = sel_propselectionsingle;
})();