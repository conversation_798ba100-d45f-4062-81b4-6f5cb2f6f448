// <reference path="/fw/js/basepage.js" />
//@ sourceURL=/fw/js/ydj/bas/bas_product.js
; (function () {
    var coo_product = (function (_super) {
    	//构造函数
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);
        
        //初始化编辑页面插件
        _child.prototype.onBillInitialized = function (args) {
        	var that = this;
			//编辑时处理相关字段的隐藏和显示
			//that.contentDis(this.Model.getSimpleValue({id:'fiscontent'}));
			//当前商品的id
            that.Model.setValue({id:'fsynid',value:that.Model.pkid});
        };
        
        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            var listId = that.Model.getSelectRows({});
            if (!args.opcode) return;
            switch (args.opcode) {
                case 'synproduct':
                    args.result = true;
                    //点击同步商品按钮显示同步本地商品弹窗
//                  if(listId.length>0){
                    	that.showSynDialog();
//                  }else{
//                  	yiDialog.mt({msg:'请选择协同商品再执行同步操作！', skinseq: 2});
//                  }
                break;
            }
        };

        _child.prototype.onEntryRowDblClick = function (args) {
            args.result = true;
        };
        
        //显示协同发布弹窗
        _child.prototype.showSynDialog = function (e) {
            var that = this;
            var selectData;
//          var domainType = that.formContext.domainType;
//          if(that.formContext.domainType == 'list'){
//          	var listId = that.Model.getSelectRows({});
//	            selectData.push(listId);
	            var cp = {
//	                domainType
	            };
				cp = $.extend(true, cp, selectData);
//          }else if(that.formContext.domainType == 'bill'){
//          	var billId = that.Model.pkid;
//          	var cp = {
//	                domainType,
//	                billId
//	            };
//				cp = $.extend(true, cp, selectData);
//          }
            
            //弹出成本核算对话框
            that.Model.showForm({
                formId: 'coo_synproduct',
                param: { openStyle: Consts.openStyle.modal },
                cp: cp
            });
        };
      	
        
        
        return _child;
    })(BasePlugIn);
    window.coo_product = window.coo_product || coo_product;
})();