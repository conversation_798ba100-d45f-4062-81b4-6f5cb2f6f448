/*
 * description:商品价目业务控制插件
 * author:
 * create date:
 * modify by:
 * modify date:
 * remark:
 *@ sourceURL=/fw/js/ydj/bas/ydj_price.js
*/
; (function () {
    var ydj_reprice = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        _child.prototype.entry = 'fentry';
        _child.prototype.cusentry = 'fcusentry';

        //业务插件内容写在此
        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            that.hideOrShowEntryField();
            that.hideOrShowCusEntry();
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e || !e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.entry:
                case that.cusentry:
                    e.result = { multiselect: false };
                    break;
            }
        };
        //业务插件内容写在此
        _child.prototype.onCustomEntryCellOperation = function (e) {
            var that = this;

            var Status = e.data.fconfirmstatus;

            if (e.oper || !e.data) {
                return;
            } else {
                e.cancel = true;
                if (Status && Status.id === '1') {
                    return e.result = [{
                        id: 'confirm',
                        text: '确认',
                        rowid: e.row
                    }];
                } else if (Status && Status.id === '2') {
                    return e.result = [{
                        id: 'cancelconfirm',
                        text: '取消确认',
                        rowid: e.row
                    }];
                }
            }
        };

        //标准定制
        _child.prototype.showstandardcustom = function () {
            debugger;
            var that = this;
            //选中行
            var ds = that.Model.getSelectRows({ id: that.entry });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行标准定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            };
            if (ds) {
                var attrinfo = ds[0].data.fattrinfo;
                //var rowData = that.Model.getEntryRowData({ id:that.entry, row: attrinfo.row });
                ////选中行
                //判断物料是否启用 选配类别 
                var fisunstandard = ds[0].data.funstdtype;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且未勾选非标定制
                if (Isenableselectioncategory && !fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    //弹出“标准定制-单件”功能框 
                    that.Model.propSelection({
                        auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                        productId: ds[0].data.fproductid.id, //商品ID
                        row: ds[0].data.id //辅助属性字段所在的明细行ID
                    });
                }
                else {
                    yiDialog.warn('当前商品未启用选配或勾选了非标定制，不允许标准定制！');
                    return;
                }
            }
        };

        //非标定制
        _child.prototype.showunstandardcustom = function () {
            var that = this;
            //选中行
            var that = this;
            var ds = that.Model.getSelectRows({ id: that.entry });
            if (ds.length == 0) {
                yiDialog.warn('请选择一条数据再进行标准定制！');
                return;
            }
            else if (ds.length > 1) {
                yiDialog.warn('标准定制不支持多选！');
                return;
            }
            if (ds) {
                //判断选中行物料是否启用 选配类别
                var fisunstandard = ds[0].data.funstdtype;
                var fissuit = ds[0].data.fissuitflag;
                //是否启用选配
                var Isenableselectioncategory = false;
                if (ds[0].data.fselcategoryid && ds[0].data.fselcategoryid != '') {
                    Isenableselectioncategory = true;
                }
                //当前行产品启用选配且 勾选非标定制
                if (Isenableselectioncategory && fisunstandard) {
                    //如果当前行 非选配套件，则弹出“标准定制-单件”功能框
                    if (!fissuit) {
                        //弹出“标准定制-单件”功能框
                        that.Model.propSelection({
                            auxPropFieldKey: 'fattrinfo', //辅助属性字段标识
                            productId: ds[0].data.fproductid.id, //商品ID
                            row: ds[0].data.id //辅助属性字段所在的明细行ID
                        });
                    }
                }
                else {
                    yiDialog.warn('当前商品未启用选配或未勾选非标定制，不允许非标准定制！');
                    return;
                }
            }
        };


        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                //确认
                case 'confirm':
                case 'cancelconfirm':
                    if (isSuccess) {
                        //刷新页面
                        that.Model.refresh();
                    }
                    break;
            }
        };
        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var productId = '';
            var fconfirmstatus = $.trim(that.Model.getSimpleValue({ id: 'fconfirmstatus', row: e.row }));

            if (fconfirmstatus == 2) {
                e.result.enabled = false;
            }
            switch (e.id.toLowerCase()) {
                case 'funstdtype':
                    //允许选配 (商品档案未勾选允许选配)下单时不允许勾选非标选项 - PC端
                    var fispresetprop = that.Model.getSimpleValue({ id: "fispresetprop", row: e.row });
                    var fcustom = that.Model.getSimpleValue({ id: "fcustom", row: e.row });
                    //如果是审核中、已审核时时不允许编辑的 如果不加此判断会导致单据审核了，可以勾选"是否非标"
                    if (status != 'D' && status != 'E') {
                        if (!fispresetprop && !fcustom) {
                            e.result.enabled = false;
                        } else {
                            e.result.enabled = true;
                        }
                    }
                    // 如果勾选上【是否非标】后, 不允许取消勾选, 只允许删除
                    if (e.value.funstdtype) {
                        e.result.enabled = false;
                    }
                    break;
            }

        };
        //表格按钮点击事件
        _child.prototype.onEntryButtonClick = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.entry:
                    var rowData = that.Model.getEntryRowData({ id: that.entry, row: e.row });
                    switch (e.btnid.toLowerCase()) {
                        //确认
                        case 'confirm':
                            that.Model.invokeFormOperation({
                                id: 'Confirm',
                                opcode: 'Confirm',
                                opname: '确认',
                                selectedRows: [{ PKValue: that.Model.pkid, entrypkvalue: rowData.id, EntityKey: 'fentry' }],
                                param: {
                                    formId: 'ydj_price',
                                    domainType: Consts.domainType.bill
                                }
                            });
                            break;
                        //取消确认
                        case 'cancelconfirm':
                            that.Model.invokeFormOperation({
                                id: 'CancelConfirm',
                                opcode: 'CancelConfirm',
                                opname: '取消确认',
                                selectedRows: [{ PKValue: that.Model.pkid, entrypkvalue: rowData.id, EntityKey: 'fentry' }],
                                param: {
                                    formId: 'ydj_price',
                                    domainType: Consts.domainType.bill
                                }
                            });
                            break;
                    }
                    break;
            }
        };
        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            var listId = that.Model.getSelectRows({});
            var domainType = that.formContext.domainType;
            if (!args.opcode) return;
            switch (args.opcode) {
                //标准定制
                case 'showstandardcustom':
                    args.result = true;
                    that.showstandardcustom();
                    break;

                //非标定制
                case 'showunstandardcustom':
                    args.result = true;
                    that.showunstandardcustom();
                    break;
                case 'recreateprice':
                case 'commonchangeprice':
                    if (listId.length === 0) {
                        args.result = true;
                        yiDialog.mt({ msg: '请先选择数据！', skinseq: 4 });
                        return;
                    }
                    break;
                case 'save':
                    var ftype = that.Model.getSimpleValue({ id: "ftype" });
                    if (ftype != "quote_type_04") {
                        that.Model.setValue({ id: 'ftype', value: 'quote_type_04' });
                    }
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'ftype':
                    that.hideOrShowEntryField(e.value.id);
                    break;
                case 'flimit':
                    that.hideOrShowCusEntry(e.value.id);
                    break;
            }
        };

        //根据限定客户显示或隐藏客户明细以及数据列
        _child.prototype.hideOrShowCusEntry = function (type) {
            var that = this;
            var entry = that.Model.getEntryData({ id: 'fcusentry' });
            type = type || that.Model.getSimpleValue({ id: 'flimit' });
            type = $.trim(type);
            if (!type) {
                that.Model.setVisible({ id: '.y-cusentry', value: false });
                for (var i = 0, j = entry.length; i < j; i++) {
                    that.Model.setValue({ id: 'fcustomerid', row: entry[i].id, value: '' });
                    that.Model.setValue({ id: 'fcustype', row: entry[i].id, value: '' });
                }
                return;
            }
            else {
                that.Model.setVisible({ id: '.y-cusentry', value: true });
            }

            var visible = type === 'limit_01';
            if (!visible) {
                that.Model.setVisible({ id: 'fcustomerid', value: false });
                that.Model.setVisible({ id: 'fcustype', value: true });
                for (var i = 0, j = entry.length; i < j; i++) {
                    that.Model.setValue({ id: 'fcustomerid', row: entry[i].id, value: '' });
                }
            }
            else {
                that.Model.setVisible({ id: 'fcustomerid', value: true });
                that.Model.setVisible({ id: 'fcustype', value: false });
                for (var i = 0, j = entry.length; i < j; i++) {
                    that.Model.setValue({ id: 'fcustype', row: entry[i].id, value: '' });
                }
            }

        };

        //根据报价类型显示或隐藏部分列
        _child.prototype.hideOrShowEntryField = function (type) {
            var that = this;
            type = type || that.Model.getSimpleValue({ id: 'ftype' });
            type = $.trim(type);

            if (type&&type!= 'quote_type_04')
            {
                that.Model.setValue({ id: 'ftype', value:'quote_type_04'});
            }

            // 如果【报价类型】为“二级分销报价”时，则显示【限定客户】及适用客户表体，否则隐藏。
            var visible = type === 'quote_type_04';
            if (visible) {
                that.Model.setVisible({ id: '.y-cuslimit', value: true });
            } else {
                that.Model.setValue({ id: 'flimit', value: '' });
                that.Model.setVisible({ id: '.y-cuslimit', value: false });
            }
        };

        return _child;
    })(BillPlugIn);
    window.ydj_reprice = window.ydj_reprice || ydj_reprice;
})();