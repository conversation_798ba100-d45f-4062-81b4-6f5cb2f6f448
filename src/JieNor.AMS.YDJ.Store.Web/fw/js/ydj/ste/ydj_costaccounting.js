///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/ydj/ste/ydj_costaccounting.js
*/
; (function () {
    var ydj_costaccounting = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //在原型上定义所有实例共享成员，以便复用

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        
		_child.prototype.productEntryId = 'fentry';//商品信息
		_child.prototype.costEntryId = 'fcostdetail';//报价明细
		
		
        //方法（对于方法则都在原型上定义）*********************************************************************************************************************************************************************************
        
        //初始化页面插件
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

            that.Model.setVisible({ id: '[target=quote]', value: false });

            that.alertModel = {};//定义弹出框

            //手工切换商品明细
            that.onElementClick({ id: 'switch-1' });
			
			if(that.formContext.status == 'push'){
				var gridData=that.Model.getValue({id:that.productEntryId});
				
				for(var i=0,l=gridData.length; i<l; i++){
					if(gridData[i].fproductid&& !gridData[i].fproductid.id ){
						
						gridData[i].fisself=true;//则是自备料
						gridData[i].fqty=1;//商品明细数量默认为一
					}
				}
				
				//var billAmount = 0;//成交总金额
				//var billsumAmount = 0;//总金额
				//var billsumCost = 0; //总成本即总经销金额
	            
	            //if (gridData && gridData.length > 0) {
	                //for (var i = 0, l = gridData.length; i < l; i++) {
	                    //var fdealamount = yiMath.toNumber(gridData[i].fdealamount);
	                    //var fcost = yiMath.toNumber(gridData[i].fcost);
	                    ////毛利=成交金额-成本
	                    //var fgaint = fdealamount - fcost;
	                    //毛利率=(毛利/成交金额)*100%
	                    //var fgainratio = fdealamount > 0 ? ((fgaint / fdealamount) * 100).toFixed(2) : 0;
	                    //gridData[i].fgain = fgaint;
	                    //gridData[i].fgainratio = fgainratio;
	                    //billAmount += fdealamount;
	                    //billsumAmount += yiMath.toNumber(gridData[i].famount);
	                    //billsumCost += fcost;
	                //}
	            //}
	            //整单折扣系数=成交总金额/总金额
	            //var allPuch=(billAmount/ billsumAmount).toFixed(2);
	            //that.Model.setValue({ id: 'fdiscscale', value: allPuch });

			    //总毛利=成交总金额-总成本 或者 订单总额-总成本 因为成本核算单的订单总额=成交总金额
	            //var billsumGain = billAmount - billsumCost;
			    //总毛利率=(总毛利/成交总金额)*100%
	            //var billsumGainRatio = billAmount > 0 ? ((billsumGain / billAmount) * 100).toFixed(2) : 0;
	            //that.Model.setValue({ id: 'fsumgain', value: billsumGain });
	            //that.Model.setValue({ id: 'fsumgainratio', value: billsumGainRatio });

				
				that.Model.refreshEntry({ id: that.productEntryId });
			
			}
			//初始化的时候，商品明细的计算初始化不用计算。
			that.onBillInitProduct('init');
        };
		
		//报价明细 辅助属性价格查询按钮点击事件
        _child.prototype.onPriceSerch = function (e) {
            var that = this;
            var flag=true;
            that.alertModel=e;
            var productData=[];
            var reAttrinofEntry=[];//按照接口，重新组合 
            var fentry=that.alertModel.Model.uiData.fentity;
            
            for(var n=0,m=fentry.length; n<m; n++){
            	var lm=fentry[n];
            	if(lm.fisselect){//被选中的辅助属性行
            		if(!lm.fvalueid){//辅助属性行需要填满信息才能查询
            			flag=false;
            		}
            		reAttrinofEntry.push({
						valueId:lm.fvalueid,
						auxPropId:lm.fauxpropid.id
					})
            	}
				
			}
            var l=w=t=0;
            if(that.alertModel.formContext.cp.priceField[0].id == 'fprice_sub'){
            	l=that.alertModel.Model.uiData.flength_sub;
            	w=that.alertModel.Model.uiData.fwidth_sub;
            	t=that.alertModel.Model.uiData.fthick_sub;
            }else{
            	l=that.alertModel.Model.uiData.flength;
            	w=that.alertModel.Model.uiData.fwidth;
            	t=that.alertModel.Model.uiData.fthick;
            }
            
            
            var stockStatus = that.Model.getSimpleValue({id:'fstockstatus',row:e.formContext.cp.flexRow});
            productData.push({
				clientId:'',
				productId:that.alertModel.Model.uiData.fmaterialid.id,
				bizDate:that.Model.uiData.fbizdate,
				length:l,
				width:w,
				thick: t,
				customerId: that.Model.uiData.fcustomerid.id,
				innerCustomerId: that.Model.uiData.finnercustomerid.id,
				stockStatus: stockStatus,
				attrInfo:{
					id:'',
					entities:reAttrinofEntry
				}
			});
			
			productData=JSON.stringify(productData);
        	if(flag){
        		that.Model.invokeFormOperation({
	                id: 'onPriceSerch',
	                
	                opcode: 'getprices',
	                //option: cvtParams,
	                param: {
	                    productInfos: productData,
                        priceFlag:7,
	                    formId:'ydj_price',
	                    domainType:'dynamic'
	                }
	            });
        	}else{
        		yiDialog.mt({msg:'请先编辑完您所选择的属性值。', skinseq: 2});
        	}
            
			
        };
		
		//初始化商品明细表的经销价插件
        _child.prototype.onBillInitProduct = function (param,params) {
        	var that=this;
        	var productData=[];
        	if(param=='init' && that.formContext.status=='push'){//初始化，
        		var reGridData = that.Model.getValue({id:that.productEntryId});
        		for(var i=0,l=reGridData.length; i<l ;i++){//零售价，经销价，采购价其一小于等于零的时候，请求
        			var reAttrinofEntry=[];//按照接口，重新组合
        			var tempAttr=reGridData[i].fattrinfo.fentity;
        			for(var n=0,m=tempAttr.length; n<m; n++){
        				reAttrinofEntry.push({
        					valueId:tempAttr[n].fvalueid,
        					auxPropId:tempAttr[n].fauxpropid.id
        				})
        			}
        			if(yiMath.toNumber(reGridData[i].fprice) <=0 || yiMath.toNumber(reGridData[i].fcostprice) <=0 || yiMath.toNumber(reGridData[i].factualcostprice) <=0 ){
        				productData.push({
        					clientId:reGridData[i].id,
        					productId:reGridData[i].fproductid.id,
        					supplierId:reGridData[i].fsupplierid.id,
        					bizDate: that.Model.getValue({id:'fbizdate'}),
        					length:reGridData[i].flength,
        					width:reGridData[i].fwidth,
        					thick: reGridData[i].fthick,
        					innerCustomerId: that.Model.getSimpleValue({ id: 'finnercustomerid' }),
        					customerId: that.Model.getSimpleValue({ id: 'fcustomerid' }),
        					stockStatus: reGridData[i].fstockstatus.id,
        					attrInfo:{
        						id:'',
        						entities:reAttrinofEntry
        					}
        				})
        			}
        		}
        		
        		if(productData.length==0){//初始化的时候，商品都有对应的值，则不查询。
        			return;
        		}
        		
        	}else if(param=='change'){//改变某一行
        		var rowData=that.Model.getEntryRowData({id:that.productEntryId,row:params.attrinfo.row});
                
        		
                //如果商品没值，就不用取价，零售价经销价直接设置为0
                if(params.attrinfo.id=='fproductid' && params.attrinfo.value && !$.trim(params.attrinfo.value.id)){
                    
                    that.Model.setValue({ id: 'factualcostprice', row: rowData.id, value: 0});
                    that.Model.setValue({ id: 'fprice', row:rowData.id, value: 0});
                    that.Model.setValue({ id: 'fcostprice', row: rowData.id, value: 0});
                    return;
                }
                //如果商品没值，所携带的辅助属性肯定也为空，零售价经销价直接设置为0
                if(params.attrinfo.id=='fattrinfo' &&rowData.fproductid && !$.trim(rowData.fproductid.id)){
                    that.Model.setValue({ id: 'factualcostprice', row: rowData.id, value: 0});
                    that.Model.setValue({ id: 'fprice', row:rowData.id, value: 0});
                    that.Model.setValue({ id: 'fcostprice', row: rowData.id, value: 0});
                    return;
                }
                

        		var reAttrinofEntry=[];//按照接口，重新组合
        		var tempAttr=[];
        		if(rowData.fattrinfo && rowData.fattrinfo.fentity && rowData.fattrinfo.fentity.length>0){
        			tempAttr= rowData.fattrinfo.fentity;
        		}
        		
        		for(var n=0,m=tempAttr.length; n<m; n++){
    				reAttrinofEntry.push({
    					valueId:tempAttr[n].fvalueid,
    					auxPropId:tempAttr[n].fauxpropid.id
    				})
    			}
        		
				productData.push({
					clientId:rowData.id,
					productId:rowData.fproductid.id,
                    supplierId:rowData.fsupplierid.id,
					bizDate:that.Model.getValue({id:'fbizdate'}),
					length:rowData.flength,
					width:rowData.fwidth,
					thick: rowData.fthick,
					innerCustomerId: that.Model.getSimpleValue({ id: 'finnercustomerid' }),
					customerId: that.Model.getSimpleValue({ id: 'fcustomerid' }),
					stockStatus: rowData.fstockstatus.id,
					attrInfo:{
						id:'',
						entities:reAttrinofEntry
					}
				});
    			
        	}else if(param=='costChange'){//报价明细值变化
        		var rowData=that.Model.getEntryRowData({id:that.costEntryId,row:params.attrinfo.row});
        		if(rowData){
					//长
		            var length = yiMath.toNumber(rowData.flength_sub);
					
		            //宽
		            var width = yiMath.toNumber(rowData.fwidth_sub);
		            //面积 =（长 / 1000）*（宽 / 1000）
		            var area = (length / 1000) * (width / 1000);
		            
		            area = area > 0 ? area : 1;
		            //计算面积
		            that.Model.setValue({ id: 'farea_sub', row: params.attrinfo.row, value: area });

		            
				}
        		
        		var reAttrinofEntry=[];//按照接口，重新组合
        		var tempAttr=[];
        		if(rowData.fattrinfo_sub){
        			tempAttr= rowData.fattrinfo_sub.fentity;
        		}
        		
        		for(var n=0,m=tempAttr.length; n<m; n++){
    				reAttrinofEntry.push({
    					valueId:tempAttr[n].fvalueid,
    					auxPropId:tempAttr[n].fauxpropid.id
    				})
    			}
        		
				productData.push({
					clientId:rowData.id,
					productId:rowData.fproductid_sub.id,
					bizDate:that.Model.getValue({id:'fbizdate'}),
					length:rowData.flength_sub,
					width:rowData.fwidth_sub,
					thick: rowData.fthick_sub,
					innerCustomerId: that.Model.getSimpleValue({ id: 'finnercustomerid' }),
					customerId: that.Model.getSimpleValue({ id: 'fcustomerid' }),
					stockStatus: rowData.fstockstatus_sub.id,
					attrInfo:{
						id:'',
						entities:reAttrinofEntry
					}
				});
        		
        	}
        	
        	if(param=='init' && that.formContext.status!='push'){
        		return;
        	}
        	
        	
        	productData=JSON.stringify(productData);
        	
            that.Model.invokeFormOperation({
                id: param,
                
                opcode: 'getprices',
                //option: cvtParams,
                param: {
                    productInfos: productData,
                    priceFlag:7,
                    formId:'ydj_price',
                    domainType:'dynamic'
                }
            });
        }
        
        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            
            switch (e.opcode) {
                
                    //匹配商品
                case 'matchproduct':
                    if (isSuccess && $.isPlainObject(srvData)) {
                        that.Model.setValue({ id: 'fproductid_sub', row: optData.rowId, value: srvData.fproductid });
                        that.Model.setValue({ id: 'funitid_sub', row: optData.rowId, value: srvData.funitid });
                    } else {
                        that.Model.setValue({ id: 'fmatchresult_sub', row: optData.rowId, value: '无关联商品' });
                        that.Model.setValue({ id: 'fproductid_sub', row: optData.rowId, value: '' });
                        that.Model.setValue({ id: 'funitid_sub', row: optData.rowId, value: '' });
                    }
                    break;
                case 'getprices':
                    
                	if(e.id =='init' ||e.id =='change'){
                		
	                	if(!srvData){
	                		yiDialog.mt({msg:' 无相关查询数据', skinseq: 2});
	                		return;
	                	}
	                	
	                	var str='';
	                	var gridData=that.Model.getValue({id:that.productEntryId});
	                	
	                	for(var i=0,l=srvData.length; i<l ;i++){
		                		var lm=srvData[i];
		                		var rowData={};
		                		var num =0;
		                		for(var n=0,m=gridData.length; n<m ;n++){
		                			if(gridData[n].id == lm.clientId){//获取对于行
		                				rowData=gridData[n];
		                				num=n+1;
		                			}
		                		}
		                		if(lm.success){//价格匹配成功，则赋值
		                			//当count大于1时，提示用户" 第{client}行，{productName},有{count}个项匹配，请谨慎使用匹配的数据" 价目表上获取不到商品名的，前端获取。
		                			//	最匹配的采购价赋值
		                			that.Model.setValue({ id: 'factualcostprice', row: lm.clientId, value: lm.purPrice });
		                			//	最匹配的销售价(零售价)赋值  ，非自备料，即使查到了零售价数据，也不赋值
		                			if(rowData.fisself){
		                				that.Model.setValue({ id: 'fprice', row: lm.clientId, value: lm.salPrice });
		                			}
		                			
		                			//	最匹配的经销价赋值
		                			that.Model.setValue({ id: 'fcostprice', row: lm.clientId, value: lm.definedPrice });
		                			
									if(lm.count>1){
										var theName=rowData.fproductid.fname;
										str+='第{0}行，{1},有{2}个项匹配 <br/>'.format(num,theName,lm.count);
									}
		                		}else{//价格匹配不成功
		                			//yiDialog.mt({msg:'价格匹配不成功', skinseq: 2});
		                			if(e.id =='change'){//查不到数据，就赋值为0
		                				that.Model.setValue({ id: 'factualcostprice', row: lm.clientId, value: 0});
		                				that.Model.setValue({ id: 'fprice', row: lm.clientId, value: 0});
		                				that.Model.setValue({ id: 'fcostprice', row: lm.clientId, value: 0});
		                				str+='第{0}行数据无相关查询数据<br/>'.format(num,theName,lm.count);
		                			}
		                		}
	                	}
	                	if(str.length>0){
	                		yiDialog.mt({msg:str, skinseq: 2});
	                		
	                	}
                	}else if(e.id =='onPriceSerch'){
                		
            			if(srvData && srvData[0] &&that.alertModel.Model){
            				
            				that.alertModel.Model.setValue({id:that.alertModel.formContext.cp.priceField[0].id,value:srvData[0].salPrice});
            			}
            			
                	}else if(e.id =='match' ||e.id =='costChange'){
                		if(!srvData){//容错。
                            return;
                        }
                        that.Model.setValue({ id: 'fmatchresult_sub', row: srvData[0].clientId, value: srvData[0].success > 0 ? '取价成功' : '无关联价格' });
                        if(srvData[0].success){
                        	
	                        that.Model.setValue({ id: 'fprice_sub', row: srvData[0].clientId, value: srvData[0].salPrice });
	                        that.Model.setValue({ id: 'fcostprice_sub', row: srvData[0].clientId, value: srvData[0].definedPrice });
                        }else{
                        	that.Model.setValue({ id: 'fprice_sub', row: srvData[0].clientId, value: 0 });
	                        that.Model.setValue({ id: 'fcostprice_sub', row: srvData[0].clientId, value: 0 });
                        }
	                    
                	}
                	
                	break;

                case 'refreshentities':
                    that.Model.refresh();
                    break;
            }
        };
		
		//元素点击事件
        _child.prototype.onElementClick = function (e) {//页签切换效果。
            var that = this;
            var opcode = e.id.toLowerCase();
            switch (e.id.toLowerCase()) {
                case 'product':
                    that.Model.removeClass({ id: '[optype=quote]', value: 'active' });
                    that.Model.addClass({ id: '[optype=product]', value: 'active' });

                    that.Model.setVisible({ id: '[target=quote]', value: false });
                    that.Model.setVisible({ id: '[target=product]', value: true });

                    that.Model.resizeEntryWidth({ id: 'fentry' });
                    break;
                case 'quote':

                    that.Model.removeClass({ id: '[optype=product]', value: 'active' });
                    that.Model.addClass({ id: '[optype=quote]', value: 'active' });

                    that.Model.setVisible({ id: '[target=product]', value: false });
                    that.Model.setVisible({ id: '[target=quote]', value: true });

                    that.Model.resizeEntryWidth({ id: 'fcostdetail' });
                    break;
            };
        };

		
        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.productEntryId:
                    e.result = { multiselect: false, rownumbers: false};
                    break;
                case this.costEntryId:
                    e.result = { multiselect: false, rownumbers: false, treeGrid: true };
                    break;    
                    
            }
        };
        
        //创建明细表格
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.row) return;
            //自备料（从合同带下来的默认为false，手工新增的默认为true，checkbox）,合同带过来的不允许被删除
            var that=this;
            var data= this.Model.getValue({id:'fentry'});
            if(e.id == 'fentry'){
            	
	            for(var i=0,l=data.length; i<l; i++){
	            	if(data[i].id==e.row){
	            		var rowData=data[i];
	            		if(rowData && !rowData.fisself){
	            			
	            			
	            			yiDialog.mt({ msg: '合同携带过来的商品不允许被删除', skinseq: 2 });
	            			e.result=true;
	            		}else{
	            			//获取报价明细数据
	            			var reGridData = that.Model.getValue({id:that.costEntryId});
	            			for(var n=0,m=reGridData.length; n<m; n++){
	            				if(reGridData[n]&& reGridData[n].fproductisn==e.row){//如果商品内码id==报价明细主行，则删除这行报价明细
	            					that.Model.deleteRow({id:that.costEntryId,row:reGridData[n].id})
	            				}
	            			}
		                	
	            			
	            		}
	            		
	            		
	            	}
	            }
            }
        };
        
        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            var productId = '';
            var eId = e.id.toLowerCase();

            if(e.value){
                if(typeof e.value.fparentid =='undefined'){
                    
                }
                else{
                    //如果当前行是父产品行，则当前行所有字段不可以编辑
                    if (!e.value || !$.trim(e.value.fparentid)) {
                        if (eId != "fprice_sub" && eId != "fcostrate_sub") {
                            e.result.enabled = false;
                        }
                        return;
                    }
                }
            }
            
            switch (eId) {
            	
            	case 'fproductid'://商品编码
            	//case 'fprice'://单价
            	case 'fqty'://数量
            	case 'fdealprice'://成交价
            	case 'fdealamount'://成交金额
            	case 'fattrinfo'://辅助属性
                case 'funitid'://单位
                case 'fbizqty':
                case 'fbizunitid':
            		//当商品不是自备料的时候，商品明细表自备料为false，商品明细表只有成本价可以放开  则要求锁定商品编码，单位，数量，成交价，成交金额
                    ieself = $.trim(that.Model.getSimpleValue({ id: 'fisself', row: e.row }));
                    
                    if (ieself=='false') {
                    	
                        e.result.enabled = false;
                        return;
                    }
                    if(eId == 'fattrinfo'){
                        //如果默认辅助属性为空则不允许编辑
                        if(e.value.fattrinfo.fentity && e.value.fattrinfo.fentity.length == 0){
                            e.result.enabled = false;
                            return;
                        }
                    }
                    break;
                case 'fattrinfo_sub'://辅助属性
                    //如果默认辅助属性为空则不允许编辑
                    if(e.value.fattrinfo_sub.fentity && e.value.fattrinfo_sub.fentity.length == 0){
                        e.result.enabled = false;
                        return;
                    }
                    break;
                case 'fproductid_sub':
                    e.result.enabled = that.Model.getValue({ id: 'fisadjust_sub', row: e.row });
                    break;
                case 'fproductname_sub':
                    e.result.enabled = !(that.Model.getValue({ id: 'fisadjust_sub', row: e.row }));
                    break;
            }
        };
        
        //辅助属性编辑页面字段值改变事件
        _child.prototype.onFlexFieldValueChanged = function (e) {
            var that = this;
            
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    

                	that.onBillInitProduct('change',{attrinfo:e});
                    break;
                case 'fattrinfo_sub':
                    that.Model.setEnable({id:'fattrinfo_sub',value:(
                        e.value.fentity.length == 0 ? false : true
                    ),row:e.row})
                	that.onBillInitProduct('costChange',{attrinfo:e});
                    break;
            }
        };
        
		//字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fstockstatus':
                var _productid=that.Model.getValue({id:'fproductid',row:e.row});
                    if(_productid && $.trim(_productid.id)){
                        that.onBillInitProduct('change',{attrinfo:e});
                    }
                
                
                break;
            	case 'fproductid':
            	case 'fsupplierid':
            		that.onBillInitProduct('change',{attrinfo:e});
            		break;
            	
                //数量单价的变化，影响金额
                case 'fqty':
                //case 'fdealprice': //成交价不可编辑并且不可改变
                case 'fprice':
                case 'factualcostprice':
                case 'fcostprice':
                
                	//金额和总金额
                    that.calculateEntry({ name: e.id, rowId: e.row, value: e.value });
                    //数量变化一次也要算表头金额
                    //that.calculateSumFieldValue();//calculateEntry方法已调用汇总方法
                    break;
                //成本核算单的成交总金额不可编辑并且不可更改，无需判断它是否改变
                //case 'fdealamount'://成交总金额
                //	that.caldealSum();
                //	break;
                //case 'fcostprice': //已放置上面，无需单独处理
                //    //商品明细改变时：重新计算明细
                //    that.productEntryChange({ name: e.id, rowId: e.row, value: e.value });
                //    break;
                //报价明细 自动匹配商品价格
                case 'fproductid_sub':
                case 'fstockstatus_sub':
                case 'flength_sub':
                case 'fwidth_sub':
                case 'fthick_sub':
                
                	var row = that.Model.getEntryRowData({ id: that.costEntryId, row: e.row });
                	
                	if(row.fproductid_sub && row.fproductid_sub.id){
                		that.onBillInitProduct('costChange',{attrinfo:e});
                	}
                    
                    break;
                    //修改物料名称时，自动匹配商品
                case 'fproductname_sub':
                    that.matchProduct(e);
                    break;
                    //根据是否手工调整来锁定相关字段
                case 'fisadjust_sub':
                    that.Model.setEnable({ id: 'fproductid_sub', value: e.value, row: e.row });
                    that.Model.setEnable({ id: 'fproductname_sub', value: !e.value, row: e.row });
                    break;
                //case 'farea_sub':
                //case "fnsc_sub":
                case 'fqty_sub':
                case 'fcostprice_sub':
                case 'fdistrate_sub':
                case 'fdistamount_sub':
                    //计算
                    that.costEntryIdChange({ name: e.id, rowId: e.row, value: e.value });
                    break;
                case 'famount_sub':
                    that.sumCostAmount("famount_sub", "faccountamount");
                    break;
                case "fcostamount_sub":
                    that.sumCostAmount("fcostamount_sub", "fsumcost");
                    break;
               	
            }
        };

        //汇总报价明细行的金额至表头，例如核算零售金额,经销商货款
        _child.prototype.sumCostAmount = function (currentFieldId, headFieldId) {
            var that = this;
            var gridData = that.Model.getValue({ id: that.costEntryId });
            //如果报价明细没有明细行设置表头金额为0
            if (!gridData || gridData.length <= 0) {
                that.Model.setValue({ id: headFieldId, value: 0 });
                return;
            }
            [].filter
            //报价明细顶级父行
            var parentDatas = gridData.map(function (d, i) { return { "row": i, "data": d }; }).filter(function (d) { return d.data.fparentid == ""; });
            //表头金额
            var sumAmount = 0;
            //汇总子行的金额并设置父行的金额
            for (var i = 0, l = parentDatas.length; i < l; i++) {
                var parentData = parentDatas[i];
                var parentId = parentData.data.id;
                var parentRow = parentData.row;
                var childrenDatas = $.trim(parentId).length == 0 ? [] : gridData.filter(function (d) { return d.fparentid == parentId });
                //没有子行时，不必修改父行的零售金额为0
                if (!childrenDatas || childrenDatas.length <= 0) {
                    sumAmount += parentData.data[currentFieldId];
                    continue;
                }
                var parentAmount = 0;
                for (var j = 0, ll = childrenDatas.length; j < ll; j++) {
                    parentAmount += childrenDatas[j][currentFieldId];
                }
                that.Model.setValue({ "id": currentFieldId, "row": parentRow, "value": parentAmount });
                sumAmount += parentAmount;
            }
            //设置表头金额
            that.Model.setValue({ "id": headFieldId, "value": sumAmount });
        }
        
        //报价明细 根据物料名称后端匹配商品
        _child.prototype.matchProduct = function (e) {
            var that = this;
            var productName = $.trim(e.value);
            if (productName) {
                that.Model.invokeFormOperation({
                    id: 'MatchProduct',
                    opcode: 'MatchProduct',
                    param: {
                        formId: 'ydj_product',
                        domainType: Consts.domainType.bill,
                        rowId: e.row,
                        productName: productName
                    }
                });
            } else {
                that.Model.setValue({ id: 'fmatchresult_sub', row: e.row, value: '' });
                that.Model.setValue({ id: 'fproductid_sub', row: e.row, value: '' });
                that.Model.setValue({ id: 'funitid_sub', row: e.row, value: '' });
            }
        };
        
        
        
        //表格控件里新增行按钮的显示与隐藏
        _child.prototype.onHideAddButton = function (e) {
            if (!e || !e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.costEntryId:
                    //如果是主物料行，则隐藏添加按钮
                    if (!e.prow) {
                        e.result = true;
                    }
                    break;
            }
        };
        
        //表格控件里删除行按钮的显示与隐藏
        _child.prototype.onHideDeleteButton = function (e) {
            if (!e || !e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.costEntryId:
                    //如果是主物料行，则隐藏删除按钮
                    if (!e.prow) {
                        e.result = true;
                    }
                    break;
            }
        };
        
        //报价明细 设值前事件
        _child.prototype.onBeforeSetValue = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fnsc':
                    //非标系数，如果不是空字符串，才处理，因为可能是手动故意清空的
                    if ($.trim(e.value)) {
                        var nsc = yiMath.toNumber(e.value);
                        if (nsc <= 0 || nsc > 10) {
                            e.value = 1;
                            e.result = true;
                        }
                    }
                    break;
            }
        };
        
        //报价明细 辅助属性编辑或查看页面渲染前事件
        _child.prototype.onFlexViewRendering = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fattrinfo':
                    var rowData = that.Model.getEntryRowData({ id: that.costEntryId, row: e.flexRow });
                    if (rowData) {
                        var areaDef = rowData.farea || '';
                        e.result = {
                            fields: [
                               { id: 'farea', caption: '面积', defval: areaDef, backfill: true, suffix: '㎡' }
                            ]
                        };
                    }
                    break;
            }
        };
        
        
        
        //报价明细 商品明细改变时：重新计算明细
        _child.prototype.costEntryIdChange = function (opt) {
            var that = this;

            //行对象
            var row = that.Model.getEntryRowData({ id: that.costEntryId, row: opt.rowId });
			
//          //长
//          var length = yiMath.toNumber(row.flength_sub);
//			
//          //宽
//          var width = yiMath.toNumber(row.fwidth_sub);
//          //面积 =（长 / 1000）*（宽 / 1000）
//          var area = (length / 1000) * (width / 1000);
//          //如果手动输入了面积，则以手动输入的为准
//          if (opt.name === 'farea_sub') {
//              area = yiMath.toNumber(row.farea_sub);
//          }
//          area = area > 0 ? area : 1;

            //非标系数
            var nsc = yiMath.toNumber(row.fnsc_sub); nsc = nsc > 0 ? nsc : 1;
			
            
			
            //数量
            var qty = yiMath.toNumber(row.fqty_sub);

            //零售价
            var price = yiMath.toNumber(row.fprice_sub);
			//经销价
			var costprice = yiMath.toNumber(row.fcostprice_sub);
			
            //表体经销金额 = 表体非标系数 * 表体数量 * 表体经销价
            var amount = nsc * qty * costprice;

            //折扣额
            var distAmount = yiMath.toNumber(row.fdistamount_sub);

            //折扣率
            var distRate = yiMath.toNumber(row.fdistrate_sub);

            //如果当前修改的是“折扣额”，则自动反算“折扣率”
            if (opt.name === 'fdistamount_sub') {
                //折扣率 =（表体折扣额 - 表体金额）/（-1 * 表体金额）
                if ((-1 * amount) !== 0) {
                    distRate = (distAmount - amount) / (-1 * amount);
                    distRate *= that.defdis;
                }
            }
            distRate = distRate > 0 ? distRate : that.defdis;

            //如果当前修改的不是“折扣额”，则根据折扣率计算“折扣额”，否则以用户输入的“折扣额”为准
            if (opt.name !== 'fdistamount_sub') {
                //表体折扣额 = 表体金额 -（(表体金额 * 表体折扣率）/ 10)
                distAmount = amount - ((amount * distRate) / that.defdis);
            }
            //表体成交单价默认等于单价
            var dealPrice = price;

            //表体成交单价 =（表体金额 - 表体折扣额）/ 表体数量
            if (qty !== 0) {
                dealPrice = (amount - distAmount) / qty;
            }

            //表体成交金额 = 表体数量 * 表体成交单价
            var dealAmount = qty * dealPrice;

            dealPrice = isNaN(dealPrice) ? 0 : dealPrice;
            dealAmount = isNaN(dealAmount) ? 0 : dealAmount;

            //更新字段值
//          that.Model.setValue({ id: 'farea_sub', row: opt.rowId, value: area });
//          that.Model.setValue({ id: 'fnsc_sub', row: opt.rowId, value: nsc });
//          that.Model.setValue({ id: 'fdistrate_sub', row: opt.rowId, value: yiMath.toDecimal(distRate, 2) });
//          that.Model.setValue({ id: 'fdistamount_sub', row: opt.rowId, value: distAmount });
            
            that.Model.setValue({ id: 'famount_sub', row: opt.rowId, value: amount });
            
            
            that.Model.setValue({ id: 'fdealprice_sub', row: opt.rowId, value: dealPrice });
            that.Model.setValue({ id: 'fdealamount_sub', row: opt.rowId, value: dealAmount });

        };
        
        //成交总金额计算汇总
        //_child.prototype.caldealSum = function () {
        //    var that = this;
        //    var ds = that.Model.getEntryData({ id: that.productEntryId });
        //    var billAmount = 0;//成交总金额
        //    var billsumAmount=0;//总金额
            
        //    if (ds && ds.length > 0) {
        //        for (var i = 0, l = ds.length; i < l; i++) {
        //            billAmount += yiMath.toNumber(ds[i].fdealamount);
        //            billsumAmount += yiMath.toNumber(ds[i].famount);
        //        }
        //    }
        //    //整单折扣系数=成交总金额/总金额
        //    var allPuch=(billAmount/ billsumAmount).toFixed(2);
        //    that.Model.setValue({ id: 'fdiscscale', value: allPuch });
            
        //    //更新计算结果,赋值订单金额
        //    that.Model.setValue({ id: 'fdealsumamount', value: billAmount });
        //};
        
        //计算明细
        _child.prototype.calculateEntry = function (opt) {
            var that = this;
            //行对象
            var row = that.Model.getEntryRowData({ id: that.productEntryId, row: opt.rowId });
            
            if (!row) return;
            //数量
            var qty = yiMath.toNumber(row.fqty);
            //成本价
            var costprice = yiMath.toNumber(row.fcostprice);
            //成交价
            var dealprice = yiMath.toNumber(row.fdealprice);
            //单价
            var price = yiMath.toNumber(row.fprice);
            //表体金额 = 表体数量 * 表体单价
            var amount = qty * price;
            //成本 = 表体数量 * 表体成本价
            var cost = qty * costprice;
            //成交金额=成交价*数量
            var dealmount=dealprice*qty;
            //采购价和采购金额的关系是，采购金额=采购价*数量
            
            var getAmount = yiMath.toNumber(row.factualcostprice) * qty;

            //毛利=成交金额-成本
            var gain = dealmount - cost;
            //毛利率=(毛利/成交金额)*100%
            var gainRadio = dealmount > 0 ? ((gain / dealmount) * 100).toFixed(2) : 0;

            //金额设值
            
            that.Model.setValue({ id: 'famount', row: opt.rowId, value: amount });
            //成本设值
            that.Model.setValue({ id: 'fcost', row: opt.rowId, value: cost });
            //采购金额设值
            that.Model.setValue({ id: 'factualcostamount', row: opt.rowId, value: getAmount });
            //成交金额=成交价*数量
            //fdealamount=fdealprice*fqty
            //成交金额
            that.Model.setValue({ id: 'fdealamount', row: opt.rowId, value: dealmount });

            //设置毛利
            that.Model.setValue({ id: 'fgain', row: opt.rowId, value: gain });
            //设置毛利率
            that.Model.setValue({ id: "fgainratio", row: opt.rowId, value: gainRadio });

            //计算汇总
            that.calculateSum();
        };

        //计算汇总
        _child.prototype.calculateSum = function () {
            var that = this;
            var ds = that.Model.getEntryData({ id: that.productEntryId });
            //var billAmount = 0;//总金额
            //var setAmount=0;//成交总金额
            var getNumAmount = 0;//采购总金额
            var sumCost = 0; //总成本或经销总金额
            if (ds && ds.length > 0) {
                for (var i = 0, l = ds.length; i < l; i++) {
                    //billAmount += yiMath.toNumber(ds[i].famount);
                   // setAmount+=yiMath.toNumber(ds[i].fdealamount);
                    getNumAmount += yiMath.toNumber(ds[i].factualcostamount);
                    sumCost += yiMath.toNumber(ds[i].fcost);
                }
            }
			//整单折扣系数=成交总金额/总金额
            //var allPuch = (setAmount / billAmount).toFixed(2);
            
            //总毛利=成交总金额-总成本
            //var sumGain = setAmount - sumCost;
            //总毛利率=(总毛利/成交金额)*100%
            //var sumGainRatio = setAmount > 0 ? ((sumGain / setAmount) * 100).toFixed(2) : 0;

            //更新整单折扣系数
            //that.Model.setValue({ id: 'fdiscscale', value: allPuch });
            //更新计算结果,赋值订单金额
            //that.Model.setValue({ id: 'fsumamount', value: billAmount });
            //更新计算结果,采购总金额
            that.Model.setValue({ id: 'fsumactualcostamount', value: getNumAmount });
            //更新成交总金额
            //that.Model.setValue({ id: "fdealsumamount", value: setAmount });
            //更新总成本或经销总金额
            that.Model.setValue({ id: "fsumcost", value: sumCost });
            //更新总毛利
            //that.Model.setValue({ id: "fsumgain", value: sumGain });
            //更新总毛利率
            //that.Model.setValue({ id: "fsumgainratio", value: sumGainRatio });
        };
        

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.productEntryId:
                	
                    //计算表头字段值
                    that.calculateSumFieldValue();
                    break;
            }
        };
        //表格行创建后事件
        _child.prototype.onEntryRowCreated = function (e) {
            var that = this;
            
            //新增行，自备料字段为true
            switch (e.id.toLowerCase()) {
                case that.productEntryId:
                	
                    //新增商品自备料为选中
                    that.Model.setValue({ id: 'fisself', row: e.row, value: true });
                    //新增商品数量默认为一
                    that.Model.setValue({ id: 'fqty', row: e.row, value: 1 });
                    
                    break;
                case that.costEntryId:
                	
                   
                    break;
            }
        };
        

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                //根据成本来源获取成本价
                case 'getcost':
                    e.result = true;
                    //that.getProductCost();
                    break;
                    //取消
                case 'costcancel':
                    e.result = true;
                    that.Model.close();
                    break;
                    //确定
                case 'costconfirm':
                    e.result = true;

                    //克隆一份当前页面的数据包
                    var costData = that.Model.clone();

                    //设置对话框的返回数据
                    that.Model.setReturnData({ costData: costData });

                    //关闭对话框
                    that.Model.close();
                    break;
            }
        };

        //根据成本来源获取成本价
        _child.prototype.getProductCost = function () {
            var that = this;

            //根据 成本来源 和 商品 去后端动态成本价......以下是测试数据
            var costs = [
                { fproductid: '', fcostprice: 1000 },
                { fproductid: '', fcostprice: 2000 }
            ];

            //商品明细数据源
            var ds = that.Model.getEntryData({ id: that.productEntryId });
            if (ds && costs) {
                for (var i = 0; i < ds.length; i++) {
                    for (var j = 0; j < costs.length; j++) {
                        if ($.trim(ds[i].fproductid.id) === $.trim(costs[j].fproductid)) {
                            ds[i].fcostprice = costs[j].fcostprice;
                            break;
                        }
                    }
                }
                //重新计算所有明细
                that.recalculateEntry();
            }
        };

        //重新计算所有明细
        _child.prototype.recalculateEntry = function () {
            var that = this,
                ds = that.Model.getEntryData({ id: that.productEntryId });
            if (ds || ds.length > 0) {

                for (var i = 0, l = ds.length; i < l; i++) {

                    var qty = yiMath.toNumber(ds[i].fqty),
                    dealAmount = yiMath.toNumber(ds[i].fdealamount),
                    costPrice = yiMath.toNumber(ds[i].fcostprice);

                    //成本 = 成本单价 * 数量
                    var cost = qty * costPrice;

                    //盈利 = 售价 - 成本
                    var gain = dealAmount - cost;

                    //更新（成本，盈利）
                    ds[i].fcost = cost;
                    ds[i].fgain = gain;
                }

                //刷新明细表格
                that.Model.refreshEntry({ id: that.productEntryId });

                //计算表头字段值
                that.calculateSumFieldValue();
            }
        };

        //商品明细改变时：重新计算明细
        _child.prototype.productEntryChange = function (opt) {
            var that = this;
            
			
            //行对象
            var row = that.Model.getEntryRowData({ id: that.productEntryId, row: opt.rowId });
            if(!row){
            	return;
            }
			var ds = that.Model.getEntryData({ id: that.productEntryId });
			
            //数量
            var qty = yiMath.toNumber(row.fqty);

            //成交金额
            var dealAmount = yiMath.toNumber(row.fdealamount);

            //成本单价
            var costPrice = yiMath.toNumber(row.fcostprice);

            //成本 = 成本单价 * 数量
            var cost = qty * costPrice;

            //盈利 = 成交金额 - 成本
            var gain = dealAmount - cost;

            //更新字段值
            that.Model.setValue({ id: 'fcost', row: opt.rowId, value: cost });
            that.Model.setValue({ id: 'fgain', row: opt.rowId, value: gain });
            
            
            //计算表头字段值
            that.calculateSumFieldValue();
        };

        //计算表头字段值
        _child.prototype.calculateSumFieldValue = function () {
            var that = this,
            	costNum=0,
                ds = that.Model.getEntryData({ id: that.productEntryId }),
                //商品成本 = 商品明细成本单价汇总
                productCost = 0;

            if (ds || ds.length > 0) {
                for (var i = 0, l = ds.length; i < l; i++) {
                    productCost += yiMath.toNumber(ds[i].fcostprice);
                    costNum += yiMath.toNumber(ds[i].fcost);
                }
            }

            //订单总额
            //var sumAmount = yiMath.toNumber(that.Model.getValue({ id: 'fsumamount' })),

            //费用支出
            expenDiture = yiMath.toNumber(that.Model.getValue({ id: 'fexpenditure' })),

            //总成本 = 费用支出 + 商品成本
            sumCost = expenDiture + productCost,

            //毛利 = 订单总额 - 总成本
            //profit = sumAmount - sumCost;

            //更新字段值
            that.Model.setValue({ id: 'fproductcost', value: yiMath.toDecimal(productCost, 2) });
            that.Model.setValue({ id: 'fsumcost', value: yiMath.toDecimal(costNum, 2) });
            //that.Model.setValue({ id: 'fprofit', value: yiMath.toDecimal(profit, 2) });
        };

        return _child;
    })(BasePlugIn);
    window.ydj_costaccounting = window.ydj_costaccounting || ydj_costaccounting;
})();