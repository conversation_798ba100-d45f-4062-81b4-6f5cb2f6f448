/*
 * description:销售机会业务控制插件
 * author:
 * create date:
 * modify by: linus.
 * modify date:
 * remark:
 *@ sourceURL=/fw/js/ydj/ste/ydj_customerrecord.js
*/
; (function () {
    var ydj_customerrecord = (function (_super) {
        var _child = function (args) {
            var that = this;
            that.oldValue = '';
            _super.call(that, args);

        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.dutyEntryId = 'fdutyentry';
        _child.prototype.isNew = false;     // 新增、下推
        _child.prototype.mustSaleCategory = false;        // 意向品类是否必录

        //表单头部菜单渲染前事件
        _child.prototype.beforeCreateTopMenu = function (e) {
            if (!e || !e.menus || e.menus.length <= 0) return;

            var that = this;
            //如果是弹框出来的客户列表则隐藏公海客户菜单按钮
            if (that.formContext.openStyle == 'modal') {
                var commoncusMenu = e.menus.filter(x => x["id"] == 'tbCommonCus');
                if (commoncusMenu && commoncusMenu.length > 0) {
                    commoncusMenu[0].visible = false;
                }
            }
        };

        _child.prototype.onBillInitialized = function (args) {
            var that = this;
            //在新增界面，生成客户按钮和任务指派不显示
            if (!that.Model.pkid) {
                that.Model.setVisible({ id: '[menu=TaskAssign]', way: 3, value: false });
            }

            that.isNew = that.formContext.status === 'new' || that.formContext.status === 'push';

            //如果是新增，下推，则需要自动将下推过来的导购员加到导购员明细表格中
            if (that.isNew) {
                that.procStaff();
            }

            //显示隐藏联合开单按钮
            that.procSaleManOp();
            //根据部门设置默认省市区(只有新增商机才设置)
            var pkid = that.Model.pkid;
            if (!pkid) that.setArea();
            that.setSaleCategoryComboData();
            that.SetImageInfoVisible();
            that.setBillTypeVal();
        };


        //重新设置客户来源
        _child.prototype.setBillTypeVal = function () {
            debugger
            var that = this;

            var fworkwxuserid = that.Model.getValue({ 'id': 'fworkwxuserid' });
  
            if (fworkwxuserid != "") {
                $("#fworkwxuserid").val('已设置');
            }

            //保存下拉值
            var para = [];
            //用于过滤重复数据
            var paraIndex = [];
            var fsourceid = that.Model.getValue({ id: 'fcustomersource' })
            //记录选中值
            var oldfid = that.Model.getValue({ id: 'fcustomersource' }).id;

            var data = $("select[name='fcustomersource']").find("option");
            //过滤总部下发选择
            if (fsourceid.fname != '总部下发') {
                $.each(data, function (i, item) {
                    if (item.text != '总部下发'
                        && paraIndex.indexOf(item.value) == -1
                    ) {
                        para.push({ id: item.value, name: item.text });
                        paraIndex.push(item.value);
                    }
                });

                that.Model.setComboData({ id: 'fcustomersource', data: para });
                //设置回选中值
                that.Model.setValue({ id: 'fcustomersource', value: oldfid });
            } else {
                that.Model.setEnable({ id: 'fcustomersource', value: false });
            }
        }

        _child.prototype.SetImageInfoVisible = function (e) {
            //debugger;
            var that = this;
            var fimage = that.Model.getValue({ id: 'fimage' });
            var hasimage = false;
            if (fimage && fimage.id.length > 0) hasimage = true;
            //如果有图片，则默认展开
            setTimeout(function () {
                debugger;
                that.Model.setAttr({ id: '.y_cusrec_tools', random: 'class', value: hasimage ? 'y_cusrec_tools collapse' : 'y_cusrec_tools expand' });
                that.Model.setAttr({ id: '.y_cusrec_portlet', random: 'style', value: hasimage ? 'display:block' : 'display:none' });
            }, 10);
        }

        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                //负责人
                case 'fdutyid':
                    var deptId = that.Model.getSimpleValue({ id: 'fdeptid' });
                    e.result.filterString = "fdeptid<>'' and fdeptid=@fdeptid";
                    e.result.params = [
                        { fieldId: 'fdeptid', pValue: deptId }
                    ];
                    break;
            }
        };

        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fdutyid_e':
                    if (e.value.fismain) {
                        e.result.enabled = false;
                    }
                    break;
            }
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.dutyEntryId:
                    e.result = { multiselect: false, rownumbers: false };
                    break;
            }
        }

        //表格行删除前事件，设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            if (!e.id) return;
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.dutyEntryId:
                    var isMain = that.Model.getValue({ id: 'fismain', row: e.row });
                    if (isMain) {
                        e.result = true;
                        yiDialog.mt({ msg: '主要销售员不允许删除！', skinseq: 2 });
                    }
                    break;
            }
        }

        //表格明细行删除后
        _child.prototype.onEntryRowDeleted = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case that.dutyEntryId:
                    //导购员信息
                    that.procSaleManOp();
                    break;
            }
        }

        //表格行创建前事件
        _child.prototype.onEntryRowCreating = function (e) {
            //debugger;
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fdutyentry':
                    //that.Model.invokeFormOperation({
                    //    id: 'tbSaleMember',
                    //    opcode: 'validateduty',
                    //    param: {
                    //        formId: 'ydj_customerrecord',
                    //        Id: this.Model.pkid
                    //    }
                    //});
                    //同步请求                 
                    var canCreate = false;
                    yiAjax.p('/bill/ydj_customerrecord?operationno=Validateduty&id=' + that.Model.pkid, {},
                        function (r) {
                            canCreate = r.operationResult.isSuccess;
                        }, null, null, null, { async: false }
                    );
                    e.result = !canCreate;
                    break;
            }
        };
        //表格行删除前事件
        _child.prototype.onEntryRowDeleting = function (args) {
            //debugger;
            var that = this;
            switch (args.id.toLowerCase()) {
                case 'fdutyentry':
                    //that.Model.invokeFormOperation({
                    //    id: 'tbSaleMember',
                    //    opcode: 'validateduty',
                    //    param: {
                    //        formId: 'ydj_customerrecord',
                    //        Id: this.Model.pkid
                    //    }
                    //});
                    var canDelete = false;
                    //同步请求
                    yiAjax.p('/bill/ydj_customerrecord?operationno=Validateduty&id=' + that.Model.pkid, {},
                        function (r) {
                            canDelete = r.operationResult.isSuccess;
                        }, null, null, null, { async: false }
                    );
                    args.result = !canDelete;
                    break;
            }
        }

        //显示或隐藏导购员信息操作
        _child.prototype.procSaleManOp = function () {
            var that = this,
                ds = that.Model.getEntryData({ id: that.dutyEntryId }),
                sales = 0;
            if (ds) {
                for (var i = 0; i < ds.length; i++) {
                    if ($.trim(ds[i].fdutyid_e.id)) {
                        sales++;
                    }
                }
            }
            that.Model.setVisible({ id: '.salemember', value: sales > 1 });
        };

        //处理导购员自动更新到人员明细表格中
        _child.prototype.procStaff = function (staff) {

            var that = this;
            if (staff === undefined) staff = that.Model.getValue({ id: 'fdutyid' });
            if (!staff) return;
            var data = that.Model.getEntryData({ id: that.dutyEntryId }),
                mainRowId = '';
            if (data) {
                for (var i = 0; i < data.length; i++) {
                    if (data[i].fismain) {
                        mainRowId = data[i].id;
                        break;
                    }
                }
            }
            //如果存在主要导购员ID，更新主要导购员字段值
            if (mainRowId) {
                that.Model.setValue({ id: 'fdutyid_e', value: staff, row: mainRowId });
            } else {
                //添加主要导购员明细行
                if (staff && $.trim(staff.id)) {
                    that.Model.addRow({
                        id: that.dutyEntryId,
                        data: { fismain: true, fdutyid_e: staff }
                    });
                }
            }
        };

        // 匹配客户
        _child.prototype.procMatchCustomer = function () {
            var that = this;
            if (!that.isNew) return;

            var ftype = that.Model.getSimpleValue({ id: 'ftype' });
            if (ftype === "1") {
                var fcustomername = that.Model.getValue({ id: 'fcustomername' });
                if (!fcustomername) {
                    return;
                }
                that.Model.invokeFormOperation({
                    id: 'match',
                    opcode: 'match',
                    param: {
                        formId: 'ydj_customer',
                        name: fcustomername,
                        type: "customertype_01"    // 公司客户
                    }
                });
            }
            if (ftype === "2") {
                var fphone = that.Model.getValue({ id: 'fphone' });
                var fwechat = that.Model.getValue({ id: 'fwechat' });
                if (!fphone && !fwechat) {
                    return;
                }
                that.Model.invokeFormOperation({
                    id: 'match',
                    opcode: 'match',
                    param: {
                        formId: 'ydj_customer',
                        type: "customertype_00",    // 个人客户
                        phone: fphone,
                        wechat: fwechat
                    }
                });
            }
        }

        //根据部门重置省市区
        _child.prototype.setArea = function () {
            debugger;
            var that = this;
            var fdeptid = that.Model.getSimpleValue({ id: "fdeptid" });
            yiAjax.p("/bill/ydj_customerrecord?operationno=getareabydept&fdeptid=" + fdeptid, {}, function (r) {
                debugger;
                var data = r.operationResult;
                if (!data || !data.srvData) return;
                var fcountry = data.srvData.fcountry;
                var fprovince = data.srvData.fprovince;
                var fcity = data.srvData.fcity;
                var fregion = data.srvData.fregion;
                if (fcountry) {
                    that.Model.setValue({ id: "fcountry", value: fcountry });
                }
                if (fprovince) {
                    that.Model.setValue({ id: "fprovince", value: fprovince });
                }
                if (fcity) {
                    that.Model.setValue({ id: "fcity", value: fcity });
                }
                if (fregion) {
                    that.Model.setValue({ id: "fregion", value: fregion });
                }
            }, null, null, null, { async: false });
        }

        // 根据部门重新设置下拉数据
        _child.prototype.setSaleCategoryComboData = function () {
            //debugger;
            var that = this;
            var fdeptid = that.Model.getSimpleValue({ id: "fdeptid" });

            yiAjax.p('/dynamic/ydj_dept?operationno=ShowDetail&pkIds=' + fdeptid, {}, function (r) {
                var srvData = r.operationResult.srvData;
                var isSuccess = r.operationResult.isSuccess;
                if (!isSuccess || !srvData || !srvData.datas || srvData.datas.length === 0) {
                    that.mustSaleCategory = false;
                    that.Model.setComboData({ id: "fsalecategory", data: [] });
                    that.Model.setValue({ id: "fsalecategory", value: '' });
                    return;
                }

                var deptData = srvData.datas[0];

                // 当前意向品类值
                var saleCategoryId = that.Model.getSimpleValue({ id: 'fsalecategory' });

                var comboData = [];
                debugger;
                var allSaleCategories = that.Model.viewModel.uiComboData["fsalecategory"] || [];
                if (deptData.fsalecategories) {
                    var ids = deptData.fsalecategories.id.split(",");
                    var names = deptData.fsalecategories.name.split(",");
                    for (var i = 0; i < ids.length; i++) {
                        if (_canAdd(ids[i])) {
                            comboData.push({
                                id: ids[i],
                                name: names[i]
                            });
                        }
                    }
                }

                function _canAdd(id) {
                    if (!id) return false;
                    for (var i = 0; i < allSaleCategories.length; i++) {
                        var saleCategory = allSaleCategories[i];

                        if (saleCategory.id == id) {
                            // 非禁用 或者 在已选值中
                            if (!saleCategory.disable || saleCategoryId.indexOf(id) != -1) {
                                return true;
                            }
                        }
                    }

                    return false;
                }

                var hasValue = saleCategoryId !== "";

                // 设置下拉数据
                that.Model.setComboData({ id: "fsalecategory", data: comboData });

                // 如果门店下的可销品类有选项，则意向品类必录
                if (comboData && comboData.length > 0) {
                    that.mustSaleCategory = true;
                } else {
                    that.mustSaleCategory = false;
                }

                // 如果有值，且在 comboData里，则忽略
                if (hasValue) {
                    var newIds = [], newNames = [];
                    for (var j = 0; j < comboData.length; j++) {
                        // 如果存在
                        if (saleCategoryId.indexOf(comboData[j].id) != -1) {
                            newIds.push(comboData[j].id);
                            newNames.push(comboData[j].name);
                        }
                    }

                    if (newIds.length > 0) {
                        that.Model.setValue({ id: "fsalecategory", value: { id: newIds.join(","), name: newNames.join(",") } });
                    } else {
                        that.Model.setValue({ id: "fsalecategory", value: '' });
                    }
                    return;
                }

                // 如果意向品类只有一个，只自动选择
                if (comboData.length === 1) {
                    that.Model.setValue({ id: "fsalecategory", value: comboData[0] });
                } else {
                    that.Model.setValue({ id: "fsalecategory", value: '' });
                }
            }, function (m) {
                yiDialog.m({ msg: '服务处理错误：' + yiCommon.extract(m) });
            });
        }

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'freferrer':
                    debugger;
                    var fnameVal = that.Model.getValue({ id: 'fcustomername' });
                    var freferrerVal = that.Model.getValue({ id: 'freferrer' });
                    if (fnameVal == freferrerVal.fname) {
                        that.Model.setValue({ id: 'freferrer', value: '' });
                        yiDialog.mt({ msg: '推荐人不能选择自己！', skinseq: 2 });
                    }
                    break;
                case 'fdeptid':
                    // 门店改变时，清空负责人字段
                    that.Model.setValue({ id: 'fdutyid', value: '' });
                    // 门店改变时，重置意向品类
                    that.setSaleCategoryComboData();
                    that.setArea();
                    break;
                case 'fdutyid':
                    // 将导购员自动填充到人员明细表格中
                    that.procStaff(e.value);
                    break;
                case 'fdutyid_e':
                    // 如果销售成员明细超过一行则显示联合开单按钮，否则隐藏
                    that.procSaleManOp();
                    break;
                case 'fsalecategory':
                    break;
                case 'ftype':
                    // 清空
                    that.Model.setValue({ id: 'fphone', value: '' })
                    that.Model.setValue({ id: 'fwechat', value: '' })
                    that.Model.setValue({ id: 'fcustomername', value: '' })
                    that.Model.setValue({ id: 'fcontacts', value: '' })
                    that.Model.setValue({ id: 'fcustomerid', value: '' })
                    that.Model.setValue({ id: 'fbuildingid', value: '' })
                    that.Model.setValue({ id: 'fprovince', value: '' })
                    that.Model.setValue({ id: 'fcity', value: '' })
                    that.Model.setValue({ id: 'fregion', value: '' })
                    that.Model.setValue({ id: 'faddress', value: '' })
                    that.Model.setValue({ id: 'fcustomersource', value: '' })
                    that.Model.setValue({ id: 'fgender', value: '' })
                    that.Model.setValue({ id: 'fage', value: '' })
                    break;
                case 'fphone':
                case 'fwechat':
                case 'fcustomername':
                    that.procMatchCustomer();
                    break;
                case 'goods_id':
                    that.onBillInitProduct('changeall', { attrinfo: e });
                    break;
            }
        };

        //自身特有的操作（菜单操作）
        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            //操作类型
            var opcode = args.opcode.toLowerCase();
            switch (opcode) {
                case 'generatecustomer':
                    if (!$.trim(this.Model.pkid)) return;

                    args.result = true;
                    //this.formContext.cp.parentPageId
                    var parentPageId = that.formContext.pageId;//pageId: that.formContext.pageId,

                    that.Model.invokeFormOperation({
                        id: 'tbCreateCustomer',
                        opcode: 'push',
                        param: {
                            ruleid: "ydj_customerrecord2ydj_customer",
                            sourceId: this.Model.pkid,
                            sourceFormId: "ydj_customerrecord",
                            sourcePageId: parentPageId
                        }
                    });
                    break;
                case 'cusreturn':
                    args.result = true;
                    if (that.formContext.domainType == 'bill') {
                        var selectedRows = [{ PKValue: that.Model.getValue({ id: 'id' }) }];
                    }
                    else {
                        var listId = that.Model.getSelectRows({}),
                            selectedRows = [];
                        for (var i = 0, j = listId.length; i < j; i++) {
                            selectedRows.push({ PKValue: listId[i].pkValue });
                        }
                    }
                    that.Model.invokeFormOperation({
                        id: 'ydj_customerrecord',
                        opcode: 'cusreturn',
                        selectedRows: selectedRows
                    });
                    break;
                //回收到门店
                case 'customerrecordreturn':
                    args.result = true;
                    if (that.formContext.domainType == 'bill') {
                        var selectedRows = [{ PKValue: that.Model.getValue({ id: 'id' }) }];
                    }
                    else {
                        var listId = that.Model.getSelectRows({}),
                            selectedRows = [];
                        for (var i = 0, j = listId.length; i < j; i++) {
                            selectedRows.push({ PKValue: listId[i].pkValue });
                        }
                    }
                    that.Model.invokeFormOperation({
                        id: 'tbLeadsreturn',
                        opcode: 'customerrecordreturn',
                        selectedRows: selectedRows
                    });
                    break;
                //更换负责人
                case 'customerrecordreplace':
                    args.result = true;
                    var cp = {
                        type: 'replace',
                        billId: that.Model.getValue({ id: 'id' }),
                        callback: function (result) {
                            if (!result || !result.newRow) { return; }
                            var newRow = result.newRow;
                            //操作成功则刷新列表
                            if (newRow.flag == 'Y') {
                                that.Model.refresh();
                            }
                        }
                    };
                    that.Model.showForm({
                        formId: 'ydj_leadsreplace',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });
                    break;
                //关闭
                case 'customerrecordclose':
                    args.result = true;
                    var cp = {
                        formid: 'ydj_customerrecord',
                        callback: function (result) {
                            if (!result || !result.newRow) { return; }
                            var newRow = result.newRow;
                            //操作成功则刷新列表
                            if (newRow.flag == 'Y') {
                                that.Model.refresh();
                            }
                        }
                    };
                    that.Model.showForm({
                        formId: 'ydj_leadsclose',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });
                    break;
                //推荐机会
                case 'copy':
                    args.result = true;
                    if (that.formContext.domainType == 'list') {
                        yiDialog.mt({ msg: '销售机会不支持列表通用复制功能！', skinseq: 2 });
                    } else {
                        var cp = {
                            billId: that.Model.getValue({ id: 'id' })
                        };
                        that.Model.showForm({
                            formId: 'ste_chancecopy',
                            param: { openStyle: Consts.openStyle.modal },
                            cp: cp
                        });
                    }
                    break;

                //分配
                case 'customerrecordclaim':
                    args.result = true;
                    var cp = {
                        type: 'claim',
                        callback: function (result) {
                            if (!result || !result.newRow) { return; }
                            var newRow = result.newRow;
                            //操作成功则刷新列表
                            if (newRow.flag == 'Y') {
                                that.Model.refresh();
                            }
                        }
                    };
                    that.Model.showForm({
                        formId: 'ydj_leadsreplace',
                        param: { openStyle: Consts.openStyle.modal },
                        cp: cp
                    });
                    break;

                //点击来源线索字段的字段名查看来源线索详情
                case 'showleads':
                    args.result = true;
                    var pkid = that.Model.getValue({ id: 'fleadssourceid' });
                    that.Model.showForm({
                        formId: 'ydj_leads',
                        openStyle: 'Modal',
                        param: { openStyle: Consts.openStyle.modal },
                        pkids: [pkid]
                    });
                    break;
                //导购员信息
                case 'salemember':
                    args.result = true;
                    that.Model.showPopForm({ popup: 'staff-info' });
                    break;
                //意向单
                case "intentiondetail":
                    args.result = true;
                    var billno = that.Model.getValue({ "id": "fintentionno" });
                    if (billno == null || billno.length <= 0) {
                        yiDialog.mt({ msg: '当前销售机会尚未报价！', skinseq: 2 });
                        return;
                    }
                    that.Model.invokeFormOperation({
                        id: 'intentiondetail',
                        opcode: 'showDetail',
                        param: {
                            domainType: Consts.domainType.bill,
                            formId: 'ydj_saleintention',
                            billNos: billno
                        }
                    });
                    break;
                case "orderdetail":
                    args.result = true;
                    var billno = that.Model.getValue({ "id": "forderno" });
                    if (billno == null || billno.length <= 0) {
                        yiDialog.mt({ msg: '当前销售机会尚未下单！', skinseq: 2 });
                        return;
                    }
                    that.Model.invokeFormOperation({
                        id: 'orderdetail',
                        opcode: 'showDetail',
                        param: {
                            domainType: Consts.domainType.bill,
                            formId: 'ydj_order',
                            billNos: billno
                        }
                    });
                    break;
                case 'staffconfirm':
                    args.result = true;
                    that.Model.hidePopForm({ popup: 'staff-info' });
                    break;

                case 'followerrecord':
                    args.result = true;
                    var sourceId = that.Model.getSimpleValue({ "id": "id" });
                    var sourceNumber = that.Model.getSimpleValue({ "id": "fbillno" });
                    var sourceFormId = that.Model.viewModel.formId;
                    var customerId = that.Model.getSimpleValue({ "id": "fcustomerid" });
                    var ruleId = "{0}2ydj_followerrecord".format(sourceFormId);
                    var isHiddenNewBtn = false;
                    var isHiddenDeleteBtn = false;
                    var filterString = "";

                    if (customerId && customerId.length > 0) {
                        filterString = " fcustomerid='{0}' ".format(customerId);
                    } else {
                        filterString = " fsourcetype='{0}' and fsourcenumber='{1}' ".format(sourceFormId, sourceNumber);
                    }

                    that.Model.showForm({
                        formId: 'ydj_followerrecord',
                        domainType: Consts.domainType.list,
                        openStyle: 'Modal',
                        param: {
                            openStyle: Consts.openStyle.modal,
                            filterstring: filterString
                        },
                        cp: {
                            sourceFormId: sourceFormId,
                            sourceId: sourceId,
                            ruleId: ruleId,
                            isHiddenNewBtn: isHiddenNewBtn,
                            isHiddenDeleteBtn: isHiddenDeleteBtn
                        }
                    });
                    break;
                case "save":
                    // 要求必填
                    if (that.mustSaleCategory) {
                        var saleCategory = that.Model.getSimpleValue({ id: 'fsalecategory' });
                        if (saleCategory === "") {
                            yiDialog.warn('请选择意向品类！');
                            args.result = true;
                        }
                    }
                    break;

            }

        };


        _child.prototype.onBillInitProduct = function (param, params) {
            debugger
            var that = this;
            var productInfos = [];

            if (param == 'changeall') {//改变某一行

                var productEntry = that.Model.getValue({ id: 'fentry' });
                for (var i = 0; i < productEntry.length; i++) {
                    var rowData = productEntry[i];
                    var productId = $.trim(rowData.goods_id && rowData.goods_id.id);
                    if (!productId) continue;


                    //按照接口，重新组合
                    var entities = [];
                    var propEntry = rowData.fattrinfo && rowData.fattrinfo.fentity;
                    if (propEntry) {
                        for (var j = 0; j < propEntry.length; j++) {
                            entities.push({
                                valueId: propEntry[j].fvalueid,
                                auxPropId: propEntry[j].fauxpropid.id
                            })
                        }
                    }
                    productInfos.push({
                        clientId: rowData.id,
                        productId: productId,
                        attrInfo: {
                            id: '',
                            entities: entities
                        }
                    });
                }
                if (productInfos.length < 1) return;

                productInfos = JSON.stringify(productInfos);

                that.Model.invokeFormOperation({
                    id: param,
                    opcode: 'getprices',
                    //option: cvtParams,
                    param: {
                        productInfos: productInfos,
                        formId: 'ydj_price',
                        domainType: 'dynamic'
                    }
                });
            }
        }


        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;

            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'custrclose':
                    if (isSuccess) {
                        //刷新单据
                        that.Model.refresh();
                    }
                    break;
                case 'save':
                    break;
                case 'match':
                    var customer = srvData;
                    if (isSuccess && customer.id) {
                        that.Model.setValue({ id: "fcustomerid", value: customer.fcustomerid });
                        that.Model.setValue({ id: "fcontacts", value: customer.fcontacts });
                        that.Model.setValue({ id: "fwechat", value: customer.fwechat });
                        that.Model.setValue({ id: "fphone", value: customer.fphone });
                        that.Model.setValue({ id: "fcustomername", value: customer.fname });
                        that.Model.setValue({ id: "fbuildingid", value: customer.fbuildingid });
                        that.Model.setValue({ id: "fprovince", value: customer.fprovince });
                        that.Model.setValue({ id: "fcity", value: customer.fcity });
                        that.Model.setValue({ id: "fregion", value: customer.fregion });
                        that.Model.setValue({ id: "faddress", value: customer.faddress });
                        that.Model.setValue({ id: "fcustomersource", value: customer.fsource });
                        that.Model.setValue({ id: "fgender", value: customer.fgender });
                        that.Model.setValue({ id: "fage", value: customer.fage });
                        that.Model.setValue({ id: "fchannelid", value: customer.fchannelid });
                    } else {
                        // 清空数据
                        // that.Model.setValue({ id: 'fphone', value: '' });
                        // that.Model.setValue({ id: 'fwechat', value: '' });
                        // that.Model.setValue({ id: 'fcustomername', value: '' });
                        // that.Model.setValue({ id: 'fcontacts', value: '' });
                        // that.Model.setValue({ id: 'fcustomerid', value: '' });
                        // that.Model.setValue({ id: 'fbuildingid', value: '' });
                        // that.Model.setValue({ id: 'fprovince', value: '' });
                        // that.Model.setValue({ id: 'fcity', value: '' });
                        // that.Model.setValue({ id: 'fregion', value: '' });
                        // that.Model.setValue({ id: 'faddress', value: '' });
                        // that.Model.setValue({ id: 'fcustomersource', value: '' });
                        // that.Model.setValue({ id: 'fgender', value: '' });
                        // that.Model.setValue({ id: 'fage', value: '' });
                        // that.Model.setValue({ id: "fchannelid", value: '' });
                    }
                    break;
                case 'validateduty':
                    //debugger;
                    //if (!isSuccess) {
                    //    var dutyentry = that.Model.getEntryData({ id: 'fdutyentry' });
                    //    for (var i = 0; i < dutyentry.length; i++) {
                    //        if (!dutyentry[i].fdutyid_e.id) {
                    //            that.Model.deleteRow({ id: 'fdutyentry', row: dutyentry[i].id })
                    //        }
                    //    }
                    //    that.Model.refreshEntry({ id: 'fdutyentry' });
                    //}

                    break;
                case 'getprices':
                    if (e.id == 'init' || e.id == 'change' || e.id == 'changeall') {

                        if (!srvData) {
                            //yiDialog.mt({msg:' 无相关查询数据', skinseq: 2});
                            return;
                        }
                        var str = '';
                        var gridData = that.Model.getValue({ id: 'fentry' });

                        for (var i = 0, l = srvData.length; i < l; i++) {
                            var lm = srvData[i];
                            var rowData = {};
                            var num = 0;
                            for (var n = 0, m = gridData.length; n < m; n++) {
                                if (gridData[n].id == lm.clientId) {//获取对于行
                                    rowData = gridData[n];
                                    num = n + 1;
                                }
                            }
                            if (lm.success) {//价格匹配成功，则赋值
                                if (rowData.fisself == null || rowData.fisself == false) {

                                    if (rowData.fisgiveaway) {
                                        // 不触发计算规则
                                        that.Model.setValue({ id: 'fprice', row: lm.clientId, value: lm.salPrice, tgChange: false });
                                    } else {
                                        that.Model.setValue({ id: 'fprice', row: lm.clientId, value: lm.salPrice });
                                    }
                                }

                            }
                        }
                    }
                    break;
            }
        }

        //加载下拉框数据源时触发：可由业务插件提供数据源
        _child.prototype.onComboDataLoading = function (e) {
            var that = this;
            switch (e.id) {
                case 'fsalecategory':
                    e.options = {
                        noquery: true
                    };
                    break;
            }
        };

        return _child;
    })(BillPlugIn);
    window.ydj_customerrecord = window.ydj_customerrecord || ydj_customerrecord;
})();