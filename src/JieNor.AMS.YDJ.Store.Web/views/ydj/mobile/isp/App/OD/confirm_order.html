<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>确认预约</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body id="confirm">
    <div class="bar bar-header bar-white">
        <a class="bar-title-left blue back" onclick="Andoggy.finishPage()">取消</a>
        <div class="title">确认预约</div>
        <a class="bar-title-right blue" onclick="submit()">确定</a>
    </div>
    <div class="marginTop44">
        <button class="btn-bg-blue operation" onclick="tabChange(0)">预约成功</button>
        <button class="btn-bg-grey operation" onclick="tabChange(1)">预约失败</button>
    </div>
    <ul class="content">
        <li class="grey border bg-white">
            <label class="item item-input">
                <span class="input-label">预约时间</span>
                <input class="content-right grey" type="datetime-local" id="ordertime" />
                <i class="icon ion-ios-arrow-right right grey"></i>
            </label>
        </li>
        <li class="grey border bg-white">
            <span class="input-label">加入日程</span>
            <label class="toggle toggle-calm">
                <input type="checkbox" id="Chk">
                <div class="track">
                    <div class="handle"></div>
                </div>
            </label>
        </li>
        <li class="border bg-white hide" id="Chktag">
            <label class="item item-input">
                <span class="input-label">提醒方式</span>
                <select name="fremind" data-bind="fremind" id="fremind" data-ch_event="Ch" class="content-right"></select>
            </label>
        </li>
        <li class="grey border bg-white"><span class="input-label">预约备注</span></li>
        <li class="border record bg-white" id="certain">
            <textarea id="certain-box" class="textarea" maxlength="100" oninput="this.value=this.value.substring(0, 100), DomTag('certaintnum').innerText = this.value.length"></textarea>
            <span class="grey"><span id="certaintnum">0</span><span>/100</span></span>
        </li>
        <li>注意事项：</li>
        <li>
            1、师傅与客户预约时注意确认核实以下事项
            <div>A.业主家是否已具备安装条件</div>
            <div>B.安装的货物是否已运送到现场</div>
            <div>C.是否存在货物不全的情况</div>
        </li>
        <li>
            2、师傅与业主预约好时间后，平台会自动发送消息给业主，师傅注意按约定时间上门签到，否则进行处罚
        </li>
    </ul>

    <ul class="content bg-white hide">
        <li>预约备注：</li>
        <li class="record" id="cancel">
            <textarea id="cancel-box" class="textarea" maxlength="100" oninput="this.value=this.value.substring(0, 100), DomTag('canceltnum').innerText = this.value.length"></textarea>
            <span class="grey"><span id="canceltnum">0</span><span>/100</span></span>
        </li>
        <li class="grey instructions">选中下方标签后自动带入到输入框中</li>
        <li id="reason">
            <button class="bg-grey col45 lables">客户改约</button>
            <button class="bg-grey col45 lables">客户电话未接</button>
            <button class="bg-grey col45 lables">天气原因</button>
        </li>
    </ul>

    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        var FromId = "sys_schedule";
        GetEnum(FromId, "fremind", function () {
            var _nl = arguments[0];
            DomTag("fremind").innerHTML = "";
            //循环提醒数据
            for (var i = 0; i < _nl.length; i++) {
                var op = CreateDom("option");
                op.innerText = _nl[i].name;
                op.value = _nl[i].id;
                DomTag("fremind").appendChild(op);
            }
        });
        DomTag("Chk").onclick = function () {
            if (Chk.checked) {
                RemoveClass("Chktag", "hide");
            }
            else {
                AddClass("Chktag", "hide");
            }

        }
        //点解按钮切换内容
        DomTag('ordertime').value = new Date().ToString("yyyy-MM-dd") + "T" + new Date().ToString("HH:mm:00.000");
        var _btn = document.getElementsByClassName('operation');
        var _ul = document.getElementsByTagName('ul');
        var fid = GetQueryString('fid');
        function tabChange() {
            var index = arguments[0];
            switch (index) {
                case 0:
                    //tab切换时title文字变化
                    DomTag('.title')[0].innerText = '确认预约';
                    //tab切换时按钮颜色的变化
                    AddClass(_btn[index], 'btn-bg-blue');
                    RemoveClass(_btn[index + 1], 'btn-bg-blue');
                    AddClass(_btn[index + 1], 'btn-bg-grey');
                    //tab切换时对应内容的显示隐藏
                    RemoveClass(_ul[index], 'hide');
                    AddClass(_ul[index + 1], 'hide');
                    break;
                case 1:
                    //tab切换时title文字变化
                    DomTag('.title')[0].innerText = '取消预约';
                    //tab切换时按钮颜色的变化
                    AddClass(_btn[index], 'btn-bg-blue');
                    RemoveClass(_btn[index - 1], 'btn-bg-blue');
                    AddClass(_btn[index - 1], 'btn-bg-grey');
                    //tab切换时对应内容的显示隐藏
                    RemoveClass(_ul[index], 'hide');
                    AddClass(_ul[index - 1], 'hide');

                    break;
            }
        };

        //输入框中原有字数
        DomTag('certaintnum').innerText = String(DomTag('certain-box').value).Trim().length;
        DomTag('canceltnum').innerText = String(DomTag('cancel-box').value).Trim().length;

        //点击下面原因并赋值到输入框中
        var _lable = DomTag('#reason .lables');
        for (var i = 0; i < _lable.length; i++) {
            _lable[i].addEventListener('touchstart', function () {
                RemoveClass(this, 'bg-grey');
                AddClass(this, 'bg-blue');
                var val = String(DomTag('cancel-box').value).Trim();
                DomTag('cancel-box').value = (val + ' ' + this.innerText).substring(0, 100);
                DomTag('canceltnum').innerText = String(DomTag('cancel-box').value).Trim().length;
            });

            _lable[i].addEventListener('touchend', function () {
                RemoveClass(this, 'bg-blue');
                AddClass(this, 'bg-grey');
            });
        }

        //填写完原因后提交原因数据到后台保存
        function submit() {
            var _attr = _ul[0].getAttribute('class');
            var datas = { "id": fid };
            var params, flag = false, historyentry = [];
            if (_attr.indexOf('hide') > -1) {
                historyentry.push({ fappointdesc: DomTag('cancel-box').value, fappointdate: DomTag('ordertime').value.replace("T", " "), fissuccess: false })

            } else {
                historyentry.push({ fappointdate: DomTag('ordertime').value.replace("T", " "), fissuccess: true, fappointdesc: DomTag('certain-box').value });
                datas.fservicedate = DomTag('ordertime').value.replace("T", " ");
                datas.fisschedule = document.getElementById("Chk").checked;
                flag = true;
            }
            datas.fhistoryentry = historyentry;
            if (_ul[1].getAttribute('class').indexOf('hide') <= -1 && String(DomTag('cancel-box').value).Trim().length <= 0) {
                Message.Alert("预约备注不能为空！");
                return;
            }
            //判断是否能够写入日程
            if (datas.fisschedule) {
                var ct = parseInt(DomTag("fremind").value.replace("remind_", ""));
                var time = String(DomTag('ordertime').value.replace("T", " ")).ToDate();
                var NowTime = new Date();
                var formatStr = null;
                switch (ct) {
                    case 2:
                        formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 10)) ? time.ToString("yyyy-MM-dd HH:mm:ss") : null;
                        break;
                    case 3:
                        formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 40)) ? new Date(time.setMinutes(time.getMinutes()-30)).ToString("yyyy-MM-dd HH:mm:ss") : null;
                        break;
                    case 4:
                        formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 70)) ? new Date(time.setMinutes(time.getMinutes() - 60)).ToString("yyyy-MM-dd HH:mm:ss") : null;
                        break;
                    case 5:
                        formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 130)) ? new Date(time.setMinutes(time.getMinutes() - 120)).ToString("yyyy-MM-dd HH:mm:ss") : null;
                        break;
                    case 6:
                        formatStr = time > new Date(NowTime.setMinutes(NowTime.getMinutes() + 1450)) ? new Date(time.setMinutes(time.getMinutes() - 1440)).ToString("yyyy-MM-dd HH:mm:ss") : null;
                        break;
                    default:
                }
                if (!String(formatStr).isNullOrEmpty()) {
                    Andoggy.addCalendarEvent(localStorage.getItem('Phone'), "预约上门" + time.ToString("yyyy-MM-dd HH:mm:ss"), DomTag('certain-box').value, formatStr, "", 'Insertcallback');
                }
                else if (ct > 1) {
                    Message.Alert("时间超出范围，日程提醒只能在提醒时间10分钟之前");
                    return;
                }
            }
            Ajax({
                url: "/bill/ydj_service?operationno=setstatus07&format=json",
                data: {
                    fromId: "ydj_service",
                    operationNo: 'setstatus07',
                    simpledata: { opid: "setstatus07", serstatus: "sersta06", remind: DomTag("fremind").value },
                    billData: JSON.stringify([datas])
                },
                callback: function () {
                    var Json = arguments[0];
                    if (String(Json.operationResult.isSuccess).Boolean()) {
                        Message.Alert('操作成功!', 2000);
                        setTimeout(function () {
                            var isdetail = String(GetQueryString("isdetail")).Boolean();
                            if (isdetail) {
                                localStorage.setItem("reloadhome", "TabExec(1" + (flag ? ",true" : "") + ")");
                                flag ? localStorage.setItem("Reload", "true") : null;
                            } else {
                                localStorage.setItem("FunEvent", "TabExec(1" + (flag ? ",true" : "") + ")");
                            }
                            Andoggy.finishPage();
                        }, 2000)

                    } else {
                        if (Json.operationResult.complexMessage.errorMessages.length > 0) {
                            Message.Alert(Json.operationResult.complexMessage.errorMessages[0])
                        } else {
                            Message.Alert(Json.operationResult.simpleMessage);
                        }
                    }
                }
            });
        };
        function Insertcallback() {
            Message.Alert("日程加入成功");
        }
    </script>
</body>
</html>
