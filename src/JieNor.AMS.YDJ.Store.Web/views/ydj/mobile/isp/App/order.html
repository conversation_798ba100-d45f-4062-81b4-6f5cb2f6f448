<html>
<head>
    <meta name="format-detection" content="telephone=no" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="initial-scale=1,maximum-scale=1, user-scalable=no" />
    <title>易到家（师傅端）首页</title>
    <link href="/fw/css/ydj/mobile/isp/App/ionic.css" rel="stylesheet" />
    <link href="/fw/css/ydj/mobile/isp/App/override.css" rel="stylesheet" />
</head>
<body>
    <div class="main-content" id="panel" style="position:relative">
        <div class="bar bar-header bar-white">
            <div menu-toggle="left" class="button button-icon icon ion-navicon " id="menu-toggle"></div>
            <div class="title">易到家（师傅端）首页</div>
        </div>
        <div id="Scroll" class="has-no-header content">
            <div class="slideout-wrapper" style="height:500px; background-color:#fff">
                我是订单内容哈哈哈哈哈
            </div>
        </div>

    </div>
    <div class="left-content" id="menu">
        <!--个人主页-->
        <div class="bar bar-header user-center black">
            <a onclick="Redirect('/views/ydj/mobile/isp/App/personalInfo.html')"><img src="/fw/css/ydj/mobile/isp/App/images/App-logo.png" class="user-image" data-bind="fimage"  /></a>
            <span class="user-account" data-bind="fphone">131****4113</span>
        </div>
        <div class="content has-header personal-info" id="Scroll">
            <div class="list">
                <label class="item item-input" onclick="Redirect('/views/ydj/mobile/isp/App/my_team.html')">
                    <i class="icon ion-ios-people-outline"></i>我的团队
                </label>
                <label class="item item-input">
                    <i class="icon ion-ios-briefcase-outline"></i>我的钱包
                </label>
                <label class="item item-input">
                    <i class="icon ion-ios-home-outline"></i>师傅学堂
                </label>
                <label class="item item-input">
                    <i class="icon ion-ios-keypad-outline"></i>更多
                </label>
            </div>
        </div>
    </div>
    <script src="/fw/js/ydj/mobile/isp/App/slideout.min.js"></script>
    <script src="/fw/js/ydj/mobile/isp/App/Comm.js"></script>
    <script>
        var doc = window.document;
        var slideout = new Slideout({
            'panel': doc.getElementById('panel'),
            'menu': doc.getElementById('menu'),
            'padding': 220
        });
        window.onload = function () {
            document.getElementById('menu-toggle').addEventListener('click', function () {
                slideout.toggle();
            });
        };


        var id = null;
        var sql = new WebSql();
        sql.Select("UserInfo", ["*"], null, function () {
            var Data = arguments[0];
            if (Data.length == 0) {
                return;
            }
            Data = Data[0];
            var Init = new HtmlSetData();
            Init.Init(Data);
            id = Init.id;
        });
    </script>
</body>
</html>