<style>
/*收银单样式*/
.pay-method .radio-inline{
    height: 50px;
    padding-top: 13px;
    border-radius: 4px !important;
    -webkit-transition: 0.25s;
    -moz-transition: 0.25s;
    -o-transition: 0.25s;
    -ms-transition: 0.25s;
    transition: 0.25s;
}
.pay-method .radio-inline:hover{
	-webkit-transition: 0.25s;
    -moz-transition: 0.25s;
    -o-transition: 0.25s;
    -ms-transition: 0.25s;
    transition: 0.25s;
}
.pay-method .radio-inline:nth-child(1){
	background: url(../fw/images/cash.png) 10px 6px no-repeat;
	background-position: ;
	background-size: 35px 35px;
}
.pay-method .radio-inline:nth-child(1):hover{
	color: #fff;
	background:#7bb9f3 url(../fw/images/cash_hover.png) 10px 6px no-repeat;
	background-size: 35px 35px;
}
.pay-method .radio-inline:nth-child(2){
	background: url(../fw/images/bank.png) 10px 6px no-repeat;
	background-size: 35px 35px;
}
.pay-method .radio-inline:nth-child(2):hover{
	color: #fff;
	background:#7bb9f3 url(../fw/images/bank_hover.png) 10px 6px no-repeat;
	background-size: 35px 35px;
}
.pay-method .radio-inline:nth-child(3){
	background: url(../fw/images/alipay.png) 10px 6px no-repeat;
	background-size: 35px 35px;
}
.pay-method .radio-inline:nth-child(3):hover{
	color: #fff;
	background:#7bb9f3 url(../fw/images/alipay_hover.png) 10px 6px no-repeat;
	background-size: 35px 35px;
}
.pay-method .radio-inline:nth-child(4){
	background: url(../fw/images/wechatpay.png) 10px 6px no-repeat;
	background-size: 35px 35px;
}
.pay-method .radio-inline:nth-child(4):hover{
	color: #fff;
	background:#7bb9f3 url(../fw/images/wechatpay_hover.png) 10px 6px no-repeat;
	background-size: 35px 35px;
}
.paymethod-box{
	border: 1px #eee solid;
    padding-bottom: 10px;
    background-color: #fff;
}
.paymethod-box label.col-sm-12{
    line-height: 35px;
    text-align: center !important;
    background: #529de3;
    background-color: #529de3;
    color: #fff;
    font-size: 17px;
    margin-bottom: 10px !important;
    margin-top: -3px;
}
.cash-sum-box{
    height: 45px;
	background: #45535e;
}
.cash-sum-box:hover{
	background: #45535e;
}
.cash-sum-box label{
    color: #fff;
    font-size: 19px;
    line-height: 15px;
    border-right: 1px #8e9eab solid;
    padding-right: 20px;
    margin-top: 8px !important;
}
.cash-sum-box .input-icon input{
	height: 36px !important;
    background: #45535e;
    color: #fff;
    font-size: 19px;
    border: none;
    cursor: default;
}
.paymethod-box label{
	text-align: center;
}
.pay-method .radio-inline .radio{
	float: right;
}
.pay-method .radio-inline{
	margin: 0;
	padding-left: 60px !important;
}
.old-order-info{
	padding: 0 5px !important;
    margin-top: 20px;
}
#Fstored,#Fintegration{
	padding-right: 0px !important;
}
.default-p{
    color: #45535e;
    margin-top: 10px;
}
.cash-label{
	color: #797979;
	font-size: 12px;
    height: 33px;
    line-height: 33px;
}
</style>	
<!--页面内容面板-->
<form action="javascript:;" class="form-horizontal">
	<div class="leftTab">
		<div class="portlet box yellow-casablanca">
	        <div class="portlet-title">
	            <div class="caption">
	                	基本信息
	            </div>
	            <div class="tools">
	                <a href="javascript:;" class="collapse"></a>
	            </div>
	        </div>
	        <div class="portlet-body form">
			    <div class="form-body">
			    	<!--验证提示信息-->
				    <div class="alert alert-danger display-hide">
				        <button class="close" data-close="alert"></button>
				        页面中有一些带红色星号 * 的信息是必填项，请检查是否都有正确录入！
				    </div>
			        <div class="row">
			        	<div class="col-md-4">
			        		<div class="form-group">
			                    <label class="col-md-3 control-label"><span class="required">*</span>单据编号</label>
			                    <div class="col-md-8 cols">
			                        <div class="input-icon right">
			                            <input type="text" class="form-control" name="fbillno" required="required" />
			                        </div>
			                    </div>
			                </div>
			        		<div class="form-group">
			                    <label class="col-md-3 control-label"><span class="required">*</span>日期</label>
			                    <div class="col-md-8">
			                        <div class="input-group date date-picker">
			                            <input type="text" class="form-control" name="fdate" 
			                            		 required="required" />
			                            <span class="input-group-addon">
			                                <i class="fa fa-calendar"></i>
			                            </span>
			                        </div>
			                    </div>
			                </div>
			                <div class="form-group">
			                    <label class="col-md-3 control-label">收支类型</label>
			                    <div class="col-md-8">
			                        <div class="radio-list">
			                            <label class="radio-inline" id="fcollect">
			                                <input type="radio" name="fszlx" value="settle_direction_001" caption="收">
			                               	收
			                            </label>
			                            <label class="radio-inline" id="fpayment">
			                                <input type="radio" name="fszlx" value="settle_direction_002" caption="退">
			                               	 退
			                            </label>
			                        </div>
			                    </div>
			                </div>
			        	</div>
			        	<div class="col-md-4">
			        		<div class="form-group">
	                            <label class="col-md-3 control-label"><span class="required">*</span>客户</label>
	                            <div class="col-md-8">
	                                <div class="input-icon right input-group">
	                                    <i class="fa"></i>
	                                    <input type="lookup" class="form-control" autocomplete="off" required
	                                           name="fcustomerid" placeholder="客户" maxlength="50" />
	                                </div>
	                            </div>
	                        </div>
			        		<div class="form-group">
			                    <label class="col-md-3 control-label"><span class="required">*</span>用途</label>
			                    <div class="col-md-8">
			                        <select class="form-control select2me dynamic" required="required" name="fusage" id="fusage">
										<option value=""></option>
									</select>
			                    </div>
			               	</div>
			               	<div class="form-group">
			                    <label class="col-md-3 control-label">订货单</label>
			                    <div class="col-md-8 cols">
			                        <div class="input-icon right">
			                            <input type="text" class="form-control" name="forderno" disabled="disabled" />
			                        </div>
			                    </div>
			                    <div class="col-md-12 no-pad" style="margin-top: 10px;">
			                    	<div class="col-xs-8 col-xs-offset-3">
			                    		<label class="col-xs-4 no-pad cash-label">订单金额</label>
					                    <div class="col-xs-8 no-pad">
					                        <input type="text" class="form-control disa-input" name="ftotalmoney" number="true" disabled="disabled"  />
					                    </div>
			                    	</div>
			                    </div>
			                </div>
			        	</div>
			        	<div class="col-md-4">
			        		<div class="form-group">
					            <label class="col-md-3 control-label">备注</label>
					            <div class="col-md-8 cols">
					                <div class="input-icon right">
					                    <textarea cols="40" rows="3" name="fdescription" class="form-control"></textarea>
					                </div>
					            </div>
					        </div>
			        	</div>
				    </div>
	            </div>
	        </div>
	    </div>
	    <div class="portlet box yellow-casablanca">
	        <div class="portlet-title">
	            <div class="caption">
	                	结算信息
	            </div>
	            <div class="tools">
	                <a href="javascript:;" class="collapse"></a>
	            </div>
	        </div>
	        <div class="portlet-body form">
			    <div class="form-body">
			        <div class="row">
			        	<div class="col-md-4"  style="padding: 0 30px">
			        		<div class="form-group paymethod-box">
			                    <label class="col-sm-12 control-label">结算方式</label>
			                    <div class="col-xs-12">
			                        <div class="radio-list pay-method">
			                            <label class="radio-inline col-xs-12">
			                                	现金<input type="radio" name="fpay" value="settle_type_001" caption="现金">
			                            </label>
			                            <label class="radio-inline col-xs-12">
			                                	银行<input type="radio" name="fpay" value="settle_type_002" caption="银行">
			                            </label>
			                            <label class="radio-inline col-xs-12">
			                                	支付宝<input type="radio" name="fpay" value="settle_type_003" caption="支付宝">
			                            </label>
			                            <label class="radio-inline col-xs-12">
			                                	微信支付<input type="radio" name="fpay" value="settle_type_004" caption="微信支付">
			                            </label>
			                        </div>
			                    </div>
			                </div>
			        	</div>
			        	<div class="col-md-4" style="padding: 0 30px">
			        		<div class="form-group cash-sum-box">
			                    <label class="col-xs-6 control-label">结算总额</label>
			                    <div class="col-xs-6 cols">
			                        <div class="input-icon right">
			                            <input type="text" class="form-control" name="fsum" 
			                            	id="fsum" value="" number="true" disabled/>
			                        </div>
			                    </div>
			                </div>
			                <div class="form-group e-hide" id="banknum_box">
	                            <label class="col-md-3 control-label">银行卡</label>
	                            <div class="col-md-8">
	                                <div class="input-icon right input-group">
	                                    <i class="fa"></i>
	                                    <input type="lookup" class="form-control" autocomplete="off"
	                                           name="fbanknumid" placeholder="银行账号" digits="true" maxlength="21" />
	                                </div>
	                            </div>
	                        </div>
	                        <div class="form-group">
	                            <label class="col-md-3 control-label">支付金额</label>
	                            <div class="col-md-8">
	                                <div class="input-icon right">
	                                    <i class="fa"></i>
	                                    <input type="text" class="form-control" autocomplete="off"
	                                           name="fmoney" number="true" value="0"/>
	                                </div>
	                            </div>
	                        </div>
			            	<div class="form-group" id="djzf">
			                    <label class="col-md-3 control-label">定金使用</label>
			                    <div class="col-md-8 cols">
			                        <div class="input-icon left input-group">
		                            	<span class="input-group-addon" style="cursor: pointer;">
		                                	<div class="checkbox-list" style="margin-top: -5px;">
		                                        <label class="checkbox-inline">
		                                            <input type="checkbox" name="fisdeposit" id="chk">
		                                        </label>
		                                    </div>
		                                </span>
		                                <input type="text" class="form-control" autocomplete="off"  name="fdepositpay" number="true">
		                            </div>
			                    </div>
			                </div>	
			        	</div>
			        	<div class="col-md-4">
			        		<div class="form-group">
	                            <label class="col-md-3 control-label">凭据</label>
	                            <div class="col-md-8">
	                                <div caution="" hasbutton="" limit="1" sizelimit="" allowExt="" class="uploader-file" name="fcredential">上传附件</div>
	                                <p class="default-p">建议上传订单付款凭证，如汇款单等；附件支持JPG\PNG\Word\Excel\Txt格式</p>
	                            </div>
	                        </div>
			        	</div>
				    </div>
	            </div>
	        </div>
	    </div>
	    <!--<div class="col-md-12 portlet box yellow-casablanca old-order-info">
	        <div class="portlet-title">
	            <div class="caption">源单信息</div>
	            <div class="tools"><a href="###" class="collapse"></a></div>
	        </div>	
	        <div class="portlet-body form">
	            <div class="form-body">
	                <div class="row">
                        <table entryid="flinkentry" data-options="allowAdd:false,allowDel:false,allowEdit:true"></table>
	                </div>
	            </div>
	        </div>
	    </div>-->
	</div>
</form>