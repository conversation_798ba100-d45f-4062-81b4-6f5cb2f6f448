<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）

    库存余额
-->
<html lang="en">
<head>
</head>
<body id="stk_inventorybalance" basemodel="stk_inventorylist" el="1" cn="库存余额" rac="true" isolate="1" nfsa="true">

    <div id="fbillhead" el="51" pk="fid" tn="t_stk_inventorybalance" pn="fbillhead" cn="单据头">
        <input group="基本信息" el="112" ek="FBillHead" id="fclosedate" fn="fclosedate" pn="fclosedate" visible="-1" cn="关账日期"
               lock="-1" copy="0" lix="0" notrace="true" ts="" />
        <input group="基本信息" el="117" ek="FBillHead" id="fbalancetype" fn="fbalancetype" pn="fbalancetype" visible="-1" cn="余额类型"
               lock="0" copy="1" lix="0" notrace="true" ts="" refid="bd_enum" cg="库存余额类型" dfld="fenumitem" />

        <input lix="80" group="基本信息" el="103" ek="fbillhead" id="fbeginqty" fn="fbeginqty" pn="fbeginqty" visible="-1" cn="期初数量(基本单位)" width="110" lock="-1" copy="0" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" />
        <input lix="85" group="基本信息" el="103" ek="fbillhead" id="fbeginstockqty" fn="fbeginstockqty" pn="fbeginstockqty" visible="-1" cn="期初数量(库存单位)" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstockunitid" basqtyfk="fbeginqty" roundType="0" format="0,000.00" />

        <input lix="80" group="基本信息" el="103" ek="fbillhead" id="finqty" fn="finqty" pn="finqty" visible="-1" cn="本期入库(基本单位)" width="110" lock="-1" copy="0" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" />
        <input lix="85" group="基本信息" el="103" ek="fbillhead" id="finstockqty" fn="finstockqty" pn="finstockqty" visible="-1" cn="本期入(库存单位)" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstockunitid" basqtyfk="finqty" roundType="0" format="0,000.00" />

        <input lix="80" group="基本信息" el="103" ek="fbillhead" id="foutqty" fn="foutqty" pn="foutqty" visible="-1" cn="本期出库(基本单位)" width="110" lock="-1" copy="0" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" />
        <input lix="85" group="基本信息" el="103" ek="fbillhead" id="foutstockqty" fn="foutstockqty" pn="foutstockqty" visible="-1" cn="本期出库(库存单位)" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstockunitid" basqtyfk="foutqty" roundType="0" format="0,000.00" />

        <input lix="80" group="基本信息" el="103" ek="fbillhead" id="fqty" fn="fqty" pn="fqty" visible="-1" cn="结余数量(基本单位)" width="110" lock="-1" copy="0" notrace="true" ts="" ctlfk="funitid" roundType="0" format="0,000.00" />
        <input lix="85" group="基本信息" el="103" ek="fbillhead" id="fstockqty" fn="fstockqty" pn="fstockqty" visible="-1" cn="结余数量(库存单位)" lock="-1" copy="0" notrace="true" ts="" ctlfk="fstockunitid" basqtyfk="fqty" roundType="0" format="0,000.00" />


        <input group="基本信息" el="105" ek="fbillhead" id="finicostprice" fn="finicostprice" pn="finicostprice" visible="1086" cn="期初成本价" lock="-1" copy="0" lix="130" xlsin="0" notrace="true" ts="" roundType="0" format="0,000.00" />
        <input group="基本信息" el="105" ek="fbillhead" id="finicostamt" fn="finicostamt" pn="finicostamt" visible="1086" cn="期初总成本" lock="-1" copy="0" lix="130" notrace="true" xlsin="0" ts="" roundType="0" format="0,000.00" />

        <input group="基本信息" el="105" ek="fbillhead" id="fcostprice" fn="fcostprice" pn="fcostprice" visible="1086" cn="期末成本价" lock="-1" copy="0" lix="130" xlsin="0" notrace="true" ts="" roundType="0" format="0,000.00" />
        <input group="基本信息" el="105" ek="fbillhead" id="fcostamt" fn="fcostamt" pn="fcostamt" visible="1086" cn="期末总成本" lock="-1" copy="0" lix="130" notrace="true" xlsin="0" ts="" roundType="0" format="0,000.00" />

        <input group="基本信息" el="112" ek="fbillhead" id="fcreatedate" fn="fcreatedate" pn="fcreatedate" visible="0" cn="创建日期" lock="-1" copy="0" lix="0" notrace="true" ts="" />
        <input group="基本信息" el="100" ek="fbillhead" id="fdescription" fn="fdescription" pn="fdescription" visible="0" cn="操作描述" lock="-1" copy="0" lix="0" notrace="true" ts="" />
    </div>

    <div id="opList">
        <ul el="10" ek="fbillhead" id="listdatatopdf" op="listdatatopdf" opn="导出PDF" data="" permid="fw_export"></ul>
        <ul el="10" ek="fbillhead" id="listdatatoexcel" op="listdatatoexcel" opn="导出Excel" data="" permid="fw_export"></ul>
    </div>

    <div id="ListFuzzyFlds" cn="默认支持快捷过滤的字段列表">
        <ul el="14" id="fw_fuzzyfld" fldkeys="fmaterialid.fname,fmtrlnumber,fmtrlmodel,fmtono" cn="默认支持快捷过滤的字段列表，多个用逗号或分号隔开"></ul>
    </div>

</body>
</html>