<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ms_deliver_series" el="3" basemodel="" cn="送达方与系列" desc="用于保存慕思同步的数据" isolate="0" rac="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ms_deliver_series" pn="fbillhead" cn="送达方与系列">

        <input type="datetime" id="fcreatedate" el="113" ek="fbillhead" fn="fcreatedate" pn="fcreatedate" cn="创建日期" width="130" visible="-1" copy="0" lix="251" xlsin="0" />
        <input type="datetime" id="fmodifydate" el="113" ek="fbillhead" fn="fmodifydate" pn="fmodifydate" cn="更新日期" visible="1124" copy="0" lix="253" xlsin="0" />

        <input type="text" id="fagentid" el="100" ek="fbillhead" fn="fagentid" pn="fagentid" cn="外部售达方ID(我方经销商)" width="120" visible="1150" />
        <input type="text" id="fagentnumber" el="100" ek="fbillhead" fn="fagentnumber" pn="fagentnumber" cn="售达方编码(我方经销商)" width="120" visible="-1" />

        <input type="text" id="fdistributorid" el="100" ek="fbillhead" fn="fdistributorid" pn="fdistributorid" cn="外部CRM经销商ID" width="120" visible="1150" />
        <input type="text" id="fdistributornumber" el="100" ek="fbillhead" fn="fdistributornumber" pn="fdistributornumber" cn="外部CRM经销商编码" width="120" visible="-1" />
        
        <input type="text" id="fdelivernumber" el="100" ek="fbillhead" fn="fdelivernumber" pn="fdelivernumber" cn="送达方编码" width="120" visible="-1" />

        <input type="text" id="fcitynumber" el="100" ek="fbillhead" fn="fcitynumber" pn="fcitynumber" cn="授权城市编码" width="120" visible="-1" />

        <input type="text" id="fbrandid" el="100" ek="fbillhead" fn="fbrandid" pn="fbrandid" cn="外部授权品牌ID" width="120" visible="1150" />

        <input type="text" id="fseriesid" el="100" ek="fbillhead" fn="fseriesid" pn="fseriesid" cn="外部系列ID" width="120" visible="1150" />
        <input type="text" id="fseriesnumber" el="100" ek="fbillhead" fn="fseriesnumber" pn="fseriesnumber" cn="系列编码" width="120" visible="-1" />

        <input type="text" id="fbossid" el="100" ek="fbillhead" fn="fbossid" pn="fbossid" cn="外部实控人ID" width="120" visible="-1" />

        <input type="text" id="fmybossnumber" el="107" ek="fbillhead" fn="fmybossnumber" pn="fmybossnumber" cn="实控人编码" width="120" ctlfk="fmybossid" dispfk="fnumber" visible="-1" />
        <input type="text" id="fmybossid" el="106" ek="fbillhead" fn="fmybossid" pn="fmybossid" cn="实控人" width="120" refid="ms_boss" visible="-1" />

        <input type="checkbox" id="fforbidstatus" el="116" ek="fbillhead" fn="fforbidstatus" pn="fforbidstatus" cn="禁用状态" visible="1062" xlsin="0" copy="0" lix="263" />

        <input type="text" id="ftranid" el="100" ek="FBillHead" fn="ftranid" ts="" cn="交易流水号" visible="-1" xlsin="0" copy="0" />

        <input type="text" id="fdescription" el="100" ek="fbillhead" fn="fdescription" pn="fdescription" cn="备注" width="120" visible="-1" />

        <input type="text" id="fjson" el="127" ek="fbillhead" fn="fjson" pn="fjson" cn="json" width="120" visible="0" lix="12" />
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="syncfrommusi" op="syncfrommusi" opn="从慕思拉取数据" permid="fw_syncfrommusi"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_view" cn="查看" order="1"></ul>
        <ul el="12" id="fw_syncfrommusi" cn="从慕思拉取数据"></ul>
    </div>
</body>
</html>