<!-- 选配配件映射
    Author: zpf
    CreateTime:2022/1/7 14pm -->
<html lang="en">
<head>
</head>
<body id="sel_fittingsmap" el="3" basemodel="bd_basetmpl" cn="选配配件映射" isolate="0" IsAsynLstDesc="true">
    <!-- 4.1.11.4.1.1基本信息-单据头 -->
    <div id="fbillhead" el="51" pk="fid" tn="t_sel_fittingsmap" pn="fbillhead" cn="选配配件映射">
        <input group="基本信息" ek="fbillhead" type="text" el="108" id="fnumber" fn="fnumber" pn="fnumber" cn="编码" width="120" visible="-1" copy="0" lix="1" />
        <input group="基本信息" ek="fbillhead" type="text" el="100" id="fname" fn="fname" pn="fname" cn="名称" width="120" visible="-1" lix="2" />
        <input group="基本信息" ek="fbillhead" type="text" el="106" id="fcategoryid" fn="fcategoryid" refid="ydj_category" ts="" visible="-1" sbm="true" cn="配件类型" lix="25" width="150" />

        <input group="匹配维度" ek="fbillhead" type="text" el="106" id="fcategoryid_d" fn="fcategoryid_d" dispfk="fcategoryid_d" refid="ydj_category" ts="" cn="商品类别" visible="-1" lock="0" lix="45"  width="150" />
        <input group="匹配维度" ek="fbillhead" type="text" el="116" id="fmatchbymodel" fn="fmatchbymodel" dispfk="fmatchbymodel" ts="" cn="按型号匹配" visible="-1" lock="0" lix="45" width="100" />
        <input group="匹配维度" ek="fbillhead" type="text" el="116" id="fmatchbyproduct" fn="fmatchbyproduct" dispfk="fmatchbyproduct" ts="" cn="按商品匹配" visible="-1" lock="0" lix="45" width="100" />
        <input group="基本信息" ek="fbillhead" type="text" xsslv="1" el="100" id="middatajson" ts="" visible="0" cn="中台数据包" lix="25" width="150" len="800000000" />
    </div>

    <!-- 4.1.11.4.1.3匹配维度-型号-单据体 model -->
    <table id="fdimensionmodelentity" el="52" pk="fdimensionmodelentity" tn="t_sel_dimensionmodelentry" pn="fdimensionmodelentity" cn="匹配维度-型号" kfks="fseltypeid">
        <tr>
            <th ek="fdimensionmodelentity" el="107" id="fseltypeid_fumber" fn="fseltypeid_fumber" ctlfk="fseltypeid" dispfk="fnumber" ts="" cn="型号编码" visible="1150" lix="9" width="140"></th>
            <th ek="fdimensionmodelentity" el="106" id="fseltypeid" fn="fseltypeid" refid="sel_type" ts="" cn="型号名称" visible="1150" lix="10" lock="0" must="1" width="200"></th>
        </tr>
    </table>

    <!-- 4.1.11.4.1.4配件映射-单据体 fittingsmap-->
    <table id="ffittingsmapentity" el="52" pk="ffittingsmapentity" tn="t_sel_fittingsmapentry" pn="ffittingsmapentity" cn="配件映射" kfks="fmaterialid">
        <tr>
            <th ek="ffittingsmapentity" el="100" id="fconditions" fn="fconditions" pn="fconditions" cn="条件" xsslv="2" width="300" lock="0" visible="1120" lix="10" xsslv="1">条件</th>
            <th ek="ffittingsmapentity" el="107" id="fmaterialidnew_fumber" fn="fmaterialidnew_fumber" ctlfk="fmaterialidnew" dispfk="fnumber" ts="" cn="商品编码" visible="1150" lix="20" width="150">商品编码</th>
            <th ek="ffittingsmapentity" el="106" id="fmaterialidnew" fn="fmaterialidnew" pn="fmaterialidnew" dispfk="fname" visible="1150" cn="商品名称"
                lock="0" copy="0" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fspecifica" lix="30" width="260">商品名称</th>
            <th ek="ffittingsmapentity" el="107" id="fmaterialid_fumber" fn="fmaterialid_fumber" ctlfk="fmaterialid" dispfk="fnumber" ts="" cn="配件商品编码" visible="1150" lix="20" width="150">商品编码</th>
            <th ek="ffittingsmapentity" el="106" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" dispfk="fname" visible="1150" cn="配件商品名称"
                lock="0" copy="0" notrace="true" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fspecifica" lix="30" must="1" width="260">配件商品名称</th>
            <th ek="ffittingsmapentity" el="132" id="fpropid" fn="fpropid" pn="fpropid" cn="配件辅助属性" ctlfk="fmaterialid" lix="40" lock="0" visible="1150" apipn="attrInfo" canchange="true" width="260"></th>
            <th ek="ffittingsmapentity" el="103" id="fqty" fn="fqty" pn="fqty" cn="数量" width="80" lock="0" visible="1150" lix="50" must="1"></th>
            <th el="116" ek="ffittingsmapentity" id="fdisable" fn="fdisable" pn="fdisable" width="100" cn="是否失效" defval="false" visible="-1" lix="35"></th>
        </tr>
    </table>

    <div id="opList">
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['middatajson']}" permid=""></ul>
    </div>
</body>