{
  //规则引擎基类
  "base": "/mdl/bill.rule.json",
  //定义表单锁定规则
  "lockRules": [
    //此项规则表示：单据状态='' 时(新增的时候)，所有字段可用，只有 新增 保存 新增负责人 替换负责人 移除负责人 操作可用，其他操作都不可用
    {
      "id": "fstatus_",
      "expression": "field:$*;menu:*$tbNew,tbSave|fstatus==''"
    },
    //保存并提交锁定锁定&解锁
    {
      "id": "lock_tbOrderApplyChgRejected",
      "expression": "menu:tbOrderApplyChgRejected|fstatus=='E' or fchangeapplystatus=='03'"
    },
    {
      "id": "unlock_tbOrderApplyChgRejected",
      "expression": "menu:$tbSaveSubmit,tbOrderApplyChgRejected|fstatus=='D' or fchangeapplystatus!='03' or fstatus!='E'"
    },    
    {
      "id": "lock_newField",
      "expression": "field:fnewdeliverydate,fnewaddress,fnewbizqty,fchangeapplyreason,frejectedreason|fchangeapplystatus!=''"
    },
    {
      "id": "unlock_newField",
      "expression": "field:fnewdeliverydate,fnewaddress,fnewbizqty,fchangeapplyreason,frejectedreason|fchangeapplystatus==''"
    }
  ]
}