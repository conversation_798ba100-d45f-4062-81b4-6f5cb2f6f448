<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_purchaseprice" basemodel="bd_basetmpl" el="3" cn="采购价目" isolate="0" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_purchaseprice" pn="fbillhead" cn="采购价目">
        <!--基本信息-->
        <select id="fstatus" el="122" refId="bd_enum" dfld="fenumitem" cg="数据状态" ek="fbillhead" fn="fstatus" pn="fstatus" cn="数据状态" visible="0" lix="27"></select>
        <input el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" cn="创建日期" width="130" visible="1150" copy="0" lix="251" />
        <input el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" cn="创建人" width="130" visible="1150" copy="0" lix="251" />
        <!--重写基类模型中的部分字段属性-->
        <input id="fnumber" el="100" visible="-1" />
        <input id="fname" el="100" visible="0" copy="0" />

        <!--基本信息-->
        <input group="基本信息" el="106" ek="fbillhead" id="fsupplierid" fn="fsupplierid" refid="ydj_supplier" visible="-1" cn="供应商" lix="10" width="300" />
        <input group="基本信息" el="106" ek="fbillhead" id="fproductid" fn="fproductid" refid="ydj_product" visible="0" cn="商品" lix="20" width="300" />
        <input group="基本信息" el="109" ek="fbillhead" id="funitid" fn="funitid" ctlfk="fproductid" refid="ydj_unit" visible="0" cn="单位" lix="30" width="90" lock="-1" />
        <input group="基本信息" el="122" ek="fbillhead" id="ftype" fn="ftype" visible="1150" cn="报价类型" cg="报价类型" refid="bd_enum" dfld="fenumitem" defval="'quote_type_01'" lix="40" width="120" filter="fid<>'quote_type_02'" />
        <input group="基本信息" el="106" ek="fbillhead" id="fdeptid" fn="fdeptid" refid="ydj_dept" visible="1150" cn="定价部门" lix="50" width="300" />

        <!--报价信息-->
        <table id="fentry" el="52" pk="fentryid" tn="t_ydj_purchasepriceentry" pn="fentry" cn="报价信息" kfks="fproductid_e" must="1">
            <tr>
                <th el="106" lix="20" ek="fentry" id="fproductid_e" fn="fproductid_e" cn="商品" refid="ydj_product" multsel="true" notrace="true" sformid="" width="260" visible="-1"></th>
                <th el="107" lix="15" ek="fentry" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" visible="-1" cn="商品编码"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid_e" dispfk="fnumber" refvt="0"></th>
                <th el="107" ek="fentry" id="fmtrlmodel_e" fn="fmtrlmodel_e" visible="1150" cn="规格型号"
                    lock="-1" copy="1" notrace="true" ts="" ctlfk="fproductid_e" dispfk="fspecifica" refvt="0"></th>
                <th el="109" lix="35" ek="fentry" id="funitid_e" fn="funitid_e" refid="ydj_unit" visible="-1" cn="采购单位" ctlfk="fproductid_e" sformid="" notrace="true" width="90"></th>
                <th el="107" lix="25" ek="fentry" id="fcategoryid_e" fn="fcategoryid_e" ctlfk="fproductid_e" dispfk="fcategoryid" ts="" cn="商品类别" sformid="" notrace="true" visible="-1" lock="-1"></th>
                <th el="107" lix="24" ek="fentry" id="fbrandid_e" fn="fbrandid_e" ts="" cn="品牌" sformid="" notrace="true" ctlfk="fproductid_e" dispfk="fbrandid" visible="-1" lock="-1"></th>
                <th el="107" lix="24" ek="fentry" id="fseriesid_e" fn="fseriesid_e" ts="" cn="系列" sformid="" notrace="true" ctlfk="fproductid_e" dispfk="fseriesid" visible="-1" lock="-1"></th>
                <th el="107" lix="24" ek="fentry" id="fsubseriesid" fn="fsubseriesid" cn="子系列" ctlfk="fproductid_e" dispfk="fsubseriesid" visible="1086" sformid="" width="100" lock="-1"></th>
                <th el="132" ek="fentry" id="fattrinfo" fn="fattrinfo" cn="辅助属性" ctlfk="fproductid_e" width="260" visible="1150"></th>
                <!--<th el="104" ek="fentry" id="fprice" fn="fprice" ts="" cn="含税单价" visible="96" width="130" format="0,000.00"></th>
    <th el="102" ek="fentry" id="ftaxrate" fn="ftaxrate" cn="税率" visible="96" format="0,000.00"></th>-->
                <th el="152" lix="45" ek="fentry" id="fconfirmstatus" fn="fconfirmstatus" cn="状态" vals="'1':'未确认','2':'确认'" visible="-1" defval="'1'" lock="-1" copy="0"></th>
                <th el="102" ek="fentry" id="flengthmax" fn="flengthmax" cn="(长/高)上限" width="110" visible="96"></th>
                <th el="102" ek="fentry" id="flengthmin" fn="flengthmin" cn="(长/高)下限" width="110" visible="96"></th>
                <th el="102" ek="fentry" id="fwidthmax" fn="fwidthmax" cn="宽上限" width="100" visible="96"></th>
                <th el="102" ek="fentry" id="fwidthmin" fn="fwidthmin" cn="宽下限" width="100" visible="96"></th>
                <th el="102" ek="fentry" id="fthickmax" fn="fthickmax" cn="(厚/深)上限" width="110" visible="96"></th>
                <th el="102" ek="fentry" id="fthickmin" fn="fthickmin" cn="(厚/深)下限" width="110" visible="96"></th>
                <th el="105" lix="40" ek="fentry" id="fpurprice" fn="fpurprice" cn="采购价" width="100" visible="-1" format="0,000.00"></th>
                <th el="112" ek="fentry" id="fstartdate_e" fn="fstartdate_e" cn="生效日期" defval="@currentshortdate" visible="1150" />
                <th el="112" ek="fentry" id="fexpiredate_e" fn="fexpiredate_e" cn="失效日期" defVal="'2099-01-01'" visible="1150" />
                <th el="113" ek="fentry" id="fconfirmdate" fn="fconfirmdate" cn="确认时间" visible="-1" desc="确认时更新为当前系统时间，取消确认时清空"></th>
                <th el="144" ek="fentry" id="foperate" fn="foperate" cn="操作" visible="1150" lock="-1" width="170" btnid="confirm,confirmyes,cancelconfirm" btntxt="确认,已确认,取消确认" align="right"></th>

                <!--用于接收慕思接口数据，这个字段不存储数据库-->
                <th el="100" ek="fentry" id="fcurrency" fn="" pn="fcurrency" cn="币别" visible="0" desc="用于接收慕思接口数据，这个字段不存储数据库"></th>

                <th el="113" ek="fentry" id="fsyncdate" fn="fsyncdate" cn="同步时间" pn="fsyncdate" visible="-1" lock="-1" desc="记录同步时间"></th>
            </tr>
        </table>



    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" ek="fentry" cn="采购价必须大于0" data="{'expr':'fpurprice>0 ','message':'采购价必须大于0！'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fname','fnumber','fproductid_e','fpurprice','fstartdate_e','fexpiredate_e', 'fconfirmstatus']}" permid=""></ul>
        <ul el="10" id="confirm" op="confirm" opn="确认" data="" permid="ydj_purchaseprice_confirm"></ul>
        <ul el="10" id="cancelconfirm" op="cancelconfirm" opn="取消确认" data="" permid="ydj_purchaseprice_cancelconfirm"></ul>
    </div>

    <!--表单所涉及的权限项定义-->
    <div id="permList">
        <ul el="12" id="ydj_purchaseprice_confirm" cn="确认"></ul>
        <ul el="12" id="ydj_purchaseprice_cancelconfirm" cn="取消确认"></ul>
    </div>
</body>
</html>
