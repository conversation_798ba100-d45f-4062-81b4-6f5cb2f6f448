{
  "Id": "ydj_purchaseorder2pur_receiptnotice",
  "Number": "ydj_purchaseorder2pur_receiptnotice",
  "Name": "采购订单生成收货通知单",
  "SourceFormId": "ydj_purchaseorder",
  "TargetFormId": "pur_receiptnotice",
  "ControlFieldKey": "fqty",
  "SourceControlFieldKey": "freceiptqty",
  "RelationFieldKey": "fsourceentryid",
  "RealtionFormIdFieldKey": "fsourceformid",
  "ActiveEntityKey": "fentity",
  "FilterString": "fqty+freturnqty>freceiptqty and fstatus='E' and fqty>flinkqty and fenablenotice='0' and fcooeditstatus<>'3'",
  "Message": "收货失败：<br>1、采购订单必须是已审核状态！<br>2、至少要有一行商品明细没有完全收货(数量-收货数量+退货数量>0)！<br>3、必须不启用收货通知！<br>4、可能已生成了下游收货通知单，请检查！<br>5、供方状态不可为删除！",
  "FieldMappings": [
    {
      "Id": "fbilltype",
      "Name": "单据类型",
      "MapType": 1,
      "SrcFieldId": "'receiptnotice_type_01'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdate_e",
      "Name": "收货日期",
      "MapType": 0,
      "SrcFieldId": "fpickdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsupplierid_e",
      "Name": "供应商",
      "MapType": 0,
      "SrcFieldId": "fsupplierid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsuppliernumber",
      "Name": "供应商货号",
      "MapType": 0,
      "SrcFieldId": "fsuppliernumber",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpostaffid",
      "Name": "采购员",
      "MapType": 0,
      "SrcFieldId": "fpostaffid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpodeptid",
      "Name": "采购部门",
      "MapType": 0,
      "SrcFieldId": "fpodeptid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkstaffid_e",
      "Name": "发货人",
      "MapType": 0,
      "SrcFieldId": "fsupplierid.fcontacts",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkmobile_e",
      "Name": "发货人电话",
      "MapType": 0,
      "SrcFieldId": "fsupplierid.fphone",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "flinkaddress_e",
      "Name": "发货人地址",
      "MapType": 0,
      "SrcFieldId": "fsupplieraddr",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fdescription",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fdescription",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotalgrossload",
      "Name": "总重量",
      "MapType": 1,
      "SrcFieldId": "0",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotalcubeqty",
      "Name": "总体积",
      "MapType": 1,
      "SrcFieldId": "0",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "ftotalpackageqty",
      "Name": "总件数",
      "MapType": 1,
      "SrcFieldId": "0",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    //表体商品明细字段映射
    {
      "Id": "fmaterialid",
      "Name": "商品",
      "MapType": 0,
      "SrcFieldId": "fmaterialid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fcustomdesc",
      "Name": "定制说明",
      "MapType": 0,
      "SrcFieldId": "fcustomdes_e",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fattrinfo",
      "Name": "辅助属性",
      "MapType": 0,
      "SrcFieldId": "fattrinfo",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "funitid",
      "Name": "基本单位",
      "MapType": 0,
      "SrcFieldId": "funitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fbizunitid",
      "Name": "采购单位",
      "MapType": 0,
      "SrcFieldId": "fbizunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fstockunitid",
      "Name": "库存单位",
      "MapType": 1,
      "SrcFieldId": "fmaterialid.fstockunitid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fplanqty",
      "Name": "基本单位应收数量",
      "MapType": 1,
      "SrcFieldId": "fqty-freceiptqty+freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fqty",
      "Name": "基本单位实收数量",
      "MapType": 1,
      "SrcFieldId": "fqty-freceiptqty+freturnqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fprice",
      "Name": "单价",
      "MapType": 1,
      "SrcFieldId": "fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "famount",
      "Name": "金额",
      "MapType": 1,
      "SrcFieldId": "(fqty-freceiptqty+freturnqty)*fdealprice",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fvolumeqty",
      "Name": "体积",
      "MapType": 1,
      "SrcFieldId": "fmaterialid.fvolume",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fvolume",
      "Name": "总体积",
      "MapType": 1,
      "SrcFieldId": "(fqty-freceiptqty+freturnqty)*fmaterialid.fvolume",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fgrossqty",
      "Name": "毛重",
      "MapType": 1,
      "SrcFieldId": "fmaterialid.fgrossload",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fgross",
      "Name": "总重",
      "MapType": 1,
      "SrcFieldId": "(fqty-freceiptqty+freturnqty)*fmaterialid.fgrossload",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpacksize",
      "Name": "纸箱尺寸",
      "MapType": 1,
      "SrcFieldId": "fmaterialid.fpacksize",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtono",
      "Name": "物流跟踪号",
      "MapType": 0,
      "SrcFieldId": "fmtono",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownertype",
      "Name": "货主类型",
      "MapType": 0,
      "SrcFieldId": "fownertype",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fownerid",
      "Name": "货主",
      "MapType": 0,
      "SrcFieldId": "fownerid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fentrynote",
      "Name": "备注",
      "MapType": 0,
      "SrcFieldId": "fnote",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderno",
      "Name": "采购订单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderinterid",
      "Name": "采购订单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fpoorderentryid",
      "Name": "采购订单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceformid",
      "Name": "来源单类型",
      "MapType": 1,
      "SrcFieldId": "'ydj_purchaseorder'",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourcebillno",
      "Name": "来源单编号",
      "MapType": 0,
      "SrcFieldId": "fbillno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceinterid_h",
      "Name": "来源单内码",
      "MapType": 0,
      "SrcFieldId": "fbillhead.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsourceentryid",
      "Name": "来源单分录内码",
      "MapType": 0,
      "SrcFieldId": "fentity.id",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderdate",
      "Name": "订单日期",
      "MapType": 0,
      "SrcFieldId": "fdate",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "forderqty",
      "Name": "基本单位订单数量",
      "MapType": 0,
      "SrcFieldId": "fqty",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fmtrlimage",
      "Name": "图片",
      "MapType": 0,
      "SrcFieldId": "fmtrlimage",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderno",
      "Name": "销售合同编号",
      "MapType": 0,
      "SrcFieldId": "fsoorderno",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderinterid",
      "Name": "销售合同内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderinterid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    },
    {
      "Id": "fsoorderentryid",
      "Name": "销售合同分录内码",
      "MapType": 0,
      "SrcFieldId": "fsoorderentryid",
      "MapActionWhenGrouping": 0,
      "Order": 0
    }

  ],
  "BillGroups": [
    {
      "Id": "fbillno",
      "Order": 1
    }
  ],
  "FieldGroups": [
    {
      "Id": "fentity_fentryid",
      "Order": 1
    }
  ]
}