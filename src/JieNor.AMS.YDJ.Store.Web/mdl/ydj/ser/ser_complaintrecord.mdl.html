
<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ser_complaintrecord" basemodel="bill_basetmpl" el="1" cn="投诉记录单" IsAsynLstDesc="true">
    <div id="fbillhead" el="51" pk="fid" tn="t_ser_complaintrecord" pn="fbillhead" cn="投诉记录单">

    	<!--基本信息-->
    	<input group="基本信息" el="112" type="date" id="fladingdate" ek="fbillhead" fn="fladingdate" pn="fladingdate" cn="提单日期" visible="-1" defval="@currentshortdate"/>
    	
    	<input group="基本信息" el="100" type="text" id="flinkid" fn="flinkid" ek="fbillhead" ts="" cn="联系人" visible="-1" />
    	
    	<input group="基本信息" el="100" type="text" id="fphone" ek="fbillhead" fn="fphone" ts="" cn="联系电话" visible="-1" />
    	
        <!--<input group="基本信息" el="100" type="text" id="fmerchant" ek="fbillhead" fn="fmerchant" ts="" cn="商户" visible="-1" lock="-1" />-->
        <input group="基本信息" el="106" ek="fbillhead" id="fmerchantid" fn="fmerchantid" pn="fmerchantid" cn="商户" cg="商户" refid="ydj_customer" filter="fcustype='customercate_03'" visible="-1" lock="-1" />
    	<input group="基本信息" el="100" type="text" id="frelabill" ek="fbillhead" fn="frelabill" ts="" cn="商户单号" visible="-1" lock="-1" />
    	<input group="基本信息" el="100" type="text" id="fmerorderid" ek="fbillhead" fn="fmerorderid" ts="" cn="商户订单id" visible="0" lock="-1" />
    	
    	<input group="基本信息" el="100" type="text" id="fcomplain" ek="fbillhead" fn="fcomplain" ts="" cn="投诉内容" visible="-1" />
    	
    	
    	<!--结论措施-->
    	<input group="结论措施" el="100" type="text" id="freason" ek="fbillhead" fn="freason" ts="" cn="原因调查" visible="-1" />
    	
    	<input group="结论措施" el="100" type="text" id="fmeasure" ek="fbillhead" fn="fmeasure" ts="" cn="措施" visible="-1" />

        <input group="结论措施" el="122" id="fcomplainstatus" ek="fbillhead" fn="fcomplainstatus" refId="bd_enum" dfld="fenumitem" defval="'complain_status01'" ts="" cg="投诉单状态" visible="-1" lock="-1" cn="状态" />
    </div>

</body>
</html>