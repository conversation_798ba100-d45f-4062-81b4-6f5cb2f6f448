using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Consumer.API.Utils
{
    /// <summary>
    /// 签名验证类
    /// </summary>
    public class CheckSignature
    {
        /// <summary>
        /// 有效分钟数，默认为5
        /// </summary>
        public static int ExpiredMinutes = 5;

        /// <summary>检查签名是否正确</summary>
        /// <param name="signature">签名</param>
        /// <param name="timestamp">时间戳（milliseconds）</param>
        /// <param name="nonce">随机数</param>
        /// <param name="secret">密钥</param>
        /// <returns></returns>
        public static bool Check(string signature, string timestamp, string nonce, string secret)
        {
            // 5分钟内有效
            if ((DateTime.Now - TimeStampToDateTime(timestamp)).TotalMinutes > ExpiredMinutes)
            {
                return false;
            }

            return signature == CheckSignature.GetSignature(timestamp, nonce, secret);
        }

        /// <summary>
        /// 时间戳转时间
        /// </summary>
        /// <param name="timestamp">时间戳</param>
        /// <returns></returns>
        private static DateTime TimeStampToDateTime(string timestamp)
        {
            if (!int.TryParse(timestamp, out var ts))
            {
                return new DateTime(1900, 1, 1);
            }

            return DateTimeOffset.FromUnixTimeMilliseconds(ts).DateTime;
        }

        /// <summary>返回正确的签名</summary>
        /// <param name="timestamp">时间戳（milliseconds）</param>
        /// <param name="nonce">随机数</param>
        /// <param name="secret">密钥</param>
        /// <returns></returns>
        public static string GetSignature(string timestamp, string nonce, string secret)
        {
            var values = new string[3]
            {
                secret,
                timestamp,
                nonce
            };

            // 按ASCII码
            Array.Sort(values, string.CompareOrdinal);

            byte[] hash = SHA1.Create().ComputeHash(Encoding.UTF8.GetBytes(string.Join("", values)));
            StringBuilder stringBuilder = new StringBuilder();
            foreach (byte num in hash)
                stringBuilder.AppendFormat("{0:x2}", (object)num);
            return stringBuilder.ToString();
        }
    }
}