using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Core.DataEntity.Inventory
{
    /// <summary>
    /// 库存数据模型
    /// </summary>
    [Serializable]
    public class InventoryModel
    {
        /// <summary>
        /// 唯一标识
        /// </summary>
        public string ClientId { get; set; }

        /// <summary>
        /// 商品Id
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 库存量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 预留量
        /// </summary>
        public decimal ReserveQty { get; set; }

        /// <summary>
        /// 在途量
        /// </summary>
        public decimal IntransitQty { get; set; }

        /// <summary>
        /// 可用量
        /// </summary>
        public decimal UsableQty { get; set; }

        /// <summary>
        /// 清库存
        /// </summary>
        public bool IsClearStock { get; set; }

        /// <summary>
        /// 库存数量明细
        /// </summary>
        public List<InventoryQtyDetailModel> List { get; set; }
    }

    /// <summary>
    /// 库存明细数据模型
    /// </summary>
    [Serializable]
    public class InventoryDetailModel : InventorySummaryModel
    {
        ///// <summary>
        ///// 商品id
        ///// </summary>
        //public string ProductId { get; set; }

        ///// <summary>
        ///// 商品名称
        ///// </summary>
        //public string ProductName { get; set; }

        ///// <summary>
        ///// 商品编码
        ///// </summary>
        //public string ProductNumber { get; set; }

        ///// <summary>
        ///// 云链id
        ///// </summary>
        //public string ChainDataId { get; set; }

        ///// <summary>
        ///// 辅助属性值
        ///// </summary>
        //public string AuxPropValue { get; set; }

        ///// <summary>
        ///// 辅助属性
        ///// </summary>
        //public JArray AuxPropVals { get; set; }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specifica { get; set; }

        ///// <summary>
        ///// 清库存
        ///// </summary>
        //public bool IsClearStock { get; set; }

        ///// <summary>
        ///// 库存量
        ///// </summary>
        //public decimal Qty { get; set; }

        ///// <summary>
        ///// 预留量
        ///// </summary>
        //public decimal ReserveQty { get; set; }

        ///// <summary>
        ///// 在途量
        ///// </summary>
        //public decimal IntransitQty { get; set; }

        ///// <summary>
        ///// 可用量
        ///// </summary>
        //public decimal UsableQty { get; set; }

        /// <summary>
        /// 仓库id
        /// </summary>
        public string StoreHouseId { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string StoreHouseName { get; set; }

        /// <summary>
        /// 仓位id
        /// </summary>
        public string StoreLocationId { get; set; }

        /// <summary>
        /// 仓位名称
        /// </summary>
        public string StoreLocationName { get; set; }

        ///// <summary>
        ///// 库存状态id
        ///// </summary>
        //public string StockStatusId { get; set; }

        ///// <summary>
        ///// 库存状态名称
        ///// </summary>
        //public string StockStatusName { get; set; }

        /// <summary>
        /// 批号
        /// </summary>
        public string LotNo { get; set; }

        /// <summary>
        /// 物流跟踪号
        /// </summary>
        public string MtoNo { get; set; }

        /// <summary>
        /// 定制描述
        /// </summary>
        public string CustomDesc { get; set; }

        /// <summary>
        /// 货主类型
        /// </summary>
        public string OwnerTypeId { get; set; }

        /// <summary>
        /// 货主类型名称
        /// </summary>
        public string OwnerTypeName { get; set; }

        /// <summary>
        /// 货主
        /// </summary>
        public string OwnerId { get; set; }

        /// <summary>
        /// 货主名称
        /// </summary>
        public string OwnerName { get; set; }

        /// <summary>
        /// 属性
        /// </summary>
        public string Attribute { get; set; }

        /// <summary>
        /// 品牌id
        /// </summary>
        public string BrandId { get; set; }

        /// <summary>
        /// 品牌名称
        /// </summary>
        public string BrandName { get; set; }

        /// <summary>
        /// 系列id
        /// </summary>
        public string SeriesId { get; set; }

        /// <summary>
        /// 系列名称
        /// </summary>
        public string SeriesName { get; set; }

        /// <summary>
        /// 品类id
        /// </summary>
        public string CategoryId { get; set; }

        /// <summary>
        /// 品类名称
        /// </summary>
        public string CategoryName { get; set; }

        /// <summary>
        /// 基本单位id
        /// </summary>
        public string UnitId { get; set; }

        /// <summary>
        /// 基本单位名称
        /// </summary>
        public string UnitName { get; set; }

        /// <summary>
        /// 库存单位id
        /// </summary>
        public string StockUnitId { get; set; }

        /// <summary>
        /// 库存单位名称
        /// </summary>
        public string StockUnitName { get; set; }

        public string Mainorgid { get; set; }
        ///// <summary>
        ///// 商品图片id
        ///// </summary>
        //public string ImageId { get; set; }

        ///// <summary>
        ///// 商品图片name
        ///// </summary>
        //public string ImageTxt { get; set; }

        ///// <summary>
        ///// 销售价
        ///// </summary>
        //public decimal SalPrice { get; set; }
    }

    /// <summary>
    /// 库存汇总数据模型
    /// </summary>
    [Serializable]
    public class InventorySummaryModel
    {
        /// <summary>
        /// 商品id
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 商品名称
        /// </summary>
        public string ProductName { get; set; }

        /// <summary>
        /// 商品编码
        /// </summary>
        public string ProductNumber { get; set; }

        /// <summary>
        /// 云链id
        /// </summary>
        public string ChainDataId { get; set; }

        /// <summary>
        /// 辅助属性组合值ID
        /// </summary>
        public string AuxPropValId { get; set; }

        /// <summary>
        /// 辅助属性值
        /// </summary>
        public string AuxPropValue { get; set; }

        /// <summary>
        /// 辅助属性
        /// </summary>
        public JArray AuxPropVals { get; set; } = new JArray();

        /// <summary>
        /// 清库存
        /// </summary>
        public bool IsClearStock { get; set; }

        /// <summary>
        /// 库存量
        /// </summary>
        public string Qty { get; set; }

        /// <summary>
        /// 预留量
        /// </summary>
        public string ReserveQty { get; set; }

        /// <summary>
        /// 在途量
        /// </summary>
        public string IntransitQty { get; set; }

        /// <summary>
        /// 可用量
        /// </summary>
        public string UsableQty { get; set; }

        /// <summary>
        /// 库存状态id
        /// </summary>
        public string StockStatusId { get; set; }

        /// <summary>
        /// 库存状态名称
        /// </summary>
        public string StockStatusName { get; set; }

        /// <summary>
        /// 商品图片id
        /// </summary>
        public string ImageId { get; set; }

        /// <summary>
        /// 商品图片name
        /// </summary>
        public string ImageTxt { get; set; }

        /// <summary>
        /// 销售价
        /// </summary>
        public decimal SalPrice { get; set; }

        /// <summary>
        /// 是否样品
        /// </summary>
        public bool IsSample { get; set; }

        /// <summary>
        /// 非标产品
        /// </summary>
        public bool IsNonStandard { get; set; }

        /// <summary>
        /// 是否套件
        /// </summary>
        public bool IsSuite { get; set; }

        /// <summary>
        /// 是否赠品
        /// </summary>
        public bool Isgiveaway { get; set; }
        public string Dataorigin { get; set; }
        public DateTime Orderdate { get; set; }
    }

    /// <summary>
    /// 库存数量明细数据模型
    /// </summary>
    [Serializable]
    public class InventoryQtyDetailModel
    {
        /// <summary>
        /// 库存量
        /// </summary>
        public decimal Qty { get; set; }

        /// <summary>
        /// 预留量
        /// </summary>
        public decimal ReserveQty { get; set; }

        /// <summary>
        /// 在途量
        /// </summary>
        public decimal IntransitQty { get; set; }

        /// <summary>
        /// 可用量
        /// </summary>
        public decimal UsableQty { get; set; }

        /// <summary>
        /// 仓库id
        /// </summary>
        public string StoreHouseId { get; set; }

        /// <summary>
        /// 仓库名称
        /// </summary>
        public string StoreHouseName { get; set; }

        /// <summary>
        /// 仓位id
        /// </summary>
        public string StoreLocationId { get; set; }

        /// <summary>
        /// 仓位名称
        /// </summary>
        public string StoreLocationName { get; set; }

        /// <summary>
        /// 库存状态id
        /// </summary>
        public string StockStatusId { get; set; }

        /// <summary>
        /// 库存状态名称
        /// </summary>
        public string StockStatusName { get; set; }
    }
}
