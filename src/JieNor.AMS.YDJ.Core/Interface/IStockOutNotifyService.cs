using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Core.Interface
{
    /// <summary>
    /// 销售出库单通知服务
    /// </summary>
    public interface IStockOutNotifyService
    {
        void Notify(UserContext userContext, DynamicObject[] argDataEntities, HtmlForm htmlForm, string notifyType);
    }
}
