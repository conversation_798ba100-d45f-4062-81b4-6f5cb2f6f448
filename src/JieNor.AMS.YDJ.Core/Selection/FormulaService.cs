using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;

namespace JieNor.AMS.YDJ.Core
{
    /// <summary>
    /// 公式服务接口实现
    /// </summary>
    [InjectService]
    public class FormulaService : IFormulaService
    {
        #region 公有方法

        /// <summary>
        /// 检查指定公式的语法是否符合要求，符合返回 true 不符合返回 false，参数 formula 为空时直接返回 false
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">待检查的公式</param>
        /// <param name="errorMessage">检查出错时的错误信息</param>
        /// <returns>返回公式的语法是否符合要求</returns>
        public bool CheckSyntax(UserContext userCtx, string formula, out string errorMessage)
        {
            errorMessage = string.Empty;

            if (formula.IsNullOrEmptyOrWhiteSpace()) return false;

            this.TryExecuteExpr<object>(userCtx, formula, out errorMessage);

            return errorMessage.IsNullOrEmptyOrWhiteSpace();
        }

        /// <summary>
        /// 将指定的公式转换为 Python 函数表达式
        /// </summary>
        /// <param name="formula">待转换的公式</param>
        /// <returns>返回转换后的 Python 函数表达式</returns>
        public string ConvertToPythonFunc(string formula)
        {
            var sbPython = new StringBuilder();
            sbPython.AppendLine("def func1():");
            sbPython.AppendLine(string.Format("    return {0};", formula));
            sbPython.AppendLine("func1()");
            return sbPython.ToString();
        }

        /// <summary>
        /// 尝试执行指定的表达式，并且返回表达式执行的结果值，内部会自动调用 ConvertToPythonFunc 将表达式转换成 Python 表达式
        /// </summary>
        /// <typeparam name="T">表达式返回的结果值的类型</typeparam>
        /// <param name="userCtx">上下文</param>
        /// <param name="expr">表达式</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>返回表达式的结果值</returns>
        public T TryExecuteExpr<T>(UserContext userCtx, string expr, out string errorMessage)
        {
            expr = this.FormulaReplace(expr);

            expr = this.ConvertToPythonFunc(expr);

            return this.TryExecutePythonExpr<T>(userCtx, expr, out errorMessage);
        }

        /// <summary>
        /// 尝试执行指定的 Python 表达式，并且返回表达式执行的结果值
        /// </summary>
        /// <typeparam name="T">表达式返回的结果值的类型</typeparam>
        /// <param name="userCtx">上下文</param>
        /// <param name="expr">Python 表达式</param>
        /// <param name="errorMessage">错误信息</param>
        /// <returns>返回表达式的结果值</returns>
        public T TryExecutePythonExpr<T>(UserContext userCtx, string expr, out string errorMessage)
        {
            errorMessage = "";
            try
            {
                return Evaluator.ExcutePython<T>(expr);
            }
            catch (Microsoft.Scripting.SyntaxErrorException ex)
            {
                errorMessage = "第" + (ex.Line - 1) + "行 ：" + ex.Message;
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
            }
            return default(T);
        }

        /// <summary>
        /// 尝试解析和执行指定的属性公式，并且返回公式执行的结果值
        /// 公式示例1："[床架类别]"=="底座床架"
        /// 公式示例2："[床架长]"==103 or "[床架长]"==204
        /// </summary>
        /// <typeparam name="T">公式返回的结果值的类型</typeparam>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>、
        /// <param name="propDatas">属性数据包集合</param>
        /// <param name="propList">属性实体集合</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="propDatas">
        /// 公式中要用到的属性数据包集合，该参数为空时将自动查询数据库，
        /// 所以如果是在循环中调用该方法，建议在循环前准备好公式用到的所有属性数据包，
        /// 避免循环调用该方法时导致的循环访问数据库。
        /// </param>
        /// <returns>返回公式执行的结果</returns>
        public T TryParseAndExecutePropFormula<T>(
            UserContext userCtx, 
            string formula, 
            List<PropEntity> propList, 
            out string errorMessage, 
            List<DynamicObject> propDatas)
        {
            errorMessage = "";

            if (formula.IsNullOrEmptyOrWhiteSpace()
                || propList == null
                || !propList.Any())
            {
                return default(T);
            }

            //将公式中的 "[床架长]" 替换为对应的属性值
            foreach (var item in propList)
            {
                if (item.PropName.IsNullOrEmptyOrWhiteSpace()
                    || item.ValueName.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }
                //如果泛型是decimal是在解析计算公式，如果是bool是在解析条件
                if (typeof(T) == typeof(decimal))
                {
                    //解析计算公式时 只处理属性值为数值类型的，否则没有意义
                    if (item.PropValueDataType == PropValueDataTypeEnum.Numeric)
                    {
                        formula = formula.Replace($"[{item.PropName.Trim()}]", item.ValueName.Trim());
                    } 
                }
                else 
                {
                    if (item.PropValueDataType == PropValueDataTypeEnum.Numeric)
                    {
                        formula = formula.Replace($"\"[{item.PropName.Trim()}]\"", item.ValueName.Trim());
                    }
                    else
                    {
                        formula = formula.Replace($"[{item.PropName.Trim()}]", item.ValueName.Trim());
                    }
                }
            }

            //找出未被替换掉的属性名称
            var unknownPropNames = this.ParseFormulaPropNames(formula);

            //尝试将未被替换掉的属性名称替换为一个固定的特殊名称，目的是为了让公式表达式符合语法要求且表达式不成立
            if (unknownPropNames.Any())
            {
                //匹配数据库时需要去一下空格（防止公式中存在空格）
                var _unknownPropNames = unknownPropNames.Select(o => o.Trim());

                //根据属性名称加载属性信息
                if (propDatas == null)
                {
                    propDatas = userCtx.LoadBizBillHeadDataByACLFilter(
                        "sel_prop",
                        $"fname in('{string.Join("','", _unknownPropNames)}')",
                        "fname,fnumber,fdatatype");
                }
                if (propDatas != null && propDatas.Any())
                {
                    foreach (var unknownPropName in unknownPropNames)
                    {
                        var propData = propDatas.FirstOrDefault(o =>
                            Convert.ToString(o["fname"]).Trim().EqualsIgnoreCase(unknownPropName.Trim()));
                        if (propData == null) continue;

                        //属性的数据类型
                        var dataType = Convert.ToString(propData["fdatatype"]).Trim();
                        switch (dataType)
                        {
                            case "1": //字符
                                formula = formula.Replace($"[{unknownPropName}]", "未知的属性值");
                                break;
                            case "2": //数值
                                formula = formula
                                    .Replace($"\"[{unknownPropName}]\"<=", $"{int.MaxValue} <= ")
                                    .Replace($"\"[{unknownPropName}]\" <=", $"{int.MaxValue} <= ")
                                    .Replace($"\"[{unknownPropName}]\"<", $"{int.MaxValue} < ")
                                    .Replace($"\"[{unknownPropName}]\" <", $"{int.MaxValue} < ")
                                    .Replace($"\"[{unknownPropName}]\">=", $"{int.MinValue} >= ")
                                    .Replace($"\"[{unknownPropName}]\" >=", $"{int.MinValue} >= ")
                                    .Replace($"\"[{unknownPropName}]\">", $"{int.MinValue} > ")
                                    .Replace($"\"[{unknownPropName}]\" >", $"{int.MinValue} > ")
                                    .Replace($"\"[{unknownPropName}]\"==", $"{int.MaxValue} == ")
                                    .Replace($"\"[{unknownPropName}]\" ==", $"{int.MaxValue} == ");
                                break;
                        }
                    }
                }
            }

            //执行公式
            var result = this.TryExecuteExpr<T>(userCtx, formula, out errorMessage);

            return result;
        }

        /// <summary>
        /// 尝试解析和执行指定的属性公式，并且返回公式执行的结果值
        /// 公式示例1："[床架类别]"=="底座床架"
        /// 公式示例2："[床架长]"==103 or "[床架长]"==204
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="propDatas">
        /// 公式中要用到的属性数据包集合，该参数为空时将自动查询数据库，
        /// 所以如果是在循环中调用该方法，建议在循环前准备好公式用到的所有属性数据包，
        /// 避免循环调用该方法时导致的循环访问数据库。
        /// </param>
        /// <returns>返回转换后的SQL条件</returns>
        public string TryPropFormulaConvertToSqlWhere(UserContext userCtx, string formula, List<DynamicObject> propDatas = null)
        {
            if (formula.IsNullOrEmptyOrWhiteSpace()) return "";

            var propNames = this.ParseFormulaPropNames(formula);
            if (!propNames.Any()) return "";

            //匹配数据库时需要去一下空格（防止公式中存在空格）
            var _propNames = propNames.Select(o => o.Trim());

            //根据属性名称加载属性信息
            if (propDatas == null)
            {
                propDatas = userCtx.LoadBizBillHeadDataByACLFilter(
                    "sel_prop",
                    $"fname in('{string.Join("','", _propNames)}')",
                    "fname,fnumber,fdatatype");
            }
            if (propDatas == null || !propDatas.Any()) return "";

            foreach (var propName in propNames)
            {
                var propNameTrim = propName.Trim();
                var propData = propDatas.FirstOrDefault(o =>
                    Convert.ToString(o["fname"]).Trim().EqualsIgnoreCase(propNameTrim));
                if (propData == null) continue;

                var propId = Convert.ToString(propData["id"]).Trim();

                //属性的数据类型
                var dataType = Convert.ToString(propData["fdatatype"]).Trim();
                switch (dataType)
                {
                    case "1": 
                        //字符
                        //将 "[床架类别]" 替换为 p.fname='床架类别' and p.fid='801033780533006345' and pv.fname
                        formula = formula.Replace($"\"[{propName}]\"", $"p.fname='{propNameTrim}' and p.fid='{propId}' and pv.fname");
                        break;
                    case "2":
                        //数值
                        //将 "[床架长]" 替换为 p.fname='床架长' and p.fid='801033780533006345' and dbo.TryToDecimal(pv.fname,{int.MaxValue})
                        var maxStr = $"p.fname='{propNameTrim}' and p.fid='{propId}' and dbo.TryToDecimal(pv.fname,{int.MaxValue})";
                        var minStr = $"p.fname='{propNameTrim}' and p.fid='{propId}' and dbo.TryToDecimal(pv.fname,{int.MinValue})";
                        formula = formula
                            .Replace($"\"[{propName}]\"<=", $"{maxStr} <= ")
                            .Replace($"\"[{propName}]\" <=", $"{maxStr} <= ")
                            .Replace($"\"[{propName}]\"<", $"{maxStr} < ")
                            .Replace($"\"[{propName}]\" <", $"{maxStr} < ")
                            .Replace($"\"[{propName}]\">=", $"{minStr} >= ")
                            .Replace($"\"[{propName}]\" >=", $"{minStr} >= ")
                            .Replace($"\"[{propName}]\">", $"{minStr} > ")
                            .Replace($"\"[{propName}]\" >", $"{minStr} > ")
                            .Replace($"\"[{propName}]\"==", $"{maxStr} = ")
                            .Replace($"\"[{propName}]\" ==", $"{maxStr} = ");
                        break;
                }
            }

            //将双引号替换为单引号
            var sqlWhere = formula
                .Replace("\"", "'")
                .Replace("==", " = ")
                .Replace("||", " or ")
                .Replace("&&", " and ");

            return sqlWhere;
        }

        /// <summary>
        /// 尝试解析和执行指定的慕思SAP属性公式，并且返回公式执行的结果值
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <typeparam name="T">公式返回的结果值的类型</typeparam>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="propList">属性实体集合</param>
        /// <param name="errorMessage">错误信息</param>
        /// <param name="propDatas">
        /// 公式中要用到的属性数据包集合，该参数为空时将自动查询数据库，
        /// 所以如果是在循环中调用该方法，建议在循环前准备好公式用到的所有属性数据包，
        /// 避免循环调用该方法时导致的循环访问数据库。
        /// </param>
        /// <returns>返回公式执行的结果</returns>
        public T TryParseAndExecutePropFormulaForMusiSap<T>(
            UserContext userCtx,
            string formula,
            List<PropEntity> propList,
            out string errorMessage,
            List<DynamicObject> propDatas = null)
        {
            errorMessage = "";

            if (formula.IsNullOrEmptyOrWhiteSpace()
                || propList == null
                || !propList.Any())
            {
                return default(T);
            }

            //编译慕思SAP公式
            formula = this.CompileMusiSapFormula(formula);

            //公式中的属性编码集合
            var formulaPropNumbers = this.ParseMusiSapFormulaPropNumbers(formula, false).Keys.ToList();

            //选配的属性编码集合
            var propNumbers = new List<string>();

            //将公式中的 S005 替换为对应的属性值
            foreach (var item in propList)
            {
                if (item.PropNumber.IsNullOrEmptyOrWhiteSpace()
                    || item.ValueNumber.IsNullOrEmptyOrWhiteSpace())
                {
                    continue;
                }

                item.PropNumber = item.PropNumber.Trim();
                item.ValueNumber = item.ValueNumber.Trim();
                propNumbers.Add(item.PropNumber);

                if (item.PropValueDataType == PropValueDataTypeEnum.Numeric)
                {
                    formula = formula.Replace($"{item.PropNumber}", $"{item.ValueNumber}");
                }
                else
                {
                    formula = formula.Replace($"{item.PropNumber}", $"'{item.ValueNumber}'");
                }
            }

            //找出未被替换掉的属性编码
            var unknownPropNumbers = new List<string>();
            foreach (var formulaPropNumber in formulaPropNumbers)
            {
                if (!propNumbers.Contains(formulaPropNumber, StringComparer.OrdinalIgnoreCase))
                {
                    unknownPropNumbers.Add(formulaPropNumber);
                }
            }

            //尝试将未被替换掉的属性编码替换为一个固定的特殊名称，目的是为了让公式表达式符合语法要求且表达式不成立
            if (unknownPropNumbers.Any())
            {
                //根据属性编码加载属性信息
                if (propDatas == null)
                {
                    propDatas = userCtx.LoadBizBillHeadDataByACLFilter(
                        "sel_prop",
                        $"fnumber in('{string.Join("','", unknownPropNumbers)}')",
                        "fname,fnumber,fdatatype");
                }
                if (propDatas != null && propDatas.Any())
                {
                    foreach (var unknownPropNumber in unknownPropNumbers)
                    {
                        var propData = propDatas.FirstOrDefault(o =>
                            Convert.ToString(o["fnumber"]).Trim().EqualsIgnoreCase(unknownPropNumber));
                        if (propData == null) continue;

                        //属性的数据类型
                        var dataType = Convert.ToString(propData["fdatatype"]).Trim();
                        switch (dataType)
                        {
                            case "1": //字符
                                formula = formula.Replace($"{unknownPropNumber}", "'未知的属性值'");
                                break;
                            case "2": //数值
                                formula = formula
                                    .Replace($"{unknownPropNumber}<>", $"{int.MaxValue} <> ")
                                    .Replace($"{unknownPropNumber}<=", $"{int.MaxValue} <= ")
                                    .Replace($"{unknownPropNumber} <=", $"{int.MaxValue} <= ")
                                    .Replace($"{unknownPropNumber}<", $"{int.MaxValue} < ")
                                    .Replace($"{unknownPropNumber} <", $"{int.MaxValue} < ")
                                    .Replace($"{unknownPropNumber}>=", $"{int.MinValue} >= ")
                                    .Replace($"{unknownPropNumber} >=", $"{int.MinValue} >= ")
                                    .Replace($"{unknownPropNumber}>", $"{int.MinValue} > ")
                                    .Replace($"{unknownPropNumber} >", $"{int.MinValue} > ")
                                    .Replace($"{unknownPropNumber}=", $"{int.MaxValue} == ")
                                    .Replace($"{unknownPropNumber} =", $"{int.MaxValue} == ");
                                break;
                        }
                    }
                }
            }

            //执行公式
            var result = this.TryExecuteExpr<T>(userCtx, formula, out errorMessage);

            return result;
        }

        /// <summary>
        /// 尝试将指定的慕思SAP属性公式转换为SQL形式的属性值过滤条件
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="restrictedPropNumber">被约束的属性编码</param>
        /// <param name="propDatas">
        /// 公式中要用到的属性数据包集合，该参数为空时将自动查询数据库，
        /// 所以如果是在循环中调用该方法，建议在循环前准备好公式用到的所有属性数据包，
        /// 避免循环调用该方法时导致的循环访问数据库。
        /// </param>
        /// <returns>返回转换后的SQL条件</returns>
        public string TryMusiSapPropFormulaConvertToSqlWhere(UserContext userCtx, string formula, string restrictedPropNumber, List<DynamicObject> propDatas = null)
        {
            if (formula.IsNullOrEmptyOrWhiteSpace()) return "";

            //编译慕思SAP公式
            formula = this.CompileMusiSapFormula(formula);

            //公式中的属性编码集合
            var formulaPropNumbers = this.ParseMusiSapFormulaPropNumbers(formula, false).Keys.ToList();

            //如果被约束的属性编码不在约束值公式中，则不用处理
            if (!formulaPropNumbers.Contains(restrictedPropNumber, StringComparer.OrdinalIgnoreCase))
            {
                return "";
            }

            //根据属性编码加载属性信息
            if (propDatas == null && formulaPropNumbers.Any())
            {
                propDatas = userCtx.LoadBizBillHeadDataByACLFilter(
                    "sel_prop",
                    $"fnumber in('{string.Join("','", formulaPropNumbers)}')",
                    "fname,fnumber,fdatatype");
            }
            if (propDatas == null || !propDatas.Any()) return "";

            foreach (var formulaPropNumber in formulaPropNumbers)
            {
                var propData = propDatas.FirstOrDefault(o =>
                    Convert.ToString(o["fnumber"]).Trim().EqualsIgnoreCase(formulaPropNumber));
                if (propData == null) continue;

                var propId = Convert.ToString(propData["id"]).Trim();

                //属性的数据类型
                var dataType = Convert.ToString(propData["fdatatype"]).Trim();
                switch (dataType)
                {
                    case "1":
                        //字符
                        //将 S005 替换为 p.fnumber='S005' and p.fid='801033780533006345' and pv.fnumber
                        formula = formula.Replace($"{formulaPropNumber}", $"p.fnumber='{formulaPropNumber}' and p.fid='{propId}' and pv.fnumber");
                        break;
                    case "2":
                        //数值
                        //将 S005 替换为 p.fnumber='S005' and p.fid='801033780533006345' and dbo.TryToDecimal(pv.fnumber,{int.MaxValue})
                        var maxStr = $"p.fnumber='{formulaPropNumber}' and p.fid='{propId}' and dbo.TryToDecimal(pv.fnumber,{int.MaxValue})";
                        var minStr = $"p.fnumber='{formulaPropNumber}' and p.fid='{propId}' and dbo.TryToDecimal(pv.fnumber,{int.MinValue})";
                        formula = formula
                            .Replace($"{formulaPropNumber}<=", $"{maxStr} <= ")
                            .Replace($"{formulaPropNumber} <=", $"{maxStr} <= ")
                            .Replace($"{formulaPropNumber}<", $"{maxStr} < ")
                            .Replace($"{formulaPropNumber} <", $"{maxStr} < ")
                            .Replace($"{formulaPropNumber}>=", $"{minStr} >= ")
                            .Replace($"{formulaPropNumber} >=", $"{minStr} >= ")
                            .Replace($"{formulaPropNumber}>", $"{minStr} > ")
                            .Replace($"{formulaPropNumber} >", $"{minStr} > ")
                            .Replace($"{formulaPropNumber}=", $"{maxStr} = ")
                            .Replace($"{formulaPropNumber} =", $"{maxStr} = ");
                        break;
                }
            }

            return formula;
        }

        /// <summary>
        /// 尝试将指定的慕思SAP属性公式中的属性编码和属性值编码转换为属性名称和属性值名称
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formula">公式字符串</param>
        /// <param name="propDatas">公式中要用到的属性数据包集合</param>
        /// <param name="restrictedPropNumber">被约束的属性编码</param>
        /// <returns>返回转换后的公式</returns>
        public string TryTranslateMusiSapPropFormula(UserContext userCtx, string formula, List<DynamicObject> propDatas, string restrictedPropNumber = "")
        {
            if (formula.IsNullOrEmptyOrWhiteSpace()
                 || propDatas == null
                 || !propDatas.Any()) return formula;

            //是否只返回公式中属性值编码对应的属性值名称
            var onlyReturnPropValueName = !restrictedPropNumber.IsNullOrEmptyOrWhiteSpace();

            //编译慕思SAP公式
            var formulaCompile = this.CompileMusiSapFormula(formula);
            var formulaTranslate = this.CompileMusiSapFormula(formula, (_propNumber, _propValueNumbers) => 
            {
                //统一加上单引号，方便下面的替换
                _propValueNumbers = _propValueNumbers.Select(o => $"'{o.TrimStart('\'').TrimEnd('\'')}'").ToList();
                return $"{_propNumber}=({string.Join(" OR ", _propValueNumbers)})";
            });

            //公式中的属性编码集合
            var propNumberKv = this.ParseMusiSapFormulaPropNumbers(formulaCompile, false);
            var propNumbers = propNumberKv.Keys.ToList();

            //只处理指定的属性编码
            if (onlyReturnPropValueName)
            {
                propNumbers = propNumbers.Where(o => o.EqualsIgnoreCase(restrictedPropNumber)).ToList();
            }
            if (!propNumbers.Any()) return formula;

            var propValueNumbers = new List<string>();
            foreach (var propNumber in propNumbers)
            {
                propValueNumbers.AddRange(propNumberKv[propNumber]);
            }
            if (!propValueNumbers.Any()) return formula;

            if (onlyReturnPropValueName)
            {
                var propValueNames = this.LoadPropValueNamesByNumbers(userCtx, propNumbers, propValueNumbers);
                if (!propValueNames.Any())
                {
                    //如果没有查到属性值名称，则直接返回原公式
                    return formula;
                }
                return string.Join(" 或 ", propValueNames);
            }

            var propObjs = this.LoadPropValueInfoByNumbers(userCtx, propNumbers, propValueNumbers);
            if (propObjs == null || !propObjs.Any()) return formula;

            foreach (var item in propObjs)
            {
                var dataType = Convert.ToString(item["fdatatype"]).Trim();
                var propNumber = Convert.ToString(item["fnumber"]).Trim();
                var propName = Convert.ToString(item["fname"]).Trim();
                var propValueNumber = Convert.ToString(item["propValueNumber"]).Trim();
                var propValueName = Convert.ToString(item["propValueName"]).Trim();

                //将公式中的属性编码替换为属性名称，属性值编码替换为属性值名称
                switch (dataType)
                {
                    case "1": //字符
                        formulaTranslate = formulaTranslate
                            .Replace($"{propNumber}='{propValueNumber}'", $"{propName}={propValueName}")
                            .Replace($"{propNumber} = '{propValueNumber}'", $"{propName}={propValueName}")
                            .Replace($"'{propValueNumber}'", $"{propValueName}");
                        break;
                    case "2": //数值
                        formulaTranslate = formulaTranslate
                            .Replace($"{propNumber}<={propValueNumber}", $"{propName}<={propValueName}")
                            .Replace($"{propNumber} <= {propValueNumber}", $"{propName}<={propValueName}")
                            .Replace($"{propNumber}<{propValueNumber}", $"{propName}<{propValueName}")
                            .Replace($"{propNumber} < {propValueNumber}", $"{propName}<{propValueName}")
                            .Replace($"{propNumber}>={propValueNumber}", $"{propName}>={propValueName}")
                            .Replace($"{propNumber} >= {propValueNumber}", $"{propName}>={propValueName}")
                            .Replace($"{propNumber}>{propValueNumber}", $"{propName}>{propValueName}")
                            .Replace($"{propNumber} > {propValueNumber}", $"{propName}>{propValueName}")
                            .Replace($"{propNumber}={propValueNumber}", $"{propName}={propValueName}")
                            .Replace($"{propNumber} = {propValueNumber}", $"{propName}={propValueName}")
                            .Replace($"'{propValueNumber}'", $"{propValueName}");
                        break;
                }

                //最后将属性编码替换为属性名称
                formulaTranslate = formulaTranslate
                    .Replace($"{propNumber}", $"{propName}");
            }

            formulaTranslate = formulaTranslate
                .Replace(" OR ", " 或 ")
                .Replace("OR", " 或 ")
                .Replace(" AND ", " 且 ")
                .Replace("AND", " 且 ");

            return formulaTranslate;
        }

        /// <summary>
        /// 根据属性编码和属性值编码加载属性值名称
        /// </summary>
        private List<string> LoadPropValueNamesByNumbers(UserContext userCtx, List<string> propNumbers, List<string> propValueNumbers)
        {
            var aclFilter1 = DataRowACLHelper.GetDataRowACLFilter(userCtx, "pv.", "");
            var aclFilter2 = DataRowACLHelper.GetDataRowACLFilter(userCtx, "p.");

            var sqlText = $@"
            select distinct pv.fname from t_sel_propvalue pv with(nolock) 
            inner join t_sel_prop p with(nolock) on p.fid=pv.fpropid 
            where {aclFilter1}{aclFilter2}";

            if (propNumbers.Count == 1)
            {
                sqlText += $" and p.fnumber='{propNumbers[0]}'";
            }
            else
            {
                sqlText += $" and p.fnumber in('{string.Join("','", propNumbers)}')";
            }

            if (propValueNumbers.Count == 1)
            {
                sqlText += $" and pv.fnumber='{propValueNumbers[0]}'";
            }
            else
            {
                sqlText += $" and pv.fnumber in('{string.Join("','", propValueNumbers)}')";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            var propObjs = dbService.ExecuteDynamicObject(userCtx, sqlText);

            var propValueNames = propObjs?.Select(o => Convert.ToString(o["fname"]))?.ToList();

            return propValueNames ?? new List<string>();
        }

        /// <summary>
        /// 根据属性编码和属性值编码加载属性和属性值信息
        /// </summary>
        private DynamicObjectCollection LoadPropValueInfoByNumbers(UserContext userCtx, List<string> propNumbers, List<string> propValueNumbers)
        {
            var aclFilter1 = DataRowACLHelper.GetDataRowACLFilter(userCtx, "pv.", "");
            var aclFilter2 = DataRowACLHelper.GetDataRowACLFilter(userCtx, "p.");

            var sqlText = $@"
            select distinct p.fdatatype,p.fnumber,p.fname,pv.fnumber propValueNumber,pv.fname propValueName from t_sel_propvalue pv with(nolock) 
            inner join t_sel_prop p with(nolock) on p.fid=pv.fpropid 
            where {aclFilter1}{aclFilter2}";

            if (propNumbers.Count == 1)
            {
                sqlText += $" and p.fnumber='{propNumbers[0]}'";
            }
            else
            {
                sqlText += $" and p.fnumber in('{string.Join("','", propNumbers)}')";
            }

            if (propValueNumbers.Count == 1)
            {
                sqlText += $" and pv.fnumber='{propValueNumbers[0]}'";
            }
            else
            {
                sqlText += $" and pv.fnumber in('{string.Join("','", propValueNumbers)}')";
            }

            var dbService = userCtx.Container.GetService<IDBService>();
            var propObjs = dbService.ExecuteDynamicObject(userCtx, sqlText);
            return propObjs;
        }

        /// <summary>
        /// 解析慕思SAP属性公式中的属性编码和属性值编码
        /// 未编译的公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// 已编译的公式示例：S005='C1411' OR S006=12 OR S006=13 OR S006=14 OR S006=15 OR S007='C1416' OR S007='C1417'
        /// </summary>
        /// <param name="formula">公式</param>
        /// <param name="needCompile">是否需要编译慕思SAP公式，默认需要编译</param>
        /// <returns>
        /// 返回解析出来的属性编码和属性值编码（属性编码为键，属性值编码集合为值的键值对）
        /// 比如：属性编码：S005、S006、S007，属性值编码：C1411、C1416、C1417
        /// </returns>
        public Dictionary<string, List<string>> ParseMusiSapFormulaPropNumbers(string formula, bool needCompile = true)
        {
            var propNumberKv = new Dictionary<string, List<string>>(StringComparer.OrdinalIgnoreCase);
            
            if (formula.IsNullOrEmptyOrWhiteSpace()) return propNumberKv;

            //编译慕思SAP公式
            if (needCompile)
            {
                formula = this.CompileMusiSapFormula(formula);
            }

            var exprs = formula?.Split(new string[] { "(", ")", "OR", "AND" }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var expr in exprs)
            {
                if (expr.IsNullOrEmptyOrWhiteSpace()) continue;

                string[] exprItems = null;
                if (expr.IndexOf("<=") > -1)
                {
                    exprItems = expr.Split(new string[] { "<=" }, StringSplitOptions.RemoveEmptyEntries);
                }
                else if (expr.IndexOf("<>") > -1)
                {
                    exprItems = expr.Split(new string[] { "<>" }, StringSplitOptions.RemoveEmptyEntries);
                }
                else if (expr.IndexOf("<") > -1)
                {
                    exprItems = expr.Split(new string[] { "<" }, StringSplitOptions.RemoveEmptyEntries);
                }
                else if (expr.IndexOf(">=") > -1)
                {
                    exprItems = expr.Split(new string[] { ">=" }, StringSplitOptions.RemoveEmptyEntries);
                }
                else if (expr.IndexOf(">") > -1)
                {
                    exprItems = expr.Split(new string[] { ">" }, StringSplitOptions.RemoveEmptyEntries);
                }
                else if (expr.IndexOf("=") > -1)
                {
                    exprItems = expr.Split(new string[] { "=" }, StringSplitOptions.RemoveEmptyEntries);
                }

                if (exprItems != null && exprItems.Length == 2)
                {
                    var propNumber = exprItems[0].Trim();
                    var propValueNumber = exprItems[1].Trim().TrimStart('\'').TrimEnd('\''); //去掉首尾的单引号
                    if (propNumber.IsNullOrEmptyOrWhiteSpace() || 
                        propValueNumber.IsNullOrEmptyOrWhiteSpace())
                    {
                        continue;
                    }

                    //属性编码为键，属性值编码列表为值
                    List<string> propValueNumbers = null;
                    propNumberKv.TryGetValue(propNumber, out propValueNumbers);
                    if (propValueNumbers == null)
                    {
                        propValueNumbers = new List<string>();
                        propNumberKv[propNumber] = propValueNumbers;
                    }
                    if (!propValueNumbers.Contains(propValueNumber, StringComparer.OrdinalIgnoreCase))
                    {
                        propValueNumbers.Add(propValueNumber);
                    }
                }
            }

            return propNumberKv;
        }

        /// <summary>
        /// 解析指定的慕思SAP属性条件公式中的属性编码，并且根据属性编码加载属性数据包
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formulas">条件公式集合</param>
        /// <returns>返回解析出来的属性数据包</returns>
        public List<DynamicObject> ParseMusiSapFormulaPropDatas(UserContext userCtx, List<string> formulas)
        {
            var propDatas = new List<DynamicObject>();
            if (formulas == null || !formulas.Any()) return propDatas;

            var propNumbers = new List<string>();

            //解析公式中的属性编码
            //一次性把公式中用到的属性基础资料查出来
            foreach (var formula in formulas)
            {
                var _propNumbers = this.ParseMusiSapFormulaPropNumbers(formula).Keys.ToList();
                propNumbers.AddRange(_propNumbers);
            }

            propNumbers = propNumbers.Distinct().ToList();

            if (propNumbers.Any())
            {
                propDatas = userCtx.LoadBizBillHeadDataByACLFilter(
                    "sel_prop",
                    $"fnumber in('{string.Join("','", propNumbers)}')",
                    "fname,fnumber,fdatatype");
            }

            return propDatas;
        }

        /// <summary>
        /// 加载所有属性数据的键值对（用于替换选配约束条件公式中的属性编码）
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <returns>
        /// 返回所有属性数据信息，包括 fid、fname、fnumber、fdatatype（属性ID、属性名称、属性编码、属性数据类型）
        /// </returns>
        public List<DynamicObject> LoadAllPropData(UserContext userCtx)
        {
            var aclFilter = DataRowACLHelper.GetDataRowACLFilter(userCtx, "", "");

            var sqlText = $"select fid,fname,fnumber,fdatatype from t_sel_prop with(nolock) where {aclFilter}";

            var dbService = userCtx.Container.GetService<IDBService>();

            return dbService.ExecuteDynamicObject(userCtx, sqlText)
                ?.OfType<DynamicObject>()
                ?.ToList();
        }

        /// <summary>
        /// 解析选配约束条件公式中的属性编码，并且根据属性编码加载属性数据包
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="constraints">选配约束条件</param>
        /// <returns>返回解析出来的属性数据包</returns>
        public List<DynamicObject> ParseSelConstraintFormulaPropDatas(UserContext userCtx, DynamicObjectCollection constraints)
        {
            var formulas = new List<string>();

            //解析约束条件和约束值公式中的属性名
            //一次性把公式中用到的属性基础资料查出来
            foreach (var dynObj in constraints)
            {
                var conditionFormula = Convert.ToString(dynObj?["fconstraintcondition"]);
                var valueFormula = Convert.ToString(dynObj?["fconstraintval"]);
                formulas.Add(conditionFormula);
                formulas.Add(valueFormula);
            }

            var propDatas = this.ParseMusiSapFormulaPropDatas(userCtx, formulas);

            return propDatas;
        }

        /// <summary>
        /// 解析指定条件公式中的属性名称，并且根据属性名称加载属性数据包
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="formulas">条件公式集合</param>
        /// <returns>返回解析出来的属性数据包</returns>
        public List<DynamicObject> ParseFormulaPropDatas(UserContext userCtx, List<string> formulas)
        {
            var propDatas = new List<DynamicObject>();
            if (formulas == null || !formulas.Any()) return propDatas;

            var propNames = new List<string>();

            //解析公式中的属性名
            //一次性把公式中用到的属性基础资料查出来
            foreach (var formula in formulas)
            {
                var _propNames = this.ParseFormulaPropNames(formula);
                propNames.AddRange(_propNames);
            }

            propNames = propNames.Distinct().ToList();

            if (propNames.Any())
            {
                propDatas = userCtx.LoadBizBillHeadDataByACLFilter(
                    "sel_prop", 
                    $"fname in('{string.Join("','", propNames)}')",
                    "fname,fnumber,fdatatype");
            }

            return propDatas;
        }


        static Regex _regexExpItem = new Regex(@"\[(.+?)\]");
        /// <summary>
        /// 解析公式中的属性名称
        /// 公式示例1："[床架类别]"=="底座床架"
        /// 公式示例2："[床架长]"==103 or "[床架长]"==204
        /// </summary>
        /// <param name="formula">公式</param>
        /// <returns>返回解析出来的属性名称集合</returns>
        public List<string> ParseFormulaPropNames(string formula)
        {
            var propNames = new List<string>();

            if (formula.IsNullOrEmptyOrWhiteSpace()) return propNames;

            //匹配所有的[]           
            MatchCollection matches = _regexExpItem.Matches(formula);
            foreach (Match match in matches)
            {
                if (match.Groups.Count == 2)
                {
                    //取到的是：[床架长]
                    //var value = match.Groups[0].Value;

                    //取到的是：床架长
                    var _propName = match.Groups[1].Value;
                    if (!_propName.IsNullOrEmptyOrWhiteSpace())
                    {
                        propNames.Add(_propName);
                    }
                }
            }

            //去重
            propNames = propNames.Distinct().ToList();

            return propNames;
        }

        /// <summary>
        /// 根据约束值条件加载属性值ID集合
        /// </summary>
        /// <param name="userCtx">上下文</param>
        /// <param name="sqlWheres">约束值条件</param>
        /// <param name="sqlWheres">多个约束值条件直接的逻辑分隔符，比如：or 或 and</param>
        /// <returns>返回属性值ID集合</returns>
        public List<string> LoadPropValueIdsByWheres(UserContext userCtx, List<string> sqlWheres, string separator = "or")
        {
            if (separator.IsNullOrEmptyOrWhiteSpace())
            {
                throw new ArgumentNullException("参数 separator 不能为空，请检查！");
            }

            var propValueIds = new List<string>();
            if (sqlWheres == null || !sqlWheres.Any()) return propValueIds;

            var aclFilter1 = DataRowACLHelper.GetDataRowACLFilter(userCtx, "pv.");
            var aclFilter2 = DataRowACLHelper.GetDataRowACLFilter(userCtx, "p.");

            var sqlText = $@"/*dialect*/
            select distinct pv.fid propValueId from t_sel_propvalue pv with(nolock) 
            inner join t_sel_prop p with(nolock) on p.fid=pv.fpropid 
            where (({string.Join($") {separator} (", sqlWheres)})){aclFilter1}{aclFilter2}";

            var dbService = userCtx.Container.GetService<IDBService>();
            var dynObjs = dbService.ExecuteDynamicObject(userCtx, sqlText);
            if (dynObjs == null || !dynObjs.Any()) return propValueIds;

            propValueIds = dynObjs.Select(o => Convert.ToString(o["propValueId"])).ToList();

            return propValueIds;
        }

        #endregion

        #region 私有方法

        static  Regex _replaceSpace = new Regex(@"\s{1,}", RegexOptions.IgnoreCase);
        static Regex _regexIn = new Regex("(\\w+)[ ]+[NOT IN|IN]+\\((.*?)\\)");

        /// <summary>
        /// 编译慕思SAP系统同步过来的公式
        /// 该编译逻辑和慕思老CRM系统（Java）的编译逻辑保存一致，只是这里通过C#重新实现。
        /// 公式示例：S005='C1411' OR S006 IN (12-15) OR S007 IN ('C1416','C1417')
        /// </summary>
        /// <param name="formula">编译前的公式字符串</param>
        /// <param name="func">用于处理公式中 IN 比较符的委托，比如：S006 IN (12-15) 这种公式该怎么处理，由具体的调用方实现。</param>
        /// <returns>
        /// 返回编译后的公式字符串
        /// 公式示例：S005='C1411' OR S006=12 OR S006=13 OR S006=14 OR S006=15 OR S007='C1416' OR S007='C1417'
        /// </returns>
        private string CompileMusiSapFormula(string formula, Func<string, List<string>, string> func = null)
        {
            //应对SAP下发的NOT (S002 IN('C001082','C001083'))
            if (formula.ToUpper().IndexOf("NOT (") > -1) 
            {
                formula = formula.Replace("NOT","").Replace("IN", "NOT IN ");
                //string pattern = @"[ ]+\((.*?)\)";
                //MatchCollection ms = Regex.Matches(formula, pattern);
                //foreach (Match match in ms) 
                //{
                //    if (match.Groups.Count > 1) 
                //    {
                //        //将之前的公式转换成S002 NOT IN ('C001082','C001083')
                //        formula = match.Groups[1].Value.Replace("IN", "NOT IN");
                //    }
                //}
            }
            //将公式中的 多个空格处理成单个，避免解析公式出现问题。            
            formula = _replaceSpace.Replace(formula, " ").Trim();

            
            //var regex = new Regex("(\\w+)[ ]+[NOT IN|IN]+[ ]+\\('?\\w+'?(,'?\\w+'?)*\\)");
            var matches = _regexIn.Matches(formula);

            foreach (Match match in matches)
            {
                //得到公式中匹配到的表达式，比如：S006 IN (17-38)
                var expr = match.Groups[0].Value;

                //得到公式中匹配到的属性编码，比如：S006
                var propNumber = match.Groups[1].Value;

                //得到公式中匹配到的数值范围表达式，比如：17-38 或者 'C1416','C1417','C1418','C1419','C1420'
                var rangeExpr = match.Groups[2].Value;
                rangeExpr = rangeExpr.Replace(" ", "");

                //范围中的每个属性值
                var propValues = new List<string>();

                if (rangeExpr.IndexOf("-") > -1)
                {
                    //处理数值范围，比如：17-38
                    var numRange = rangeExpr.Split('-');
                    int.TryParse(numRange[0], out var begin);
                    int.TryParse(numRange[1], out var end);
                    for (var i = begin; i <= end; i++)
                    {
                        propValues.Add(i.ToString());
                    }
                }
                else
                {
                    //处理字符串范围，比如：'C1416','C1417','C1418','C1419','C1420'
                    propValues = rangeExpr.Split(',').ToList();
                }

                var strExpr = "";
                if (func != null)
                {
                    //该逻辑由调用方实现
                    strExpr = func.Invoke(propNumber, propValues);
                }
                else
                {
                    var sbExpr = new StringBuilder();
                    if (formula.ToUpper().IndexOf("NOT IN") > -1)
                    {
                        foreach (var propValue in propValues)
                        {
                            sbExpr.AppendFormat("{0}<>{1} AND ", propNumber, propValue);
                        }
                    }
                    else {
                        foreach (var propValue in propValues)
                        {
                            sbExpr.AppendFormat("{0}={1} OR ", propNumber, propValue);
                        }
                    }
                    strExpr = "(" + sbExpr.ToString().Substring(0, sbExpr.Length - 4) + ")";
                }

                //将公式中原始表达式替换为编译后的表达式（公式中有多个重复表达式时只替换第一次出现的）
                var sbFormula = new StringBuilder(formula);
                formula = sbFormula.Replace(expr, strExpr, formula.IndexOf(expr), expr.Length).ToString();

                //对编译后的公式进行再次循环编译，直到公式中的所有表达式都被编译完成（也就是循环结束）
                matches = _regexIn.Matches(formula);
            }

            return formula;
        }

        /// <summary>
        /// 纠正公式
        /// </summary>
        /// <param name="formula">公式</param>
        /// <returns></returns>
        private string FormulaReplace(string formula)
        {
            //1、去除中文单引号 2、将 =（赋值）变成 ==（等于）
            formula = formula
                .Replace("\"", "'") // 双引号 替换为 单引号
                .Replace("||", "or") // || 替换为 or
                .Replace("&&", "and") // || 替换为 and
                .Replace("（", "(").Replace("）", ")")
                .Replace("‘", "'").Replace("’", "'")
                .Replace(">=", "[大于等于]").Replace("<=", "[小于等于]")
                .Replace("!=", "[不等于]").Replace("<>", "[不等于]")
                .Replace("==", "[等于]").Replace("=", "[等于]");

            formula = formula
                .Replace("[大于等于]", ">=")
                .Replace("[小于等于]", "<=")
                .Replace("[不等于]", "!=")
                .Replace("[等于]", "==");

            formula = formula
                .Replace(" OR ", " or ")
                .Replace(" AND ", " and ");

            return formula;
        }

        #endregion
    }
}