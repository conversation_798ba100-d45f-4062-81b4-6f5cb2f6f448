using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.Model.BD.Customer.Consumption
{
    /// <summary>
    /// 消费记录列表数据模型
    /// </summary>
    public class ConsumptionListModel : BaseDataModel
    {

        /// <summary>
        /// 合同金额
        /// </summary>
        public decimal SumAmount { get; set; }

        /// <summary>
        /// 签订日期
        /// </summary>
        public DateTime OrderDate { get; set; }

        /// <summary>
        /// 结算状态
        /// </summary>
        public ComboDataModel ReceiptStatus { get; set; }
        /// <summary>
        /// 负责人
        /// </summary>
        public StaffSimpleModel Staff { get; set; }
        /// <summary>
        /// 当前登录用户是否为合同负责人
        /// </summary>
        public bool IsLoginStaff { get; set; }
    }
}
