using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.MP.API.Model;
using System.Runtime.Serialization;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    /// <summary>
    /// 意向单编辑数据模型
    /// </summary>
    public class SaleIntentionEditModel : BaseDataModel
    {
        [IgnoreDataMember]
        public new string Name { get; set; }

        [IgnoreDataMember]
        public new ComboDataModel Status { get; set; }

        public BaseDataSimpleModel Channel { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 销售员
        /// </summary>
        public BaseDataSimpleModel Staff { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 销售门店
        /// </summary>
        public BaseDataSimpleModel Dept { get; set; } = new BaseDataSimpleModel();

        /// <summary>
        /// 交货时间
        /// </summary>
        public DateTime? PickDate { get; set; }

        /// <summary>
        /// 商品
        /// </summary>
        public List<SaleIntentionProductModel> Products { get; set; } = new List<SaleIntentionProductModel>();

        /// <summary>
        /// 客户档案
        /// </summary>
        public CustomerSimpleModel Customer { get; set; } = new CustomerSimpleModel();

        /// <summary>
        /// 是否有预留单
        /// </summary>
        public BaseDataModel ReserveBill { get; set; } = new BaseDataModel();

        ///// <summary>
        ///// 收货地址
        ///// </summary>
        //public AddressModel Address { get; set; } = new AddressModel();

        /// <summary>
        /// 辅助资料、简单枚举、单据类型 下拉框数据源
        /// </summary>
        public Dictionary<string, List<Dictionary<string, object>>> ComboData { get; set; }

        /// <summary>
        /// 源单类型
        /// </summary>
        public string SourceType { get; set; }

        /// <summary>
        /// 源单单号
        /// </summary>
        public string SourceNumber { get; set; }

        /// <summary>
        /// 订单总额（ffbillamount）
        /// </summary>
        public decimal BillAmount { get; set; }

        ///// <summary>
        ///// 剩余收款（freceiptamount）
        ///// </summary>
        //public decimal ReceiptAmount { get; set; }

        ///// <summary>
        ///// 已结算金额（freceivedamount）
        ///// </summary>
        //public decimal ReceivedAmount { get; set; }

        ///// <summary>
        ///// 收款确认中（fconfirmamount）
        ///// </summary>
        //public decimal ConfirmAmount { get; set; }

        /// <summary>
        /// 应收定金（fcollectamount）
        /// </summary>
        public decimal CollectAmount { get; set; }

        /// <summary>
        /// 已收定金（fcollectedamount）
        /// </summary>
        public decimal CollectedAmount { get; set; }

        /// <summary>
        /// 确认已收（fconfirmedamount）
        /// </summary>
        public decimal ConfirmedAmount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Description { get; set; }

    }

    public class AddressModel
    {
        /// <summary>
        /// 地址id
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// 完整地址
        /// </summary>
        public string District { get; set; }

        /// <summary>
        /// 联系人
        /// </summary>
        public string Contact { get; set; }

        /// <summary>
        /// 手机号
        /// </summary>
        public string Phone { get; set; }

        public static AddressModel Create(DynamicObject obj, HtmlForm htmlForm, UserContext userCtx)
        {
            AddressModel model = new AddressModel();

            model.Id = JNConvert.ToStringAndTrim(obj["id"]);
            model.District = htmlForm.GetDistrictFullText(userCtx, obj);
            model.Contact = JNConvert.ToStringAndTrim(obj["fcontact"]);
            model.Phone = JNConvert.ToStringAndTrim(obj["fphone"]);

            return model;
        }
    }
}