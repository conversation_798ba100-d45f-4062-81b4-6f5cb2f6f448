using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework;
using JieNor.Framework.Utils;
using JieNor.Framework.Interface;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.AMS.YDJ.Core;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.BD.Product
{
    /// <summary>
    /// 微信小程序：商品详情取数接口
    /// </summary>
    public class ProductDetailController : BaseController
    {
        /// <summary>
        /// 商品表单模型
        /// </summary>
        protected HtmlForm ProductForm { get; set; }

        public string PriceMsg { get; set; }
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ProductDetailDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ProductDetailModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            //根据唯一标识获取数据
            this.ProductForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var productObj = this.ProductForm.GetBizDataById(this.Context, dto.Id, true);

            //设置响应数据包
            this.SetResponseData(dto, productObj, resp);

            return resp;
        }

        /// <summary>
        /// 设置响应数据包
        /// </summary>
        /// <param name="dto"></param>
        /// <param name="productObj"></param>
        /// <param name="resp"></param>
        private void SetResponseData(ProductDetailDTO dto, DynamicObject productObj, BaseResponse<ProductDetailModel> resp)
        {
            if (productObj == null)
            {
                resp.Message = "商品不存在或已被删除！";
                resp.Success = false;
                return;
            }

            resp.Message = "取数成功！";
            resp.Success = true;

            var productId = Convert.ToString(productObj["id"]);
            var brandObj = productObj["fbrandid_ref"] as DynamicObject;
            var spaceObj = productObj["fspace_ref"] as DynamicObject;
            var styleObj = productObj["fstyle_ref"] as DynamicObject;
            var salUnitObj = productObj["fsalunitid_ref"] as DynamicObject;
            var seriesObj = productObj["fseriesid_ref"] as DynamicObject;
            var categoryObj = productObj["fcategoryid_ref"] as DynamicObject;

            //简单下拉框模型数据
            var deliveryModeId = Convert.ToString(productObj["fdeliverymode"]).Trim();
            var deliveryModeName = this.ProductForm.GetSimpleSelectItemText(productObj, "fdeliverymode");

            //是否允许选配
            var isPresetProp = Convert.ToBoolean(productObj["fispresetprop"]);

            resp.Data.SrcFormId = dto.SrcFormId;
            resp.Data.BillTypeNo = dto.BillTypeNo;
            resp.Data.BillTypeName = dto.BillTypeName;
            resp.Data.Id = productId;
            resp.Data.Number = Convert.ToString(productObj["fnumber"]);
            resp.Data.Name = Convert.ToString(productObj["fname"]);
            resp.Data.ImageUrl = StringUtil.CheckURLValid(Convert.ToString(productObj["fimage"])) ? Convert.ToString(productObj["fimage"]) : Convert.ToString(productObj["fimage"]).GetSignedFileUrl();
            resp.Data.SalPrice = Convert.ToDecimal(productObj["fsalprice"]);
            resp.Data.GuidePrice = Convert.ToDecimal(productObj["fguideprice"]);
            resp.Data.Space = Convert.ToString(spaceObj?["fenumitem"]);
            resp.Data.Style = Convert.ToString(styleObj?["fenumitem"]);
            resp.Data.Specifica = Convert.ToString(productObj["fspecifica"]);
            resp.Data.Size = Convert.ToString(productObj["fsize"]);
            resp.Data.Suttle = Convert.ToDecimal(productObj["fsuttle"]);
            resp.Data.GrossLoad = Convert.ToDecimal(productObj["fgrossload"]);
            resp.Data.Volume = Convert.ToDecimal(productObj["fvolume"]);
            resp.Data.Originplace = Convert.ToString(productObj["foriginplace"]);
            resp.Data.IsCustom = Convert.ToBoolean(productObj["fcustom"]);
            resp.Data.IsPresetProp = isPresetProp;
            resp.Data.IsSuite = Convert.ToBoolean(productObj["fsuiteflag"]);
            resp.Data.IsNonStandard = Convert.ToBoolean(productObj["funstdtype"]);
            resp.Data.IsAddibleParts = Convert.ToBoolean(productObj["fbedpartflag"]);
            //配件标记
            resp.Data.IsPartFlag = Convert.ToBoolean(productObj["fispartflag"]);
            resp.Data.IsFixProp = Convert.ToBoolean(productObj["fisfixprop"]);
            resp.Data.DeliveryMode = new ComboDataModel
            {
                Id = deliveryModeId,
                Name = deliveryModeName
            };
            resp.Data.SalUnit = new BaseDataSimpleModel(salUnitObj);
            resp.Data.Content = Convert.ToString(productObj["fcontent"]);
            resp.Data.SelCategoryId = Convert.ToString(productObj["fselcategoryid"]);
            resp.Data.IsGiveaway = false;
            resp.Data.Dataorigin = Convert.ToString(productObj["fmainorgid"]).EqualsIgnoreCase(this.Context.TopCompanyId) ? "总部" : "";
            resp.Data.IsSofaCategory = ProductUtil.HaveAnyCategory(this.Context, "沙发类", productId);

            var orderservice = this.Context.Container.GetService<IOrderService>();

            // 商品标签
            resp.Data.Brand = new ComboDataModel
            {
                Id = Convert.ToString(brandObj?["id"]),
                Name = Convert.ToString(brandObj?["fname"])
            };

            resp.Data.Series = new ComboDataModel
            {
                Id = Convert.ToString(seriesObj?["id"]),
                Name = Convert.ToString(seriesObj?["fname"])
            };

            resp.Data.Category = new BaseDataDTO
            {
                Id = Convert.ToString(categoryObj?["id"]),
                Name = Convert.ToString(categoryObj?["fname"]),
                Number = Convert.ToString(categoryObj?["fnumber"])
            };

            //销售详情如果没有业绩品牌值则取默认系列
            if (resp.Data.ResultBrand.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Data.ResultBrand = this.Context.GetResultBrand(dto.deptId,
                    dto.BillTypeName,
                    Convert.ToString(seriesObj?["id"]),
                    Convert.ToString(seriesObj?["fnumber"]),
                    Convert.ToString(seriesObj?["fname"]),
                    Convert.ToString(productObj?["fauxseriesid"]));
            }

            resp.Data.MoreNumConf = GetMoreNumConf();

            //加载商品选配类别的默认属性值集
            var selCategoryAuxPropValKv = ProductUtil.LoadProductAuxPropVals(this.Context, new List<string> { resp.Data.SelCategoryId });
            var auxPropVals = selCategoryAuxPropValKv.GetValue(resp.Data.SelCategoryId);

            //如果商品勾选了允许选配，则获取对应的选配类别属性值集
            if (isPresetProp)
            {
                var auxPropInfo = ProductUtil.GetAuxPropInfo(this.Context, productId, dto.AuxPropValId, resp.Data.IsNonStandard, true);
                resp.Data.AuxPropInfo = auxPropInfo;
            }

            //加载商品价格
            this.LoadProductPrice(resp.Data, dto.AuxPropValId, auxPropVals, dto);
            //商品详情不需要提示
            //if (!PriceMsg.IsNullOrEmptyOrWhiteSpace())
            //{
            //    resp.Message = PriceMsg;
            //    resp.IsShowMessage = true;
            //}

            //中台同步过来的直接加载，不需要获取URL，固定一张已确认需求
            if (StringUtil.CheckURLValid(Convert.ToString(productObj["fimage"])))
            {
                //加载商品图片
                resp.Data.ImageList = new List<BaseImageModel>() {
                    new BaseImageModel()
                    {
                        Id = Convert.ToString(productObj["fimage"]),
                        Name = Convert.ToString(productObj["fimage_txt"]),
                        Url = Convert.ToString(productObj["fimage"]),
                        ThumbUrl = Convert.ToString(productObj["fimage"])
                    }
                };
            }
            else
            {
                //加载商品图片
                this.LoadProductImage(resp.Data, productObj, dto.AuxPropValId, auxPropVals);
            }



            resp.Data.IsAllowOrders = orderservice.CheckNoOrders(this.Context, new NoOrderParmas
            {
                fisgiveaway = false,
                fissuit = resp.Data.IsSuite,
                fmainorgid = Convert.ToString(productObj["fmainorgid"]),
                fprice = resp.Data.SalPrice,
                funstdtype = Convert.ToBoolean(productObj["funstdtype"])
            });

            #region 判断商品能否编辑零售价
            // 向麦浩系统发送请求
            var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
                this.Request,
                new CommonBillDTO()
                {
                    FormId = "ydj_order",
                    OperationNo = "CheckEditPrice",
                    Option = new Dictionary<string, object>().AddMSAPIDefaultParams(),
                    SimpleData = new Dictionary<string, string>
                    {
                        { "productId", productId }
                    }
                });
            var result = response?.OperationResult;
            var isEdit = false;
            if (result.IsSuccess) 
            {
                var isJson = IsJson((string)result.SrvData);
                //不是Json的话就用之前的
                if (isJson)
                {
                    var data = JObject.Parse((string)result.SrvData);
                    var AgentForbid = Convert.ToBoolean(data?["agentForbid"]);
                    if (AgentForbid)
                    {
                        if (resp.Data.SalPrice > 0)
                        {
                            isEdit = false;
                        }
                        else
                        {
                            isEdit = true;
                        }
                    }
                    //原逻辑
                    else
                    {
                        isEdit = Convert.ToBoolean(result.SrvData);
                    }
                    //result.IsSuccess? Convert.ToBoolean(result.SrvData) : false;
                }
                else
                {
                    isEdit = Convert.ToBoolean(result.SrvData);
                }

            }
            resp.Data.IsEditPrice = isEdit;
            #endregion

            if (resp.Data.IsSuite)
            {
                var propSuiteCount = this.Context.LoadBizDataByFilter("sel_prop", "fname = '套件总件数'").FirstOrDefault();

                if (propSuiteCount != null)
                {
                    resp.Data.SuiteSumQtyProp = new BaseDataDTO
                    {
                        Id = Convert.ToString(propSuiteCount["Id"]),
                        Name = Convert.ToString(propSuiteCount["fname"]),
                        Number = Convert.ToString(propSuiteCount["fnumber"])
                    };
                }

                //加载子件列表
                LoadPartsList(this.Context, resp.Data, DateTime.Now);
            }
        }

        /// <summary>
        /// 商品非标选配属性值默认个数显示
        /// </summary>
        /// <returns></returns>
        private int GetMoreNumConf()
        {
            //销售合同允许采购的最低金额比例
            var profileService = this.Context.Container.GetService<ISystemProfile>();
            var systemParameter = profileService.GetSystemParameter(this.Context, "bas_storesysparam");
            var foptionalattrnumber = Convert.ToInt32(systemParameter["foptionalattrnumber"]);
            return foptionalattrnumber;
        }

        /// <summary>
        /// 加载商品价格
        /// </summary>
        private void LoadProductPrice(ProductDetailModel product, string auxPropValId, List<Dictionary<string, string>> auxPropVals, ProductDetailDTO dto)
        {
            //商品档案中的销售价
            var salPrice = product.SalPrice;

            //选配计价服务
            var formulaService = this.Container.GetService<ISelectionPriceService>();

            if (!auxPropValId.IsNullOrEmptyOrWhiteSpace())
            {
                if (product.IsNonStandard)
                {
                    //如果是非标商品，则销售价直接返回0
                    product.SalPrice = 0;
                }
                else
                {
                    var price = ProductUtil.GetPrices(this.Context, product.Id, auxPropValId, dto.Orderdate, false);
                    product.SalPrice = price.SalPrice;
                    product.FromAgent = price.FromAgent;
                    product.HqSalPrice = ProductUtil.GetPrices(this.Context, product.Id, auxPropValId, dto.Orderdate, true).SalPrice;
                    PriceMsg = price.PriceMsg;
                    var propList = ProductUtil.LoadPropEntityList(this.Context, auxPropValId);
                    //if (!price.IgnorePriceFormula)
                    //{
                    //    product.SalPrice = formulaService.GetProductPrice_New(this.Context, product.Id, product.SalPrice, propList);
                    //}
                    //product.HqSalPrice = formulaService.GetProductPrice_New(this.Context, product.Id, product.HqSalPrice, propList);
                }
            }
            else
            {
                if (product.IsNonStandard)
                {
                    //如果是非标商品，则销售价直接返回0
                    product.SalPrice = 0;
                }
                else
                {
                    var price = ProductUtil.GetPrices(this.Context, product.Id, dto.Orderdate, false, auxPropVals);
                    product.SalPrice = price.SalPrice;
                    product.FromAgent = price.FromAgent;
                    product.HqSalPrice = ProductUtil.GetPrices(this.Context, product.Id, dto.Orderdate, true, auxPropVals).SalPrice;
                    PriceMsg = price.PriceMsg;
                    var propList = ProductUtil.LoadPropEntityList(this.Context, auxPropVals);
                    //if (!price.IgnorePriceFormula)
                    //{
                    //    product.SalPrice = formulaService.GetProductPrice_New(this.Context, product.Id, product.SalPrice, propList);
                    //}
                    //product.HqSalPrice = formulaService.GetProductPrice_New(this.Context, product.Id, product.HqSalPrice, propList);
                }
            }

            //如果本次取到的价格不等于商品档案中的销售价时，需要将取到的最新价格反写到商品档案中
            //if (product.HqSalPrice != salPrice)
            //{
            //    //异步反写商品价格：需要将取到的价格反写到商品档案中，以便小程序在下次访问商品列表接口时可以直接获取商品档案中的销售价
            //    ProductUtil.BackWriteProductPriceAsync(this.Context, new ProductPriceModel
            //    {
            //        ProductId = product.Id,
            //        SalPrice = product.HqSalPrice
            //    });
            //}

            //异步反写经销商价格：需要将取到的价格反写到经销商价格表中，以便小程序在下次访问商品列表接口时可以直接获取经销商价格表中的统一零售价
            //ProductUtil.BackWriteAgentProductPriceAsync(this.Context, new ProductPriceModel
            //{
            //    FromAgent = product.FromAgent,
            //    ProductId = product.Id,
            //    SalPrice = product.SalPrice
            //});
        }

        /// <summary>
        /// 加载商品图片
        /// </summary>
        private void LoadProductImage(ProductDetailModel product, DynamicObject productObj, string auxPropValId, List<Dictionary<string, string>> auxPropVals)
        {
            if (!auxPropValId.IsNullOrEmptyOrWhiteSpace())
            {
                product.ImageList = ProductUtil.GetImages(this.Context, productObj, auxPropValId);
            }
            else
            {
                product.ImageList = ProductUtil.GetImages(this.Context, productObj, auxPropVals);
            }
        }

        /// <summary>
        /// 加载子件列表
        /// </summary>
        public static void LoadPartsList(UserContext ctx, ProductDetailModel product, DateTime orderDate)
        {
            var aclFilter1 = DataRowACLHelper.GetDataRowACLFilter(ctx, "s.");
            var aclFilter2 = DataRowACLHelper.GetDataRowACLFilter(ctx, "m.");
            var aclFilter3 = DataRowACLHelper.GetDataRowACLFilter(ctx, "c.");

            //数据隔离规则SQL
            var authPara = new DataQueryRuleParaInfo()
            {
                SrcFormId = product.SrcFormId,
            };
            authPara.SrcPara.Add("billtypeName", product.BillTypeName);
            authPara.SrcPara.Add("billtypeNo", product.BillTypeNo);
            var tempView = ctx.GetAuthProductDataPKID(authPara);
            var authProductPkidWhere = $" and exists (select FPKId from ({tempView})acl where m.fid=FPKId)";

            var sqlText = $@"
            select c.fname fcategoryname,c.fid fcategoryid,c.fnumber fcategorynumber,m.fid fprodcutid,m.fnumber fprodcutnumber,m.fname fprodcutname,m.fselcategoryid,m.funstdtype,
                se.fqty,m.fsalprice,se.fwhetherdefault,
			    p.fid AS propid, p.fname AS propname, p.fnumber AS propnumber, m.fispresetprop
            from t_sel_suite s with(nolock) 
            inner join t_sel_suiteentry se with(nolock) on se.fid=s.fid 
            inner join t_bd_material m with(nolock) on m.fid=se.fpartproductid {aclFilter2}
            inner join ser_ydj_category c with(nolock) on c.fid=m.fcategoryid {aclFilter3}
            left join t_sel_prop p with(nolock) on se.fpartprop = p.fid
            where s.fproductid=@fproductid {aclFilter1} {authProductPkidWhere}
";
            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fproductid", System.Data.DbType.String, product.Id)
            };

            using (var reader = ctx.ExecuteReader(sqlText, sqlParam))
            {
                while (reader.Read())
                {
                    product.PartsList.Add(new PartsModel
                    {
                        Id = reader.GetValueToString("fprodcutid"),
                        Number = reader.GetValueToString("fprodcutnumber"),
                        Name = reader.GetValueToString("fprodcutname"),
                        Category = new BaseDataDTO
                        {
                            Id = reader.GetValueToString("fcategoryid"),
                            Name = reader.GetValueToString("fcategoryname"),
                            Number = reader.GetValueToString("fcategorynumber"),
                        },
                        Qty = reader.GetValueToDecimal("fqty"),
                        Price = reader.GetValueToDecimal("fsalprice"),
                        IsDefault = reader.GetValueToString("fwhetherdefault") == "1",
                        SelCategoryId = reader.GetValueToString("fselcategoryid"),
                        IsNonStandard = reader.GetValueToString("funstdtype") == "1",
                        PartProp = new BaseDataDTO
                        {
                            Id = reader.GetValueToString("propid"),
                            Name = reader.GetValueToString("propname"),
                            Number = reader.GetValueToString("propnumber"),
                        },
                        IsSofaCategory = ProductUtil.HaveAnyCategory(ctx, "沙发类", reader.GetValueToString("fprodcutid")),
                        IsPresetProp = reader.GetValueToString("fispresetprop") == "1",
                    });
                }
            }


            //根据商品选配类别的默认属性值匹配图片
            List<DynamicObject> selCategorys = null;
            var selCategoryIds = product.PartsList.Select(o => o.SelCategoryId).Where(o => !o.IsNullOrEmptyOrWhiteSpace());
            if (selCategoryIds.Any())
            {
                selCategorys = ctx.LoadBizDataById("sel_category", selCategoryIds, true);
            }

            //选配计价服务
            var formulaService = ctx.Container.GetService<ISelectionPriceService>();

            foreach (var parts in product.PartsList)
            {
                var auxPropVals = new List<Dictionary<string, string>>();
                var selCategory = selCategorys?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(parts.SelCategoryId));
                var entrys = selCategory?["fentity"] as DynamicObjectCollection;
                if (entrys != null)
                {
                    foreach (var entry in entrys)
                    {
                        if (Convert.ToBoolean(entry["fiswhetherinforce"]))
                        {
                            auxPropVals.Add(new Dictionary<string, string>
                            {
                                { "auxPropId", Convert.ToString(entry["fpropid"]) },
                                { "valueId", Convert.ToString(entry["fdefaultpropvalueid"]) }
                            });
                        }
                    }
                }

                //取子件图片
                parts.ImageList = ProductUtil.GetImages(ctx, parts.Id, auxPropVals);

                if (parts.IsNonStandard)
                {
                    //如果是非标商品，则销售价直接返回0
                    parts.Price = 0;
                }
                else
                {
                    //取子件价格
                    var priceInfo = ProductUtil.GetPrices(ctx, parts.Id, orderDate, false, auxPropVals, true, orderDate);
                    parts.Price = priceInfo.SalPrice;
                    parts.HqPrice = ProductUtil.GetPrices(ctx, parts.Id, orderDate, true, auxPropVals).SalPrice;

                    if (!priceInfo.fpromotionid.IsNullOrEmptyOrWhiteSpace())
                    {
                        var promotion = ctx.LoadBizBillHeadDataById("ydj_productpromotion", priceInfo.fpromotionid,
                            "fnumber,fname");

                        parts.fpromotionid = new JObject()
                        {
                            { "id", priceInfo.fpromotionid },
                            { "fnumber", promotion?["fnumber"]?.ToString() },
                            { "fname", promotion?["fname"]?.ToString() },
                        };
                        parts.fpromotionrule = priceInfo.fpromotionrule;
                        parts.fpromotionsalprice = priceInfo.fpromotionsalprice;
                        parts.fpromotionlowestprice = priceInfo.fpromotionlowestprice;
                    }

                    var propList = ProductUtil.LoadPropEntityList(ctx, auxPropVals);
                    //if (!priceInfo.IgnorePriceFormula)
                    //{
                    //    parts.Price = formulaService.GetProductPrice_New(ctx, parts.Id, parts.Price, propList);
                    //}
                    //parts.HqPrice = formulaService.GetProductPrice_New(ctx, parts.Id, parts.HqPrice, propList);
                }

                var auxPropInfo = ProductUtil.GetAuxPropInfo(ctx, parts.Id);

                var auxs = new List<Core.PropEntity>();
                if (auxPropInfo != null)
                {
                    parts.AuxPropVals = auxPropInfo.ConvertAll(x =>
                    {
                        var pe = new Core.PropEntity()
                        {
                            PropId = x.PropId,
                            PropName = x.PropName,
                            PropNumber = x.PropNumber,
                            PropValueDataType = (Core.PropValueDataTypeEnum)Convert.ToInt32(x.PropValueDataType)
                        };

                        var val = x.ValueList.FirstOrDefault(y => y.IsDefVal);
                        if (val != null)
                        {
                            pe.ValueId = val.ValueId;
                            pe.ValueName = val.ValueName;
                            pe.ValueNumber = val.ValueNumber;
                        }

                        return pe;
                    });// SEL.Suite.SuiteController.GetPropEntity(selCategory);
                }
            }
        }

        private bool IsJson(string sourceStr)
        {
            //如果是空或是空字符串，那么直接返回false
            if (sourceStr.IsNullOrEmptyOrWhiteSpace())
            {
                return false;
            }
            else
            {
                //如果
                if (sourceStr.Length <= 2)
                {
                    return false;
                }
                else
                {
                    //去除开头和结尾的空格和制表符、换行符
                    sourceStr = sourceStr.Trim();
                    if ((sourceStr.StartsWith("{") && sourceStr.EndsWith("}")) || //For JObject
                        (sourceStr.StartsWith("[") && sourceStr.EndsWith("]")))  // For JArray
                    {
                        try
                        {
                            var obj = JToken.Parse(sourceStr);
                            return true;
                        }
                        catch (JsonReaderException jex)
                        {
                            //Exception in parsing json

                            return false;
                        }
                    }
                    else
                    {
                        return false;
                    }
                    // //第一个
                    // char s = sourceStr[0];
                    // //最后一个字符串是}或者是]说明是json格式的。
                    // char e = sourceStr[sourceStr.Length - 1];
                    // return (s == '{' && e == '}') || (s == '[' && e == ']');
                }

            }

        }
    }
}