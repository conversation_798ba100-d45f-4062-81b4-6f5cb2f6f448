using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormModel.List;
using System.Collections.Generic;
using System.Data;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.CustomerRecord
{
    public class BaseCustomerRecordController : BaseController
    {
        /// <summary>
        /// 构建SqlBuilderParameter
        /// 注：用于商机公海时，请设置管理员角色操作，操作结束后再还原。
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="listBuilder"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static SqlBuilderParameter BuildSqlBuilderParameter(UserContext userCtx, IListSqlBuilder listBuilder, string type, string fchannelid = "", int daterange = 0)
        {
            //参数对象
            SqlBuilderParameter param = new SqlBuilderParameter(userCtx, "ydj_customerrecord");
            param.ReadDirty = true;
            param.NoColorSetting = true;

            //当前要查询的字段列表
            var fieldKeys = new string[] { "fbillno", "fcreatedate", "fdemand", "fcustomersource", "fintentionno", "fcustomerid", "fcustomername", "fphone", "fdutyid", "ffollowtime", "fdeptid", "fsalecategory", "fsalecategory_type", "fchancestatus", "forderno", "fclosestatus", "fassigntype", "" };
            foreach (var fieldKey in fieldKeys)
            {
                var field = param.HtmlForm.GetField(fieldKey);
                var columnList = field.ToListColumn(userCtx);
                foreach (var column in columnList)
                {
                    param.SelectedFieldKeys.Add(column.Id);
                }
            }

            switch (type?.ToLower())
            {
                case "intention":
                    param.AddParameter(new SqlParam("@fphase", System.Data.DbType.String, "customerrecord_phase_02"));
                    param.AppendFilterString("fphase = @fphase and fclosestatus=0");
                    break;
                case "design":
                    param.AddParameter(new SqlParam("@fphase", System.Data.DbType.String, "customerrecord_phase_03"));
                    param.AppendFilterString("fphase = @fphase and fclosestatus=0");
                    break;
                case "order":
                    param.AddParameter(new SqlParam("@fphase", System.Data.DbType.String, "customerrecord_phase_04"));
                    /*
                     * 合同签订阶段：
                     * 1.合同待签订：所有未签合同的，都是待签合同。
                     * 2.合同待提交：商机已经签订合同，但是未提交审核。
                     * 3.合同待审核：商机已经签订合同，并已提交审核。
                     */
                    param.AppendFilterString("fphase = @fphase and fclosestatus=0");
                    break;
                case "pool":
                    // 商机公海：对应的是未分配的销售机会。
                    ////param.AppendFilterString("fdutyid='' and fdeptid in ('', @fdeptid) and fclosestatus=0");
                    //param.AppendFilterString("fdutyid='' and fclosestatus=0");
                    bool hasPerm = userCtx.HasPermission("ydj_customerrecord", "fw_commoncus");
                    if (hasPerm)
                    {
                        param.EnableDataRowACL = false;
                        param.AppendFilterString(
                        @"
                    exists(select fid from(
                            select u1.fid from t_ydj_customerrecord u1 with(nolock) where u1.fdutyid='' and fdeptid='' and u1.fclosestatus=0 and u1.fmainorgid=@currentCompanyId
                            union all
                            select u1.fid from t_ydj_customerrecord u1 with(nolock) where u1.fdutyid='' and fdeptid=@currentDeptId and u1.fclosestatus=0 and u1.fmainorgid=@currentCompanyId
                            ) u2 where u2.fid = fid)");
                        param.AddParameter(new SqlParam("@currentDeptId", System.Data.DbType.String, userCtx.GetCurrentDeptId()));
                    }
                    else
                    {
                        param.AppendFilterString("1!=1");
                    }
                    break;
                case "close":
                    param.AddParameter(new SqlParam("@fphase", System.Data.DbType.String, "customerrecord_phase_05"));
                    /*
                     * 已关闭：
                     * 1.成单关闭：商机阶段为已成单。
                     * 2.失效关闭：商机关闭状态为true。
                     */
                    param.AppendFilterString("(fphase = @fphase or fclosestatus=1)");
                    break;
                case "unfollow":
                    // 待跟进
                    param.AddParameter(new SqlParam("@fphase", System.Data.DbType.String, "customerrecord_phase_02"));
                    // chance_status_02：已分配
                    param.AddParameter(new SqlParam("@fchancestatus", System.Data.DbType.String, "chance_status_02"));
                    param.AppendFilterString("fphase = @fphase and fclosestatus=0 and fchancestatus=@fchancestatus");
                    break;
            }
            //if (type != "pool")
            //{
            //    param.AppendFilterString("fdutyid != ''");
            //}
            if (!fchannelid.IsNullOrEmptyOrWhiteSpace())
            {
                param.AddParameter(new SqlParam("@fchannelid", System.Data.DbType.String, fchannelid));
                param.AppendFilterString(" fchannelid=@fchannelid ");

                var daterangesql = GetDateRangesql(daterange);//报备日期范围
                if (!daterangesql.IsNullOrEmptyOrWhiteSpace())
                {
                    param.AppendFilterString($@" fcreatedate between  {daterangesql}");
                }
            }

            //设置数据隔离方案的过滤条件
            var accessFilter = listBuilder.GetListAccessControlFilter(userCtx, param.HtmlForm.Id);
            param.SetFilter(accessFilter);

            return param;
        }

        /// <summary>
        /// 获取
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public static int GetTotalRecordByType(UserContext userCtx, string type, string channelid = "", int daterange = 0)
        {
            var listBuilder = userCtx.Container.GetService<IListSqlBuilder>();
            var param = BuildSqlBuilderParameter(userCtx, listBuilder, type, channelid, daterange);

            var oldRoles = userCtx.UserSession.Roles;

            // 注意：如果商机公海，则使用管理员获取数据，忽略数据范围权限
            var isPool = type.EqualsIgnoreCase("pool");
            if (isPool)
            {
                //if (userCtx.UserSession.Roles == null)
                //{
                //    userCtx.UserSession.Roles = new List<string>();
                //}

                //if (userCtx.UserSession.Roles.Contains($"admin.{userCtx.Company}") == false)
                //{
                //    userCtx.UserSession.Roles.Add($"admin.{userCtx.Company}");
                //}
                param.EnableDataRowACL = false;
            }

            //查询对象
            var queryObj = listBuilder.GetQueryObject(userCtx, param);

            //获取分页信息（总纪录数、总页数、每页条数、单据数）
            var listDesc = listBuilder.GetListDesc(userCtx, param, queryObj);

            //// 还原角色
            //if (isPool)
            //{
            //    userCtx.UserSession.Roles = oldRoles;
            //}

            return (int)listDesc.Rows;
        }

        /// <summary>
        /// 获取日期范围 。如果日期类型为：3全部，则返回空字符串
        /// </summary> 
        /// <param name="daterange"></param>
        public static string GetDateRangesql(int daterange)
        {
            var yearsql = "";
            switch (daterange)
            {
                case 0:
                    var startdate = BeiJingTime.Now.AddYears(-1).DayBegin().ToString();
                    var enddate = BeiJingTime.Now.DayEnd().ToString();
                    yearsql = $@"'{startdate}' and '{enddate}'";
                    break;
                case 1:
                    yearsql = BeiJingTime.Now.ThisYearSql();
                    break;
                case 2:
                    yearsql = BeiJingTime.Now.LastYearSql();
                    break;
                default:
                    break;
            }
            return yearsql;
        }
    }
}
