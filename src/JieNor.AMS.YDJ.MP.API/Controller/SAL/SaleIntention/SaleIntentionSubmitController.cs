using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using ServiceStack;
using System.Collections.Generic;
using JieNor.Framework.DataTransferObject.Poco;
using ServiceStack.Web;

namespace JieNor.AMS.YDJ.MP.API.Controller.SAL.SaleIntention
{
    /// <summary>
    /// 微信小程序：意向单提交接口
    /// </summary>
    public class SaleIntentionSubmitController : BaseController
    {
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(SaleIntentionSubmitDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseDataModel>();

            if (dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                resp.Success = false;
                resp.Message = $"参数 id 不能为空！";
                return resp;
            }

            return ApprovalHelper.Submit(this.Context, this.Request, "ydj_saleintention", dto.Id);

            //return DoExecute(this.Request, dto);
        }

        //internal static BaseResponse<BaseDataModel> DoExecute(IRequest request, SaleIntentionSubmitDTO dto)
        //{
        //    // 向麦浩系统发送请求
        //    var response = JsonClient.Invoke<CommonBillDTO, DynamicDTOResponse>(
        //        request,
        //        new CommonBillDTO()
        //        {
        //            FormId = "ydj_saleintention",
        //            OperationNo = "submitflow",
        //            SelectedRows = new List<SelectedRow> { new SelectedRow { PkValue = dto.Id } }
        //        });
        //    var result = response?.OperationResult;

        //    var resp = result.ToResponseModel<BaseDataModel>(false);

        //    return resp;
        //}
    }
}