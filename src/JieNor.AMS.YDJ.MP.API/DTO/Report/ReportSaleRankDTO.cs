using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ServiceStack;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 统计分析排行榜接口
    /// </summary>
    [Api("统计分析排行榜接口")]
    [Route("/mpapi/report/salerank")]
    [Authenticate]
    public class ReportSaleRankDTO : BaseDTO
    {
        /// <summary>
        /// 统计周期（按日：0，按周：1，按月：2、按季度：3、按年：4）
        /// </summary>
        public int StatPeriod { get; set; }

        /// <summary>
        /// 统计范围（本店：0；全公司：1）
        /// </summary>
        public int StatScope { get; set; }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// 统计维度（营业额：0，意向金额：1，商机数：2，会员数：3）
        /// </summary>
        public int StatDimension { get; set; }
    }
}
