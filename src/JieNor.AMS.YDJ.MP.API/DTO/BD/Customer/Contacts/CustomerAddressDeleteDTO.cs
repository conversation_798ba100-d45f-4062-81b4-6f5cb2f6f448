using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.MP.API.DTO
{
    /// <summary>
    /// 客户联系人删除接口
    /// </summary>
    [Api("客户联系人删除接口")]
    [Route("/mpapi/customer/addressdelete")]
    [Route("/mpapi/customer/contactsdelete")]
    [Authenticate]
    public class CustomerContactsDeleteDTO : BaseDetailDTO
    {
        /// <summary>
        /// 客户id
        /// </summary>
        public string CustomerId { get; set; }
    }
}