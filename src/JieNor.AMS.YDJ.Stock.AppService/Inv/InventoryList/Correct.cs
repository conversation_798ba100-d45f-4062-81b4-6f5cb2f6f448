using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.Core.Interface.StockInit;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryList
{
    /// <summary>
    /// 即时库存校正逻辑
    /// </summary>
    [InjectService]
    [FormId("stk_inventorylist")]
    [OperationNo("correct")]
    public class Correct : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 操作执行后处理时点
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            //暂时只实现校正所有数据的逻辑
            this.Container.GetService<IStockBaseService>()?.CorrectInventoryData(this.Context, this.Option);
            this.AddRefreshPageAction();
        }
    }
}
