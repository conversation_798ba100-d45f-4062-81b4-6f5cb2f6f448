using JieNor.AMS.YDJ.Store.AppService.PDA.WmsCommon;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.Inv.InventoryTransfer
{
    /// <summary>
    /// 库存调拨单：调出
    /// </summary>
    [InjectService]
    [FormId("stk_inventorytransfer")]
    [OperationNo("allotout")]
    public class AllotOut : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if (e.DataEntitys == null) return;
            foreach (var item in e.DataEntitys)
            {
                item["fisstockout"] = true;//已分步式调出
                item["fiscallout"] = true;//是否做出调出操作
                var entitys = item["fentity"] as DynamicObjectCollection;
                List<string> materialnos = entitys.Select(o=>o["fmaterialid"]?.ToString()).ToList();
                var allMaterials = this.Context.LoadBizDataById("ydj_product", materialnos, true);
                foreach (var en in entitys)
                {
                    if (Check(item["fbillno"]?.ToString()) && Convert.ToInt32(en["fstockoutqty"]) == 0)
                    {
                        en["fstockoutqty"] = en["fbizqty"];//分步式调出数量

                        decimal fstockoutbizqty = UnitConvert(en["funitid"]?.ToString(), Convert.ToDecimal(en["fbizqty"]), en["fbizunitid"]?.ToString(), allMaterials, en["fmaterialid"]?.ToString());

                        en["fstockoutbizqty"] = fstockoutbizqty;//分步式调出基本单位数量

                        en["fstockoutdate"] = DateTime.Now.ToString("yyyy-MM-dd");//分布式调出日期
                    }
                }
            }
            //保存
            this.Context.SaveBizData("stk_inventorytransfer", e.DataEntitys);
        }

        /// <summary>
        /// 单位换算
        /// </summary>
        /// <param name="fbizunitid">单位</param>
        /// <param name="tupqty"></param>
        /// <param name="funitid">基本单位</param>
        /// <param name="allMaterials"></param>
        /// <param name="materialid13"></param>
        /// <returns></returns>
        public decimal UnitConvert(string fbizunitid,decimal tupqty,string funitid,List<DynamicObject> allMaterials,string materialid13)
        {
           decimal fqty = UnitConvertHelper.UnitConvertToBaseById(this.Context, allMaterials, materialid13, fbizunitid, tupqty, funitid);
            return fqty;
        }

        /// <summary>
        /// 判断是否存在调出扫描任务以及任务状态是否为"已完成"
        /// </summary>
        /// <param name="srcBillNo"></param>
        /// <returns></returns>
        public bool Check(string srcBillNo)
        {
            bool flag = false;
            var sql = $"select ftaskstatus from t_bcm_transfertask where ftask_type = 'transferout' and fsourcenumber = '{srcBillNo}'";
            var data = this.DBService.ExecuteDynamicObject(this.Context, sql).FirstOrDefault();
            if (data == null)
            {
                flag = true;
            }
            else
            {
                //任务状态"已完成"
                string ftaskstatus = data["ftaskstatus"]?.ToString();
                if (ftaskstatus != "ftaskstatus_04")
                {
                    flag = true;
                }
            }
            return flag;
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            if (e.DataEntitys == null) return;

            //检查调入库存维度在《即时库存》中是否存在，对不存在的维度插入一条库存量为0的数据
            var allIds = new List<string>();
            foreach (var data in e.DataEntitys)
            {
                var fid = Convert.ToString(data["id"]);
                if (!fid.IsNullOrEmptyOrWhiteSpace())
                {
                    allIds.Add(fid);
                }
            }

            if (!allIds.Any()) return;

            //查询不在即时库存中的维度，存入临时表
            var sql = @"SELECT A.fmainorgid,B.fmaterialid,B.fattrinfoto,B.fattrinfoto_e,B.fstorehouseidto,B.fstorelocationidto,B.fstockstatusto
                        ,B.flotno,B.fmtonoto,B.fcallupcustomdescto,B.fownertypeto,B.fowneridto,B.funitid,B.fstockunitid 
                        ,A.fmainorgid_txt,A.fmainorgid_pid
                        FROM T_STK_INVTRANSFER AS A WITH(NOLOCK) 
                        INNER JOIN T_STK_INVTRANSFERENTRY AS B WITH(NOLOCK) ON A.fid=B.fid 
                        LEFT JOIN T_STK_INVENTORYLIST AS INV WITH(NOLOCK) ON A.fmainorgid=INV.fmainorgid 
                        AND INV.fmaterialid=B.fmaterialid AND INV.fattrinfo_e=B.fattrinfoto_e AND INV.fstorehouseid=B.fstorehouseidto AND INV.fstorelocationid=B.fstorelocationidto 
                        AND INV.fstockstatus=B.fstockstatusto AND INV.flotno=B.flotno AND INV.fmtono=B.fmtonoto AND INV.fcustomdesc=B.fcallupcustomdescto 
                        AND INV.fownertype=B.fownertypeto AND INV.fownerid=B.fowneridto AND INV.funitid=B.funitid AND INV.fstockunitid=B.fstockunitid ";

            var sqlWhere = $"WHERE A.fid IN ('{string.Join("','", allIds)}') AND INV.fid IS NULL ";
            sql += sqlWhere;

            var invDatas = this.DBService.ExecuteDynamicObject(this.Context, sql);
            if (!invDatas.IsNullOrEmptyOrWhiteSpace() && invDatas.Any())
            {
                //如果查询到数据，这创建新的即时库存维度
                var seqSvc = this.Context.Container.GetService<ISequenceService>();
                using (var tran = this.Context.CreateTransaction((int)System.Transactions.TransactionScopeOption.Required))
                {
                    var lstBatchSql = new List<KeyValuePair<string, IEnumerable<SqlParam>>>();
                    foreach (var inv in invDatas)
                    {
                        List<SqlParam> lstParam = new List<SqlParam>();
                        lstParam.AddRange(new SqlParam[]
                        {
                            new SqlParam("fid", System.Data.DbType.String,seqSvc.GetSequence<string>()),
                            new SqlParam("fmainorgid", System.Data.DbType.String,inv["fmainorgid"]),
                            new SqlParam("fmaterialid", System.Data.DbType.String,inv["fmaterialid"]),
                            new SqlParam("fattrinfo", System.Data.DbType.String,inv["fattrinfoto"]),
                            new SqlParam("fattrinfo_e", System.Data.DbType.String,inv["fattrinfoto_e"]),
                            new SqlParam("fstorehouseid", System.Data.DbType.String,inv["fstorehouseidto"]),
                            new SqlParam("fstorelocationid", System.Data.DbType.String,inv["fstorelocationidto"]),
                            new SqlParam("fstockstatus", System.Data.DbType.String,inv["fstockstatusto"]),
                            new SqlParam("flotno", System.Data.DbType.String,inv["flotno"]),
                            new SqlParam("fmtono", System.Data.DbType.String,inv["fmtonoto"]),
                            new SqlParam("fcustomdesc", System.Data.DbType.String,inv["fcallupcustomdescto"]),
                            new SqlParam("fownerid", System.Data.DbType.String,inv["fowneridto"]),
                            new SqlParam("fownertype", System.Data.DbType.String,inv["fownertypeto"]),
                            new SqlParam("funitid", System.Data.DbType.String,inv["funitid"]),
                            new SqlParam("fstockunitid", System.Data.DbType.String,inv["fstockunitid"]),
                            new SqlParam("fmainorgid_txt", System.Data.DbType.String,inv["fmainorgid_txt"]),
                            new SqlParam("fmainorgid_pid", System.Data.DbType.String,inv["fmainorgid_pid"]),
                            new SqlParam("ftranid", System.Data.DbType.String,seqSvc.GetSequence<string>())
                        });
                        var insertSql = $@"
if not exists(SELECT 1 FROM T_STK_INVENTORYLIST WHERE fmainorgid=@fmainorgid AND fmaterialid=@fmaterialid AND fattrinfo_e=@fattrinfo_e AND fcustomdesc=@fcustomdesc
		AND fstorehouseid=@fstorehouseid AND fstorelocationid=@fstorelocationid AND fstockstatus=@fstockstatus 
		AND flotno=@flotno AND fmtono=@fmtono AND fownertype=@fownertype AND fownerid=@fownerid AND funitid=@funitid AND fstockunitid=@fstockunitid)
begin 
	INSERT INTO T_STK_INVENTORYLIST(fid,FFormId,fmainorgid,fmaterialid,fattrinfo,fattrinfo_e,fcustomdesc,fstorehouseid,fstorelocationid,fstockstatus,flotno,fmtono,fownertype,fownerid,funitid,fstockunitid
									,fmainorgid_txt,fmainorgid_pid,fqty,fstockqty,famount,fcostprice,fcostamt,fvolumeunit,ftotalvolume,fsinglevolume,ftranid)
	VALUES(@fid,'stk_inventorylist',@fmainorgid,@fmaterialid,@fattrinfo,@fattrinfo_e,@fcustomdesc,@fstorehouseid,@fstorelocationid,@fstockstatus,@flotno,@fmtono,@fownertype,@fownerid,@funitid,@fstockunitid
									,@fmainorgid_txt,@fmainorgid_pid,0,0,0,0,0,'',0,0,@ftranid)
end
                                        ";
                        lstBatchSql.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(insertSql, lstParam));

                        //再插入扩展数据（所有数量默认为0）
                        var insertExSql = $@"/*dialect*/
            insert into T_STK_INVENTORYLIST_EXTENDDATA (fid, fmainorgid, fqty, fstockqty, fmaterialid, funitid, fstockunitid, fusableqty, fstockusableqty, fintransitqty, fstockintransitqty, freserveqty, fstockreserveqty)
            values(@fid,@fmainorgid,0,0,@fmaterialid,@funitid,@fstockunitid,0,0,0,0,0,0)";
                        lstBatchSql.Add(new KeyValuePair<string, IEnumerable<SqlParam>>(insertExSql, lstParam));
                    }

                    if (lstBatchSql.Any())
                    {
                        var dbServiceEx = this.Context.Container.GetService<IDBServiceEx>();
                        dbServiceEx.ExecuteBatch(this.Context, lstBatchSql);
                    }

                    tran.Complete();
                }
            }
        }
    }
}
