using JieNor.AMS.YDJ.Core.Interface.StockInit;
using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Stock.AppService.OpValidation
{
    /// <summary>
    /// 库存单据保存时检查初始化情况
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_StockBillSaveValidation)]
    public class StockBillSaveValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        /// <summary>
        /// 库存基础服务
        /// </summary>
        [InjectProperty]
        protected IStockBaseService StockBaseService { get; set; }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validationExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validationExpr)
        {
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();

            //库存是否初始化完成
            bool isInventoryInited = this.StockBaseService.IsInitedInventory(userCtx);
            if (!isInventoryInited)
            {
                throw new BusinessException($"{this.OperationName ?? "N/A"}操作失败：库存还未完成初始化！");
            }

            var billNoField = formInfo.GetNumberField();
            var billDateField = formInfo.GetField("fdate") as HtmlDateField;
            if (billDateField == null)
            {
                throw new BusinessException("库存单据必须要有业务日期字段：fdate");
            }

            //获得最近关账日期
            var dtLatestCloseDate = this.StockBaseService.GetLatestInventoryCloseDate(userCtx);
            //最近关账日期校验通过的单据
            var lstValidEntities = new List<DynamicObject>();
            
            foreach (var entity in dataEntities)
            {
                //如果是期初盘点单，且无盘盈盘亏数量的，不需要做关账日期校验
                if(formInfo.Id.EqualsIgnoreCase("stk_inventoryverify") && IsStkVerifyOk(entity))
                {
                    //库存单据业务日期合法
                    lstValidEntities.Add(entity);
                    continue;
                }

                var currBillDate = billDateField.DynamicProperty?.GetValue<DateTime?>(entity);
                if(dtLatestCloseDate==null
                    || dtLatestCloseDate!=null&& dtLatestCloseDate < currBillDate)
                {
                    //库存单据业务日期合法
                    lstValidEntities.Add(entity);
                }
                else
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"编号为{billNoField?.DynamicProperty.GetValue(entity)}的{formInfo.Caption}{this.OperationName}操作失败：{billDateField.Caption}不允许小于当前系统最近关账日期！",
                        DataEntity = entity
                    });
                }
            }

            //获得库存初始化日期
            var dtStockInitDate = this.StockBaseService.GetInitedInventoryDate(userCtx);
            //检查业务日期>=最近关账日期的单据，其业务日期是否小于库存启用日期
            foreach (var entity in lstValidEntities)
            {
                var currBillDate = billDateField.DynamicProperty?.GetValue<DateTime?>(entity);
                if (dtStockInitDate == null)
                {
                    //库存单据业务日期合法
                }
                else if (dtStockInitDate != null && dtStockInitDate.HasValue && dtStockInitDate.Value <= currBillDate.Value)
                { 
                    //库存单据业务日期合法
                }
                else
                {
                    result.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"编号为{billNoField?.DynamicProperty.GetValue(entity)}的{formInfo.Caption}{this.OperationName}操作失败：{billDateField.Caption}不允许小于当前系统库存初始化启用日期【{dtStockInitDate.Value.ToString("yyyy-MM-dd")}】！",
                        DataEntity = entity
                    });
                }
            }

            return result;
        }


        /// <summary>
        /// 检查是否期初盘点单且无盘盈盘亏数量
        /// </summary>
        /// <param name="billData"></param>
        /// <returns></returns>
        private bool IsStkVerifyOk(DynamicObject billData)
        {
            var billTypeId = billData["fbilltype"]?.ToString();
            if(!billTypeId.EqualsIgnoreCase ("inventoryverify_billtype_02"))
            {
                return false;
            }

            var enRows = billData["fentity"] as DynamicObjectCollection;
            if(enRows ==null || enRows.Count ==0)
            {
                return true;
            }

            var haveQty = enRows.Any(f=> Convert.ToDecimal (f["fpyqty"])>0 || Convert.ToDecimal(f["fpyqty"]) > 0);

            return !haveQty;
        }


    }
}
