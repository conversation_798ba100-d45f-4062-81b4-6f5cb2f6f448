using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.PDA.Inventoryverify
{
    /// <summary>
    /// 盘点扫描任务：联查源单类型
    /// </summary>
    [InjectService]
    [FormId("bcm_countscantask")]
    [OperationNo("querysourceformid")]
    public class QuerySourceFormId : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            this.Result.IsSuccess = false;
            base.BeforeExecuteOperationTransaction(e);
            var queryfiled = this.GetQueryOrSimpleParam("queryfiled", "");
            var queryTable = this.GetQueryOrSimpleParam("queryTable", "");
            var entryid = this.GetQueryOrSimpleParam("entryid", "");
            if (string.IsNullOrWhiteSpace(queryfiled) || string.IsNullOrWhiteSpace(queryTable) || string.IsNullOrWhiteSpace(entryid))
            {
                throw new BusinessException("参数都不能为空!");
            }

            var sql = $@"/*dialect*/select {queryfiled} from {queryTable} where fentryid='{entryid}'";
            var dbService = this.Container.GetService<IDBService>();
            var result = dbService.ExecuteDynamicObject(this.Context, sql)?.FirstOrDefault();
            if (result != null)
            {
                this.Result.IsSuccess = true;
                this.Result.SrvData = result[queryfiled];
            }
        }
    }
}
