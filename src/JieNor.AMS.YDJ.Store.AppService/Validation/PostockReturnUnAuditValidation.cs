using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Validation
{
    /// <summary>
    /// 采购退货单检查是否存在已完成的发货扫描任务
    /// </summary>
    [InjectService]
    [LifetimeScope(Enu_LifetimeScope.InstancePerDependency)]
    [ServiceMetaAttribute("validationid", YDJHtmlElementType.HtmlValidator_PostockReturnUnAuditValidation)]
    public class PostockReturnUnAuditValidation : AbstractBaseValidation
    {
        /// <summary>
        /// 强制实体为单据头
        /// </summary>
        public override string EntityKey
        {
            get { return "fbillhead"; }
            set { base.EntityKey = value; }
        }

        /// <summary>
        /// 初始化上下文
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="validExpr"></param>
        protected override void InitializeContext(UserContext userCtx, string validExpr)
        {
            base.InitializeContext(userCtx, validExpr);
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult validationResult = new ValidationResult();

            var deliveryScanTasks = DeliveryScanTasks(dataEntities);

            foreach (var dataEntitie in dataEntities)
            {
                var result = IsHasAuditedDeliveryScanTask(dataEntitie, deliveryScanTasks);
                if (result != null)
                    validationResult.Errors.Add(new ValidationResultEntry()
                    {
                        ErrorMessage = $"当前采购退货单{dataEntitie["fbillno"]}对应发货扫描任务{result}已完成, 不允许反审核!",
                        DataEntity = dataEntitie
                    });
            }

            return validationResult;
        }

        /// <summary>
        /// 是否存在关联的发货扫描任务，并且返回发货扫描任务编号
        /// </summary>
        /// <param name="dataEntitie">当前单据</param>
        /// <param name="deliveryScanTasks">发货扫描任务集合</param>
        /// <returns></returns>
        private string IsHasAuditedDeliveryScanTask(DynamicObject dataEntitie, IEnumerable<DynamicObject> deliveryScanTasks)
        {
            var FFormId = (string)dataEntitie["FFormId"];
            var fbillno = (string)dataEntitie["fbillno"];

            var deliveryScanTask = deliveryScanTasks.FirstOrDefault(item => (string)item["flinkformid"] == FFormId && (string)item["flinkbillno"] == fbillno);
            if (deliveryScanTask == null)
                return null;
            return Convert.ToString(deliveryScanTask["fbillno"]) ?? "";
        }


        /// <summary>
        /// 查询发货扫描任务
        /// </summary>
        /// <param name="fsourceFormIds">来源单据集合</param>
        /// <param name="fsourceBillNos">来源单据编号集合</param>
        /// <returns></returns>
        private IEnumerable<DynamicObject> DeliveryScanTasks(DynamicObject[] dataEntities)
        {
            var FFormIds = dataEntities.Select(item => (string)item["FFormId"]);
            var fbillnos = dataEntities.Select(item => (string)item["fbillno"]);

            var sqlString = $"SELECT de.fbillno ,dt.flinkformid,dt.flinkbillno FROM t_bcm_deliveryscantask AS de JOIN t_bcm_descantaskentity AS dt ON de.fid = dt.fid WHERE dt.flinkformid IN ({ToINSql(FFormIds)}) AND dt.flinkbillno IN ({ToINSql(fbillnos)}) AND de.ftaskstatus = 'ftaskstatus_04' AND de.fmainorgid='{this.Context.Company}'";
            return DBService.ExecuteDynamicObject(this.Context, sqlString);
        }

        /// <summary>
        /// 将集合转为 sql IN查询语句
        /// </summary>
        /// <param name="list"></param>
        /// <returns></returns>
        private string ToINSql(IEnumerable<string> list)
        {
            return string.Join(",", list.Select(item => $"'{item}'"));
        }


    }
}
