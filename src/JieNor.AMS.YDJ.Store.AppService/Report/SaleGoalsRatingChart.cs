using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Report
{
    /// <summary>
    /// 报表：销售目标完成率
    /// </summary>
    [InjectService("GoalsRatingChart")]
    [FormId("rpt_salesgoal")]
    public class SaleGoalsRatingChart : OfficeRptDataService // AbstractQueryDataService
    {
        public override Dictionary<string, string> FilterTitle
        {
            get
            {
                var desc = new Dictionary<string, string>();
                desc.Add("last", DateTime.Now.AddDays(-7).ToString("yyyy-MM-dd"));
                desc.Add("now", DateTime.Now.ToString("yyyy-MM-dd"));
                return desc;
            }
        }

        //      protected override IDataReader BuildQueryData(out ListDesc desc)
        //      {
        //          //按各自的报表取数要求，编写报表取数的业务逻辑，返回数据。

        //          // 考核维度（开单额）/财年/部门/人员
        //          string evalType = this.GetQueryOrSimpleParam<string>("evalType");// 考核维度：开单额 fal_001
        //          string evalYear = this.GetQueryOrSimpleParam<string>("evalYear");// 财年 fy_2017
        //          string evalDept = this.GetQueryOrSimpleParam<string>("evalDept");// 部门id
        //          string evalStaff = this.GetQueryOrSimpleParam<string>("evalStaff");// 人员id
        //          if (string.IsNullOrWhiteSpace(evalYear) || evalYear == "all") evalYear = "fy_" + DateTime.Now.Year.ToString();
        //          string dtYear = evalYear.Substring(3);
        //          int curYear = 0;
        //          if (!int.TryParse(dtYear, out curYear))
        //          {
        //              curYear = DateTime.Now.Year;
        //          }
        //          string[] dtTimes = new string[] { dtYear + "-01-01 00:00:00", (curYear + 1) + "-01-01 00:00:00" };
        //          var dicMonths = GetMonthSpan(dtYear);

        //          #region 参数及查询条件（语句）
        //          // 四个季度
        //          string[] firstQuars = new string[] { dtYear + "-01-01 00:00:00", curYear + "-04-01 00:00:00" };
        //          string[] secondQuars = new string[] { dtYear + "-04-01 00:00:00", curYear + "-07-01 00:00:00" };
        //          string[] thirdQuars = new string[] { dtYear + "-07-01 00:00:00", curYear + "-10-01 00:00:00" };
        //          string[] fourthQuars = new string[] { dtYear + "-10-01 00:00:00", (curYear + 1) + "-01-01 00:00:00" };
        //          StringBuilder sbCondition = new StringBuilder();
        //          StringBuilder sbGoalCondition = new StringBuilder();
        //          StringBuilder sbTargetConditions = new StringBuilder();
        //          if (!string.IsNullOrWhiteSpace(evalDept))
        //          {
        //              sbCondition.AppendFormat(" and od.fdeptid='{0}'", evalDept);
        //              //sbGoalCondition.AppendFormat(" and g.fdeptid='{0}'", evalDept);
        //              sbTargetConditions.AppendFormat(" and (g.fdeptid='{2}' or g.fid in(select fid from t_ste_goal where fassobject='fao_001' and fyear='fy_{0}' and fmainorgid='{1}' and fastaffid in (select fid from T_BD_staff where fdeptid='{2}')) )",
        //                  curYear, this.OperationContext.UserContext.Company, evalDept);
        //              if (!string.IsNullOrWhiteSpace(evalStaff))
        //              {
        //                  sbCondition.AppendFormat(" and ou.fdutyid='{0}'", evalStaff);
        //                  //sbGoalCondition.AppendFormat(" and g.fastaffid='{0}'", evalStaff);
        //                  sbTargetConditions = new StringBuilder();
        //                  sbTargetConditions.AppendFormat(" and g.fid in(select fid from t_ste_goal where fassobject='fao_001' and fyear='fy_{0}' and fmainorgid='{1}' and fastaffid='{2}')",
        //                  curYear, this.OperationContext.UserContext.Company, evalStaff);
        //              }
        //          }
        //          string sqlSelect = string.Format(@"select fid,FFormId,evalType as 'fcheckgoal',(case when fassobject='fao_001' then staffName else deptName end) as fpart, fposition
        //        ,year_goal as 'fyeargoal',isnull(year_finished,0) as 'fyearcomplete'
        //      ,(case when year_goal=0 or isnull(year_finished,0)=0 then 0 else ROUND(year_finished/year_goal*100,2) end) as 'fyearcomrate'
        //        ,first_goal as 'ffirstgoal',isnull(first_finished,0) as 'ffirstcomplete'
        //       ,(case when first_goal=0 or isnull(first_finished,0)=0 then 0 else ROUND(first_finished/first_goal*100,2) end) as 'ffirstcomrate'
        //        ,second_goal as 'fsecondgoal',isnull(second_finished,0) as 'fsecondcomplete'
        //       ,(case when second_goal=0 or isnull(second_finished,0)=0 then 0 else ROUND(second_finished/second_goal*100,2) end) as 'fsecondcomrate'
        //        ,third_goal as 'fthirdgoal',isnull(third_finished,0) as 'fthirdcomplete'
        //        ,(case when third_goal=0 or isnull(third_finished,0)=0 then 0 else ROUND(third_finished/third_goal*100,2) end) as 'fthirdcomrate'
        //        ,fourth_goal as 'ffourthgoal',isnull(fourth_finished,0) as 'ffourthcomplete' 
        //       ,(case when fourth_goal=0 or isnull(fourth_finished,0)=0 then 0 else ROUND(fourth_finished/fourth_goal*100,2) end) as 'ffourthcomrate'
        //      ,faqjm as 'fjangoal',isnull(fjancomplete,0) as 'fjancomplete'
        //       ,(case when faqjm=0 or isnull(fjancomplete,0)=0 then 0 else ROUND(fjancomplete/faqjm*100,2) end) as 'fjancomrate'
        //       ,faqfm as 'ffebgoal',isnull(ffebcomplete,0) as 'ffebcomplete'
        //       ,(case when faqfm=0 or isnull(ffebcomplete,0)=0 then 0 else ROUND(ffebcomplete/faqfm*100,2) end) as 'ffebcomrate'
        //       ,faqmm as 'fmargoal',isnull(fmarcomplete,0) as 'fmarcomplete'
        //       ,(case when faqmm=0 or isnull(fmarcomplete,0)=0 then 0 else ROUND(fmarcomplete/faqmm*100,2) end) as 'fmarcomrate' 
        //       ,ftqapr as 'faprilgoal',isnull(faprilcomplete,0) as 'faprilcomplete'
        //       ,(case when ftqapr=0 or isnull(faprilcomplete,0)=0 then 0 else ROUND(faprilcomplete/ftqapr*100,2) end) as 'faprilcomrate'
        //       ,ftqmay as 'fmaygoal',isnull(fmaycomplete,0) as 'fmaycomplete'
        //       ,(case when ftqmay=0 or isnull(fmaycomplete,0)=0 then 0 else ROUND(fmaycomplete/ftqmay*100,2) end) as 'fmaycomrate'
        //       ,ftqjune as 'fjunegoal',isnull(fjunecomplete,0) as 'fjunecomplete'
        //       ,(case when ftqjune=0 or isnull(fjunecomplete,0)=0 then 0 else ROUND(fjunecomplete/ftqjune*100,2) end) as 'fjunecomrate' 
        //       ,fthjuly as 'fjulygoal',isnull(fjulycomplete,0) as 'fjulycomplete'
        //       ,(case when fthjuly=0 or isnull(fjulycomplete,0)=0 then 0 else ROUND(fjulycomplete/fthjuly*100,2) end) as 'fjulycomrate'
        //       ,fthaug as 'fauggoal',isnull(faugcomplete,0) as 'faugcomplete'
        //       ,(case when fthaug=0 or isnull(faugcomplete,0)=0 then 0 else ROUND(faugcomplete/fthaug*100,2) end) as 'faugcomrate'
        //       ,fthsep as 'fsepgoal',isnull(fsepcomplete,0) as 'fsepcomplete'
        //       ,(case when fthsep=0 or isnull(fsepcomplete,0)=0 then 0 else ROUND(fsepcomplete/fthsep*100,2) end) as 'fsepcomrate'
        //       ,ffoct as 'foctgoal',isnull(foctcomplete,0) as 'foctcomplete'
        //       ,(case when ffoct=0 or isnull(foctcomplete,0)=0 then 0 else ROUND(foctcomplete/ffoct*100,2) end) as 'foctcomrate'
        //       ,ffnov as 'fnovgoal',isnull(fnovcomplete,0) as 'fnovcomplete'
        //       ,(case when ffnov=0 or isnull(fnovcomplete,0)=0 then 0 else ROUND(fnovcomplete/ffnov*100,2) end) as 'fnovcomrate'
        //       ,ffdec as 'fdecgoal',isnull(fdeccomplete,0) as 'fdeccomplete'
        //       ,(case when ffdec=0 or isnull(fdeccomplete,0)=0 then 0 else ROUND(fdeccomplete/ffdec*100,2) end) as 'fdeccomrate' 
        //         from (select g.fid,g.FFormId,ff.fname as 'staffName',g.fassobject,et.fenumitem as 'evalType',ee.fenumitem as 'fposition',
        //         g.fdeptid as 'goalDeptId',dp.fname as 'deptName',dp.fid as 'deptfid',g.frelateman as 'year_goal',
        //       (select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={1} and od.forderdate<{2} and
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'year_finished',
        //       g.faquarter as 'first_goal',(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={3} and od.forderdate<{4} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'first_finished',
        //       g.ftquarter as 'second_goal',(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={5} and od.forderdate<{6} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'second_finished',
        //       g.fthrqarter as 'third_goal', (select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={7} and od.forderdate<{8} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'third_finished',
        //       g.ffqarter as 'fourth_goal',(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={9} and od.forderdate<{10} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fourth_finished'
        //      ,faqjm,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={13} and od.forderdate<{14} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fjancomplete'
        //       ,faqfm,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={15} and od.forderdate<{16} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'ffebcomplete'
        //       ,faqmm,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={17} and od.forderdate<{18} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fmarcomplete'
        //       ,ftqapr,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={19} and od.forderdate<{20} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'faprilcomplete'
        //       ,ftqmay,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={21} and od.forderdate<{22} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fmaycomplete'
        //       ,ftqjune,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={23} and od.forderdate<{24} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fjunecomplete'
        //       ,fthjuly,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={25} and od.forderdate<{26} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fjulycomplete'
        //       ,fthaug,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={27} and od.forderdate<{28} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'faugcomplete'
        //       ,fthsep,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={29} and od.forderdate<{30} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fsepcomplete'
        //       ,ffoct,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={31} and od.forderdate<{32} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'foctcomplete'
        //       ,ffnov,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={33} and od.forderdate<{34} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fnovcomplete'
        //       ,ffdec,(select sum(ou.famount) from t_ydj_order od left join t_ydj_orderduty ou on od.fid=ou.fid where od.forderdate>={35} and od.forderdate<{36} and 
        //      (case when g.fassobject='fao_001' and od.fstaffid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
        //      and od.fmainorgid='{37}' {0}) as 'fdeccomplete'
        //         from t_ste_goal g left join T_BD_staff ff on g.fastaffid=ff.fid
        //        left join T_BD_department dp on g.fdeptid=dp.fid and dp.fmainorgid='{37}'
        //        left join T_BD_ENUMDATAENTRY ee on g.fassobject=ee.fentryid
        //        left join T_BD_ENUMDATAENTRY et on g.fasslatitude=et.fentryid
        //        where g.fmainorgid='{37}' and g.fyear='{11}' {38} {12}) a",
        //sbCondition.Length < 5 ? "" : sbCondition.ToString(), "{ts '" + dtTimes[0] + "'}", "{ts '" + dtTimes[1] + "'}"
        //, "{ts '" + firstQuars[0] + "'}", "{ts '" + firstQuars[1] + "'}", "{ts '" + secondQuars[0] + "'}", "{ts '" + secondQuars[1] + "'}"
        //, "{ts '" + thirdQuars[0] + "'}", "{ts '" + thirdQuars[1] + "'}", "{ts '" + fourthQuars[0] + "'}", "{ts '" + fourthQuars[1] + "'}"
        //, evalYear, sbGoalCondition.Length < 5 ? "" : sbGoalCondition.ToString()
        //, "{ts '" + dtYear + "-01-01 00:00:00" + "'}", dicMonths[dtYear + "-01-01"], "{ts '" + dtYear + "-02-01 00:00:00" + "'}", dicMonths[dtYear + "-02-01"], "{ts '" + dtYear + "-03-01 00:00:00" + "'}", dicMonths[dtYear + "-03-01"], "{ts '" + dtYear + "-04-01 00:00:00" + "'}", dicMonths[dtYear + "-04-01"]
        //, "{ts '" + dtYear + "-05-01 00:00:00" + "'}", dicMonths[dtYear + "-05-01"], "{ts '" + dtYear + "-06-01 00:00:00" + "'}", dicMonths[dtYear + "-06-01"], "{ts '" + dtYear + "-07-01 00:00:00" + "'}", dicMonths[dtYear + "-07-01"], "{ts '" + dtYear + "-08-01 00:00:00" + "'}", dicMonths[dtYear + "-08-01"]
        //, "{ts '" + dtYear + "-09-01 00:00:00" + "'}", dicMonths[dtYear + "-09-01"], "{ts '" + dtYear + "-10-01 00:00:00" + "'}", dicMonths[dtYear + "-10-01"], "{ts '" + dtYear + "-11-01 00:00:00" + "'}", dicMonths[dtYear + "-11-01"], "{ts '" + dtYear + "-12-01 00:00:00" + "'}", dicMonths[dtYear + "-12-01"]
        //, this.OperationContext.UserContext.Company
        //, sbTargetConditions.Length < 5 ? "" : sbTargetConditions.ToString()
        //);
        //          string sqlCount = string.Format("select count(1) from ( {0} ) x ", sqlSelect);
        //          #endregion

        //          desc = new ListDesc();
        //          var dbSvc = this.UserCtx.Container.GetService<IDBService>();
        //          using (var reader = dbSvc.ExecuteReader(this.UserCtx, sqlCount))
        //          {
        //              while (reader.Read())
        //              {
        //                  desc.CurrentRows = Convert.ToInt64(reader[0]);
        //                  break;
        //              }
        //          }
        //          var data = dbSvc.ExecuteReader(this.UserCtx, sqlSelect);
        //          return data;
        //      }

        protected override IDataReader BuildQueryData(out ListDesc desc)
        {
            //按各自的报表取数要求，编写报表取数的业务逻辑，返回数据。

            // 考核维度（开单额）/财年/部门/人员
            string evalType = this.GetQueryOrSimpleParam<string>("evalType");// 考核维度：开单额 fal_001,fal_002,fal_003
            string evalYear = this.GetQueryOrSimpleParam<string>("evalYear");// 财年 fy_2017
            string evalDept = this.GetQueryOrSimpleParam<string>("evalDept");// 部门id
            string evalStaff = this.GetQueryOrSimpleParam<string>("evalStaff");// 人员id
            var evalObject = this.GetQueryOrSimpleParam<string>("evalObject");//考核维度fao_001,fao_002
            if (string.IsNullOrWhiteSpace(evalYear) || evalYear == "all") evalYear = "fy_" + DateTime.Now.Year.ToString();
            string dtYear = evalYear.Substring(3);
            int curYear = 0;
            if (!int.TryParse(dtYear, out curYear))
            {
                curYear = DateTime.Now.Year;
            }

            //加载年度时间和四季时间
            var dtTimes = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>($"{curYear}-01-01 00:00:00", $"{(curYear + 1)}-01-01 00:00:00"),
                new KeyValuePair<string, string>($"{curYear}-01-01 00:00:00", $"{curYear}-04-01 00:00:00"),
                new KeyValuePair<string, string>($"{curYear}-04-01 00:00:00", $"{curYear}-07-01 00:00:00"),
                new KeyValuePair<string, string>($"{curYear}-07-01 00:00:00", $"{curYear}-10-01 00:00:00"),
                new KeyValuePair<string, string>($"{curYear}-10-01 00:00:00", $"{curYear + 1}-01-01 00:00:00")
            };
            
            //加载12个月的时间
            for(var i = 1; i <= 12; i++)
            {
                dtTimes.Add(new KeyValuePair<string, string>(string.Format("{0}-{1:d2}-01 00:00:00", curYear, i),
                    i < 12 ? string.Format("{0}-{1:d2}-01 00:00:00", curYear, i + 1) : $"{curYear + 1}-01-01 00:00:00"));
            }

            #region 参数及查询条件（语句）
            // 四个季度
            StringBuilder sbCondition = new StringBuilder();
            StringBuilder sbTargetConditions = new StringBuilder();
            var staticLeftStaffSql = @"
left join t_bd_staff ff with(nolock) on ou.fdutyid = ff.fid and ff.fmainorgid = od.fmainorgid
left join t_bd_staffentry sf with(nolock) on ff.fid = sf.fid";
            var dynamicLeftStaffSql = string.Empty;

            switch (evalObject)
            {
                case "fao_002":
                    sbTargetConditions.Append(" and g.fassobject='fao_002'");
                    if (!string.IsNullOrWhiteSpace(evalDept))
                    {
                        sbCondition.AppendFormat(" and od.fdeptid='{0}'", evalDept);
                        sbTargetConditions.AppendFormat(" and g.fdeptid='{0}' ",evalDept);
                    }
                    break;
                case "fao_001":
                    sbTargetConditions.Append(" and g.fassobject='fao_001'");

                    if (!string.IsNullOrWhiteSpace(evalStaff) && string.IsNullOrWhiteSpace(evalDept))
                    {
                        sbCondition.AppendFormat(" and ou.fdutyid='{0}'", evalStaff);
                        sbTargetConditions.AppendFormat(" and g.fastaffid='{0}'", evalStaff);
                    }
                    else if (!string.IsNullOrWhiteSpace(evalStaff) && !string.IsNullOrWhiteSpace(evalDept))
                    {
                        sbCondition.AppendFormat(" and ou.fdutyid='{0}'", evalStaff);
                        sbCondition.AppendFormat(" and sf.fdeptid='{0}'", evalDept);
                        sbTargetConditions.AppendFormat(" and g.fastaffid='{0}'", evalStaff);
                        dynamicLeftStaffSql = staticLeftStaffSql;
                    }
                    else if(string.IsNullOrWhiteSpace(evalStaff) && !string.IsNullOrWhiteSpace(evalDept))
                    {
                        sbCondition.AppendFormat(" and sf.fdeptid='{0}'", evalDept);
                        sbTargetConditions.AppendFormat(" and g.fastaffid in (select s.fid from t_bd_staff s with(nolock) left join t_bd_staffentry se with(nolock) on se.fid=s.fid where se.fdeptid='{0}')", evalDept);
                        dynamicLeftStaffSql = staticLeftStaffSql;
                    }
                    break;
            }

            var evalTypeSql = string.Empty;
            evalType = string.IsNullOrWhiteSpace(evalType) ? "fal_001" : evalType;

            switch (evalType)
            {
                case "fal_001":
                    evalTypeSql = @"
select sum(ou.famount) 
from t_ydj_order od with(nolock) 
left join t_ydj_orderduty ou with(nolock) on od.fid=ou.fid 
{2}
where od.forderdate>={{{{ts '{{0}}'}}}} and od.forderdate<{{{{ts '{{1}}'}}}} and
(case when g.fassobject='fao_001' and ou.fdutyid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
and od.fmainorgid='{0}' and od.fcancelstatus='0' {1}
";
                    sbTargetConditions.Append(" and g.fasslatitude='fal_001' ");
                    break;
                case "fal_002":
                    evalTypeSql = @"
select sum(ou.famount) 
from t_ydj_saleintention od with(nolock) 
left join t_ydj_orderduty ou with(nolock) on od.fid=ou.fid 
{2}
where od.fdate>={{{{ts '{{0}}'}}}} and od.fdate<{{{{ts '{{1}}'}}}} and
(case when g.fassobject='fao_001' and ou.fdutyid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
and od.fmainorgid='{0}' and od.fcancelstatus='0' {1}
";
                    sbTargetConditions.Append(" and g.fasslatitude='fal_002' ");
                    break;
                case "fal_003":
                    evalTypeSql = @"
select count(distinct od.fid) 
from t_ydj_customerrecord od with(nolock) 
left join t_ydj_customerrecordduty ou with(nolock) on od.fid=ou.fid 
{2}
where od.fgoshopdate>={{{{ts '{{0}}'}}}} 
and od.fgoshopdate<{{{{ts '{{1}}'}}}} and
(case when g.fassobject='fao_001' and ou.fdutyid=g.fastaffid then 1 when g.fassobject='fao_002' and od.fdeptid=g.fdeptid then 1 else 0 end)=1
and od.fmainorgid='{0}' and od.fcancelstatus='0'  and (fchancestatus='chance_status_02' or fchancestatus='chance_status_03') {1}
";
                    sbTargetConditions.Append(" and g.fasslatitude='fal_003' ");
                    break;
                default:
                    throw new BusinessException($"系统不存在{evalType}的考核维度");
            }

            evalTypeSql = string.Format(evalTypeSql, this.UserCtx.Company, sbCondition.ToString(), dynamicLeftStaffSql);

            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.Append(@"
select fid,FFormId,evalType as 'fcheckgoal',(case when fassobject='fao_001' then staffName else deptName end) as fpart, fposition
  ,year_goal as 'fyeargoal',isnull(year_finished,0) as 'fyearcomplete'
,(case when year_goal=0 or isnull(year_finished,0)=0 then 0 else ROUND(year_finished/year_goal*100,2) end) as 'fyearcomrate'
  ,first_goal as 'ffirstgoal',isnull(first_finished,0) as 'ffirstcomplete'
 ,(case when first_goal=0 or isnull(first_finished,0)=0 then 0 else ROUND(first_finished/first_goal*100,2) end) as 'ffirstcomrate'
  ,second_goal as 'fsecondgoal',isnull(second_finished,0) as 'fsecondcomplete'
 ,(case when second_goal=0 or isnull(second_finished,0)=0 then 0 else ROUND(second_finished/second_goal*100,2) end) as 'fsecondcomrate'
  ,third_goal as 'fthirdgoal',isnull(third_finished,0) as 'fthirdcomplete'
  ,(case when third_goal=0 or isnull(third_finished,0)=0 then 0 else ROUND(third_finished/third_goal*100,2) end) as 'fthirdcomrate'
  ,fourth_goal as 'ffourthgoal',isnull(fourth_finished,0) as 'ffourthcomplete' 
 ,(case when fourth_goal=0 or isnull(fourth_finished,0)=0 then 0 else ROUND(fourth_finished/fourth_goal*100,2) end) as 'ffourthcomrate'
,faqjm as 'fjangoal',isnull(fjancomplete,0) as 'fjancomplete'
 ,(case when faqjm=0 or isnull(fjancomplete,0)=0 then 0 else ROUND(fjancomplete/faqjm*100,2) end) as 'fjancomrate'
 ,faqfm as 'ffebgoal',isnull(ffebcomplete,0) as 'ffebcomplete'
 ,(case when faqfm=0 or isnull(ffebcomplete,0)=0 then 0 else ROUND(ffebcomplete/faqfm*100,2) end) as 'ffebcomrate'
 ,faqmm as 'fmargoal',isnull(fmarcomplete,0) as 'fmarcomplete'
 ,(case when faqmm=0 or isnull(fmarcomplete,0)=0 then 0 else ROUND(fmarcomplete/faqmm*100,2) end) as 'fmarcomrate' 
 ,ftqapr as 'faprilgoal',isnull(faprilcomplete,0) as 'faprilcomplete'
 ,(case when ftqapr=0 or isnull(faprilcomplete,0)=0 then 0 else ROUND(faprilcomplete/ftqapr*100,2) end) as 'faprilcomrate'
 ,ftqmay as 'fmaygoal',isnull(fmaycomplete,0) as 'fmaycomplete'
 ,(case when ftqmay=0 or isnull(fmaycomplete,0)=0 then 0 else ROUND(fmaycomplete/ftqmay*100,2) end) as 'fmaycomrate'
 ,ftqjune as 'fjunegoal',isnull(fjunecomplete,0) as 'fjunecomplete'
 ,(case when ftqjune=0 or isnull(fjunecomplete,0)=0 then 0 else ROUND(fjunecomplete/ftqjune*100,2) end) as 'fjunecomrate' 
 ,fthjuly as 'fjulygoal',isnull(fjulycomplete,0) as 'fjulycomplete'
 ,(case when fthjuly=0 or isnull(fjulycomplete,0)=0 then 0 else ROUND(fjulycomplete/fthjuly*100,2) end) as 'fjulycomrate'
 ,fthaug as 'fauggoal',isnull(faugcomplete,0) as 'faugcomplete'
 ,(case when fthaug=0 or isnull(faugcomplete,0)=0 then 0 else ROUND(faugcomplete/fthaug*100,2) end) as 'faugcomrate'
 ,fthsep as 'fsepgoal',isnull(fsepcomplete,0) as 'fsepcomplete'
 ,(case when fthsep=0 or isnull(fsepcomplete,0)=0 then 0 else ROUND(fsepcomplete/fthsep*100,2) end) as 'fsepcomrate'
 ,ffoct as 'foctgoal',isnull(foctcomplete,0) as 'foctcomplete'
 ,(case when ffoct=0 or isnull(foctcomplete,0)=0 then 0 else ROUND(foctcomplete/ffoct*100,2) end) as 'foctcomrate'
 ,ffnov as 'fnovgoal',isnull(fnovcomplete,0) as 'fnovcomplete'
 ,(case when ffnov=0 or isnull(fnovcomplete,0)=0 then 0 else ROUND(fnovcomplete/ffnov*100,2) end) as 'fnovcomrate'
 ,ffdec as 'fdecgoal',isnull(fdeccomplete,0) as 'fdeccomplete'
 ,(case when ffdec=0 or isnull(fdeccomplete,0)=0 then 0 else ROUND(fdeccomplete/ffdec*100,2) end) as 'fdeccomrate' 
   from (select g.fid,g.FFormId,ff.fname as 'staffName',g.fassobject,
   (case fasslatitude when 'fal_001' then '开单额' when 'fal_002' then '意向额' when 'fal_003' then '商机数' else '' end) as 'evalType',
   (case g.fassobject when 'fao_001' then '员工' when 'fao_002' then '部门' else '' end) as 'fposition',
   g.fdeptid as 'goalDeptId',dp.fname as 'deptName',dp.fid as 'deptfid'");


            var fieldKeys = new[] { "frelateman", "faquarter", "ftquarter", "fthrqarter", "ffqarter", "faqjm", "faqfm", "faqmm", "ftqapr", "ftqmay", "ftqjune", "fthjuly", "fthaug", "fthsep", "ffoct", "ffnov", "ffdec" };
            var fieldAliasKeys = new[] { "year_goal", "first_goal", "second_goal", "third_goal", "fourth_goal", "faqjm", "faqfm", "faqmm", "ftqapr", "ftqmay", "ftqjune", "fthjuly", "fthaug", "fthsep", "ffoct", "ffnov", "ffdec" };
            var aliasKeys = new[] { "year_finished", "first_finished", "second_finished", "third_finished", "fourth_finished", "fjancomplete", "ffebcomplete", "fmarcomplete", "faprilcomplete", "fmaycomplete", "fjunecomplete", "fjulycomplete", "faugcomplete", "fsepcomplete", "foctcomplete", "fnovcomplete", "fdeccomplete" };

            for(var i = 0; i < 17; i++)
            {
                var dtTime = dtTimes[i];
                var dtTimeSql = string.Format(evalTypeSql, dtTime.Key, dtTime.Value);
                sqlBuilder.AppendFormat(",g.{0} as '{1}',({3}) as '{2}'", fieldKeys[i], fieldAliasKeys[i], aliasKeys[i], dtTimeSql);
                sqlBuilder.AppendLine();
            }

            sqlBuilder.AppendFormat(@"
          from t_ste_goal g with(nolock) 
          left join T_BD_staff ff with(nolock) on g.fastaffid=ff.fid
          left join T_BD_department dp with(nolock) on g.fdeptid=dp.fid and dp.fmainorgid='{0}'
          where g.fmainorgid='{0}' and g.fyear='{1}' {2}) a
", this.UserCtx.Company,evalYear, sbTargetConditions.ToString());

            var sqlSelect = sqlBuilder.ToString();
            string sqlCount = string.Format("select count(1) from ( {0} ) x ", sqlSelect);
            #endregion

            desc = new ListDesc();
            var dbSvc = this.UserCtx.Container.GetService<IDBService>();
            using (var reader = dbSvc.ExecuteReader(this.UserCtx, sqlCount))
            {
                while (reader.Read())
                {
                    desc.CurrentRows = Convert.ToInt64(reader[0]);
                    break;
                }
            }
            var data = dbSvc.ExecuteReader(this.UserCtx, sqlSelect);
            return data;
        }

        //数据源
        protected override void BuildRptChartDataSource(UserContext userCtx, ReportOperationContext rptOperCtx, ReportDataSource rptDataSource)
        {
            if (rptDataSource != null && rptDataSource.RptGridDataSource.Count() > 0)
            {
                //List<Dictionary<string, object>> rptGridData = new List<Dictionary<string, object>>();
                //List<Dictionary<string, object>> chartData = rptDataSource.RptGridDataSource.ToList();
                //for (int x = 0; x < chartData.Count; x++)
                //{
                //    var tmpData = chartData[x];
                //    Dictionary<string, object> items = new Dictionary<string, object>();
                //    foreach (var dic in tmpData)
                //    {
                //        items.Add(dic.Key, GetFmtVal(dic.Key, dic.Value));
                //    }
                //    rptGridData.Add(items);
                //}
                //rptDataSource.RptGridDataSource = rptGridData.AsEnumerable();

                rptDataSource.RptGridDataSource = rptDataSource.RptGridDataSource.ToList();
            }
        }

        // 构建报表图表数据源
        protected override void BuildRptChartDataDesc(UserContext userCtx, ReportOperationContext rptOperCtx, ReportModelDesc rptModelDesc)
        {

        }

        #region 自定义方法
        private string GetFmtVal(string key, object obj)
        {
            if (obj == null || string.IsNullOrWhiteSpace(obj.ToString())) return "";
            if (key.EndsWith("goal") || key.EndsWith("complete") || key.EndsWith("comrate"))
            {
                decimal val = 0.0M;
                if (decimal.TryParse(obj.ToString(), out val))
                {
                    if (key.EndsWith("comrate"))
                        return val.ToString("f2") + "%";
                    return val.ToString(val > 0 ? "###,###.00" : "f2");
                }
            }
            return obj.ToString();
        }

        private Dictionary<string, string> GetMonthSpan(int curYear)
        {
            Dictionary<string, string> dicMonth = new Dictionary<string, string>();
            for (int i = 1; i <= 12; i++)
            {
                dicMonth.Add(string.Format("{0}-{1}-01", curYear, (i < 10 ? ("0" + i.ToString()) : i.ToString())),
                    "{ts '" + string.Format("{0}-{1}-01 00:00:00", (i == 12) ? (curYear + 1) : curYear, ((i + 1) < 10 ? ("0" + (i + 1).ToString()) : (i == 12 ? "01" : (i + 1).ToString()))) + "'}");
            }
            return dicMonth;
        }
        #endregion
    }
}
