using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BillConvert
{
    /// <summary>
    /// 其它出库申请生成其它出库单
    /// </summary>
    [InjectService]
    [FormId("stk_otherstockout")]
    [OperationNo("stk_otherstockoutreq2stk_otherstockout")]
    public class OtherStockOutReq2OtherStockOutConvertPlugIn : BaseScanTaskConvertPlugIn
    {
    }
}
