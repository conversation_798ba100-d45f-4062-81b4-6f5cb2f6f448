using JieNor.AMS.YDJ.MS.API.DTO.Order.DirectOrder;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STK.InventoryVerify
{
    /// <summary>
    /// 盘点单，接受SAP回传总部状态
    /// </summary>
    [InjectService]
    [FormId("stk_inventoryverify")]
    [OperationNo("returnheadquarterstatus")]
    public class ReturnHeadQuarterStatus : AbstractOperationServicePlugIn
    {
        private List<ReturnDirectOrderHeadquarterStatusDTO> SapInfo { set; get; }
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            var sapInfoStr = this.GetQueryOrSimpleParam<string>("sapInfo", "");
            if (!sapInfoStr.IsNullOrEmptyOrWhiteSpace())
            {
                this.SapInfo = JsonConvert.DeserializeObject<List<ReturnDirectOrderHeadquarterStatusDTO>>(sapInfoStr);
            }
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //错误消息
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var isTrue = true;
                //总部状态
                var chstatus = Convert.ToString(newData["fchstatus"]);
                if (!chstatus.IsNullOrEmptyOrWhiteSpace())
                {
                    //'1':'已提交总部','2':'已驳回','3':'已终审'
                    switch (chstatus)
                    {
                        case "3":
                            var tempStr = string.Empty;
                            var sapOrderStatus = this.GetQueryOrSimpleParam<string>("sapOrderStatus", "");
                            if (!sapOrderStatus.IsNullOrEmptyOrWhiteSpace())
                            {
                                if (sapOrderStatus.Equals("2"))
                                {
                                    tempStr = "驳回";
                                }
                                else if (sapOrderStatus.Equals("3"))
                                {
                                    tempStr = "再次终审";
                                }
                            }
                            errorMessage = $"该盘点单订单【{Convert.ToString(newData["fbillno"])}】已终审，不能{tempStr}！";
                            isTrue = false;
                            break;
                    }
                }
                return isTrue;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var saveOrderDys = new List<DynamicObject>();
            if (this.Result.ComplexMessage.SuccessMessages != null && this.Result.ComplexMessage.SuccessMessages.Any())
            {
                this.Result.ComplexMessage.SuccessMessages.Clear();
            }
            List<DynamicObject> auditObj = new List<DynamicObject>();
            foreach (var orderDy in e.DataEntitys)
            {
                if (this.SapInfo != null && this.SapInfo.Any())
                {
                    var orderNo = Convert.ToString(orderDy["fbillno"]);

                    var findSapInfo = this.SapInfo.FirstOrDefault(x => x.OrderBillNo.Equals(orderNo));

                    if (findSapInfo != null)
                    {
                        switch (findSapInfo.SapOrderStatus.ToLower())
                        {
                            case "2":
                                //总部状态
                                orderDy["fchstatus"] = "2";
                                //总部驳回原因(终审了，那就要把这个清除)
                                orderDy["fheadquartsyncmessage"] = findSapInfo.SapRejectReason;
                                orderDy["fheadcontracttype"] = findSapInfo.SapOrderType;
                                //总部终审时间
                                orderDy["fheadquartfrtime"] = null;
                                break;
                            //'1':'已提交总部','2':'已驳回','3':'已终审'
                            case "3":
                                //总部状态
                                orderDy["fchstatus"] = "3";
                                //sap合同号
                                orderDy["fheadquartno"] = findSapInfo.SapOrderNo;
                                //总部终审时间
                                orderDy["fheadquartfrtime"] = findSapInfo.SapFinalAuditTime;
                                //总部驳回原因(终审了，那就要把这个清除)
                                if (!findSapInfo.SapRejectReason.IsNullOrEmptyOrWhiteSpace())
                                {
                                    orderDy["fheadquartsyncmessage"] = findSapInfo.SapRejectReason;
                                }
                                else
                                {
                                    orderDy["fheadquartsyncmessage"] = string.Empty;
                                }
                                auditObj.Add(orderDy);
                                orderDy["fheadcontracttype"] = findSapInfo.SapOrderType;
                                break;
                            default:
                                break;
                        }
                        saveOrderDys.Add(orderDy);
                        this.Result.ComplexMessage.SuccessMessages.Add($"盘点单【{Convert.ToString(orderDy["fbillno"])}】回传审批状态成功！");
                    }
                    else
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add($"盘点单【{Convert.ToString(orderDy["fbillno"])}】找不到对应传进来的中台数据包，请检查!");
                    }

                }
                else
                {
                    this.Result.ComplexMessage.ErrorMessages.Add("没有传对应的数据包进来，请检查");
                }
            }

            if (saveOrderDys != null && saveOrderDys.Any())
            {
                e.DataEntitys = saveOrderDys.ToArray();
                this.Context.SaveBizData(this.HtmlForm.Id, saveOrderDys);
                //如果有需要审核的单据，进行审核操作

                if (auditObj.Any())
                {
                    // 示例：调用费用应付单的同步操作
                    var result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, auditObj, "auditflow",
                           new Dictionary<string, object>
                           {
                                { "IgnoreCheckPermssion", true }, { "IgnoreValidateDataEntities", true }
                           });
                    result?.ThrowIfHasError();
                }
                this.Result.IsSuccess = true;
            }
            else
            {
                this.Result.IsSuccess = false;
            }
        }
    }
}