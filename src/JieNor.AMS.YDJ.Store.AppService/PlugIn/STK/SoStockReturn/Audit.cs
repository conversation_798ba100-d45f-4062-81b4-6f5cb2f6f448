using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.CustomException;
using System.Text;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.Utils;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Helpers;
using JieNor.Framework.Enums;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using JieNor.AMS.YDJ.Core.Interface.StockUpdate;
using JieNor.AMS.YDJ.Core.Helpers;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.SoStockReturn
{
    /// <summary>
    /// 销售退货单审核：更新销售合同成本信息
    /// </summary>
    [InjectService]
    [FormId("stk_sostockreturn")]
    [OperationNo("Audit")]
    public class Audit : AbstractOperationServicePlugIn
    {
        private List<DynamicObject> auditBeforeOrder { get; set; }
        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);


            var svc = this.Container.GetService<ICostCalulateService>();
            svc.UpdateSalesOrderCostInfo(this.Context, this.HtmlForm, e.DataEntitys?.ToList());
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            // 退货单只反写合同关联的二级经销商采购订单【一级合同状态】为“订单已出库”，且自动重新计算二级销售合同和采购订单关闭状态
            var stockInfo = this.Context.LoadBizBillHeadDataById("stk_sostockout", e.DataEntitys.Select(x => Convert.ToString(x["fsourceinterid"]))?.ToList(), "fsourcetype,fsourceinterid");
            if (stockInfo != null && stockInfo.Any())
            {
                ResellerHelper.WriteBackPurchaseOrdersBySoStockOuts(
                this.OperationContext,
                stockInfo,
                OneLvOrderSratusConst.OutStock);
            }
            // 反写销售合同【流程状态】
            Core.Helpers.LinkProHelper.WriteBackOrderLinkPro(
                this.Context, this.HtmlForm, e.DataEntitys);

            //筛选【退货类型】为“正常退换”的单据
            List<DynamicObject> filterObjs = e.DataEntitys.Where(t => Convert.ToString(t["freturntype"]).EqualsIgnoreCase("sostockreturn_biztype_01")).ToList();

            //反写销售合同【销售退换中数量】
            OrderQtyWriteBackHelper.WriteBackReturningQty(this.Context, this.HtmlForm, filterObjs);

            //获取源头单据销售出库单信息
            var soStockOutNos = filterObjs.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "stk_sostockout").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var soStockOuts = this.Context.LoadBizDataByFilter("stk_sostockout", " fbillno in ('{0}') ".Fmt(string.Join("','", soStockOutNos)));
            //获取源头单据销售合同信息
            var fsourcenumbers = soStockOuts.Where(t => !t["fsourcenumber"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(t["fsourcetype"]) == "ydj_order").Select(t => Convert.ToString(t["fsourcenumber"])).Distinct().ToList();
            var orders = this.Context.LoadBizDataByFilter("ydj_order", " fbillno in ('{0}') ".Fmt(string.Join("','", fsourcenumbers)));
            foreach (var order in orders)
            {
                DocumentStatusHelper.CalcOrderCloseStatus(order, this.Context);
            }
            this.Context.SaveBizData("ydj_order", orders);

            ProductDelistingHelper.DealDelistingDataByStockOut(this.Context, this.HtmlForm, this.auditBeforeOrder,  e.DataEntitys.ToList(), "stockoutreturnaudit");
        }

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            this.auditBeforeOrder = ProductDelistingHelper.GetOrderData(this.Context, e.DataEntitys.ToList(), this.HtmlForm);
        }
    }
}