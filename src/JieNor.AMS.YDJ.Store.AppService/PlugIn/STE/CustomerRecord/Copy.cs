using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.CustomerRecord
{
    /// <summary>
    /// 销售机会：复制
    /// </summary>
    [InjectService]
    [FormId("ydj_customerrecord")]
    [OperationNo("chancecopy")]
    public class Copy : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);

            #region 参数处理
            var opId = this.GetQueryOrSimpleParam<string>("id");
            var staffId = this.GetQueryOrSimpleParam<string>("dutyId");
            if (opId.IsNullOrEmptyOrWhiteSpace() || staffId.IsNullOrEmptyOrWhiteSpace())
                throw new BusinessException("请求参数错误！");

            var metaService = this.Context.Container.GetService<IMetaModelService>();
            var custrMeta = metaService.LoadFormModel(this.Context, this.HtmlForm.Id);
            var dm = this.Context.Container.GetService<IDataManager>();
            dm.InitDbContext(this.Context, custrMeta.GetDynamicObjectType(this.Context));

            DynamicObject dataEntity = dm.Select(opId) as DynamicObject;
            if (dataEntity == null) throw new BusinessException("当前单据不存在！");
            //加载引用数据
            this.Container.GetService<LoadReferenceObjectManager>()?.Load(
                this.Context,
                this.HtmlForm.GetDynamicObjectType(this.Context),
                dataEntity,
                false);
            #endregion

            #region 新增销售机会数据
            DynamicObject copyObj = OrmUtils.Clone(dataEntity, false, true) as DynamicObject;
            copyObj["fcreatorid"] = this.Context.UserId;
            copyObj["fcreatedate"] = DateTime.Now;
            copyObj["fbillno"] = "";
            copyObj["fstatus"] = "B";
            copyObj["fchancestatus"] = "chance_status_01";
            copyObj["fmodifierid"] = null;
            copyObj["fmodifydate"] = null;
            //最后跟进时间
            copyObj["ffollowtime"] = null;
            //最后跟进人
            copyObj["ffollowerid"] = null;
            copyObj["fchancesource"] = dataEntity["fbillno"];//源机会
            copyObj["fsourcenumber"] = dataEntity["fbillno"];
            copyObj["fintentionno"] = "";
            copyObj["forderno"] = "";

            var staffHtmlForm = metaService.LoadFormModel(this.Context, "ydj_staff");
            var staffDm = this.Container.GetService<IDataManager>();
            staffDm.InitDbContext(this.Context, staffHtmlForm.GetDynamicObjectType(this.Context));
            DynamicObject staffDyo = staffDm.Select(staffId) as DynamicObject;
            if (staffDyo == null) throw new BusinessException("选定的负责人不存在!");

            copyObj["fdutyid"] = staffId;
            copyObj["fdutyid_ref"] = staffDyo;
            copyObj["fdeptid"] = staffDyo["fdeptid"];
            copyObj["fdeptid_ref"] = null;
            this.Container.GetService<LoadReferenceObjectManager>()?.Load(
                this.Context,
                staffHtmlForm.GetDynamicObjectType(this.Context),
                staffDyo,
                false);

            var staffDeptRef = staffDyo["fdeptid_ref"] as DynamicObject;
            if (staffDeptRef != null)
            {

                copyObj["fdeptid_ref"] = staffDeptRef;
            }

            //填充进度
            (copyObj["fndentry"] as DynamicObjectCollection).Clear();
            var billNodeDefineService = this.Container.GetService<IBillNodeDefineService>();
            billNodeDefineService.Init(this.Context, custrMeta, null);
            billNodeDefineService.FillBillNodeDefineInfos(new[] { copyObj });

            //加载引用数据，如：基础资料，单据类型，辅助资料 等等
            //this.Container.GetService<LoadReferenceObjectManager>()?.Load(
            //    this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), copyObj, false
            //    );
            #endregion

            #region 打包成前端的数据结构
            var uiDataConvert = this.Container.GetService<IUiDataConverter>();
            var billJsonData = uiDataConvert.CreateUIDataObject(this.Context, this.HtmlForm, copyObj);

            var action = this.Context.ShowSpecialForm(this.HtmlForm,
                        copyObj,
                        false,
                        Guid.NewGuid().ToString(),
                        Enu_OpenStyle.Default,
                        Enu_DomainType.Bill,
                        new Dictionary<string, object>
                {
                    { "sourceId", "" },
                    { "sourceFormId", this.HtmlForm.Id },
                    { "sourcePageId", this.CurrentPageId }
                },
                (formPara) =>
                {
                    formPara.FormCaption = this.HtmlForm.Caption + "--复制新增";
                    formPara.Status = Enu_BillStatus.New;
                    formPara.UiData = billJsonData.GetJsonValue("uiData", new JObject());
                    //源单类型
                    JObject joSourceType = new JObject();
                    joSourceType["id"] = this.HtmlForm.Id;
                    joSourceType["fnumber"] = this.HtmlForm.Id;
                    joSourceType["fname"] = this.HtmlForm.Caption;
                    formPara.UiData["fsourcetype"] = joSourceType;
                },
                GetQueryOrSimpleParam<string>("containerId"));
            #endregion

            this.Result.HtmlActions.Add(action);
            this.Result.IsSuccess = true;
        }
    }
}