using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Core.Interface.StockReserve;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE
{
   
    
    
    
    public   class ReserveUtil
    {



        /// <summary>
        /// 销售合同、销售意向单的预留更新
        /// </summary>
        public static  IOperationResult UpdateReserve(UserContext ctx, HtmlForm billForm, DynamicObject[] dataEntities, OperateOption option)
        {
            IOperationResult result = new OperationResult();
            result.IsSuccess = true;
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return result;
            }

            var profileService = ctx.Container.GetService<ISystemProfile>();
            var autoReserve = profileService.GetSystemParameter(ctx, "bas_storesysparam", "foutspotautores", false);

            var beUpdate = new List<DynamicObject>();
            var beAutoSet = new List<DynamicObject>();
            var reserveService = ctx.Container.GetService<IReserveUpdateService>();
            var reserveBillIDS = reserveService.GetReserveBillIDS(ctx, billForm.Id, dataEntities);
            foreach (var item in dataEntities)
            {
                var id = item["Id"].ToString();
                if (reserveBillIDS.ContainsKey(id))
                {
                    //存在预留单，则自动更新预留
                    beUpdate.Add(item);
                }
                else if (autoReserve)
                {
                    //不存在预留单，如果参数设置为【出现货时自动预留】，则出现货的行要自动预留
                    beAutoSet.Add(item);
                }
            }

            option.SetVariableValue("updateReserve", true);
             
            if (beUpdate.Count > 0)
            {
                result = reserveService.SetOrUpdateReserve(ctx,  billForm , beUpdate, option);                
            }

            if (beAutoSet.Count > 0)
            {
                //需要做自动预留的需求明细行：出现货的商品行
                var enKey = "fentry";
                if(billForm.Id.EqualsIgnoreCase ("ydj_saleintention"))
                {
                    enKey = "fentity";
                }
                var fentries = beAutoSet.SelectMany(x => x[enKey] as DynamicObjectCollection)?.Where(x => Convert.ToBoolean(x["fisoutspot"]))?.ToList();
                if (fentries != null && fentries.Count > 0)
                {
                    option.SetVariableValue("autoReserve", true);//自动预留标记
                    var selectEntryRow = fentries.Select(f => f["Id"].ToString()).ToList();
                    option.SetVariableValue("selectEntryRow", selectEntryRow);
                    var resultx = reserveService.SetOrUpdateReserve(ctx,  billForm, beAutoSet, option);
                    result.MergeResult(resultx);
                }
            }

            var xx = new List<string>(result.ComplexMessage.WarningMessages).Distinct().ToList();
            result.ComplexMessage.WarningMessages.Clear();
            result.ComplexMessage.WarningMessages.AddRange(xx);

            xx = new List<string>(result.ComplexMessage.ErrorMessages).Distinct().ToList();
            result.ComplexMessage.ErrorMessages.Clear();
            result.ComplexMessage.ErrorMessages.AddRange(xx);
             
            result.ThrowIfHasError(true, "预留更新出现意外错误！");

            return result;
        }




        /// <summary>
        /// 更新销售合同的预留日期
        /// </summary>
        public static void  UpdateReserveDate(UserContext ctx, HtmlForm billForm, DynamicObject[] dataEntities, OperateOption option)
        { 
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return ;
            }
             
            var beUpdate = new List<DynamicObject>(); 
            var reserveService = ctx.Container.GetService<IReserveUpdateService>();
            var reserveBillIDS = reserveService.GetReserveBillIDS(ctx, billForm.Id, dataEntities);
            foreach (var item in dataEntities)
            {
                var id = item["Id"].ToString();
                if (reserveBillIDS.ContainsKey(id))
                {
                    //存在预留单，则自动更新预留
                    beUpdate.Add(item);
                } 
            }
             
            if (beUpdate.Count > 0)
            {
                reserveService.SetOrUpdateReserve(ctx, billForm, beUpdate, option);
            } 
        }


    }
}
