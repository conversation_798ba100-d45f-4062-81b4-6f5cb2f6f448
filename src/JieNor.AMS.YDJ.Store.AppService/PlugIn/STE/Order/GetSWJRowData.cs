using System.Linq;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Model;
using System.Collections.Generic;
using JieNor.Framework;
using System;
using JieNor.Framework.MetaCore.FormModel.Office;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Store.AppService.Model.Unboxing;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BCM.BarcodeMaster;
using JieNor.Framework.DataTransferObject.Print;
using JieNor.Framework.SuperOrm;
using System.Data;
using JieNor.AMS.YDJ.MS.API.DTO;
using JieNor.AMS.YDJ.MS.API.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// V6定制柜，三维家单据获取默认新增行数据
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getswjrowdata")]
    public class GetSWJRowData : AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);


            string productid = "";
            var materialObjs = this.Context.ExecuteDynamicObject("select top 1  fid,funitid,fpurunitid,fseriesid,fbrandid,fcategoryid from T_BD_MATERIAL with(nolock) where fnumber=@fnumber order by fmainorgid", new List<SqlParam> { new SqlParam("fnumber", DbType.String, "VFZ1-M001") });
            if (materialObjs != null && materialObjs.Count > 0)
            {
                productid = Convert.ToString(materialObjs[0]["fid"]);
            }
            else
            {
                this.Result.IsSuccess = false;
                return;
            }
            string attrinfo = "";
            string attrName = "";
            //辅助属性
            List<AuxPropValDTO> newPropList = new List<AuxPropValDTO>();
            var sel_cats = this.Context.LoadBizDataByNo("sel_category", "fnumber", new List<string>() { "VFZ1_M001" }).FirstOrDefault();
            if (sel_cats != null)
            {
                var selentrys = sel_cats["fentity"] as DynamicObjectCollection;
                foreach (var sel_propobj in selentrys.Where(a => Convert.ToInt32(a["fiswhetherinforce"]) == 1))
                {
                    var propItem = this.Context.LoadBizDataById("sel_prop", Convert.ToString(sel_propobj["fpropid"]));
                    var propValueItem = this.Context.LoadBizDataById("sel_propvalue", Convert.ToString(sel_propobj["fdefaultpropvalueid"]));
                    newPropList.Add(new AuxPropValDTO
                    {
                        PropNo = Convert.ToString(propItem["fnumber"]),
                        PropValueNo = Convert.ToString(propValueItem["fname"]),
                        PropValueName = Convert.ToString(propValueItem["fname"])
                    });
                    attrName = propItem["fname"].ToString();
                }
            }
            var auxObj = ProductUtil.ConvertAuxPropFieldValue(this.Context, productid, newPropList);
            if (auxObj != null)
            {
                List<object> attrs = new List<object>();
                var entrys = auxObj["fentity"] as DynamicObjectCollection;
                foreach (var item in entrys)
                {
                    var attrObj = new
                    {
                        fauxpropid = new
                        {
                            id = item["fauxpropid"],
                            fname = attrName,
                            fnumber = newPropList[0].PropNo
                        },
                        fvalueid = item["fvalueid"],
                        fvaluename = item["fvaluename"],
                        fvaluenumber = item["fvaluenumber"]
                    };
                    attrs.Add(attrObj);
                };
                attrinfo = Newtonsoft.Json.JsonConvert.SerializeObject(attrs);
            }



            var obj = new
            {
                fproductid = productid,
                fattrinfo = attrinfo,
                fbizqty = 1,
                fprice = 999,
                fdistrateraw = 10,
                funstdtype=1
            };
            this.Result.IsSuccess = true;
            this.Result.SrvData = obj;

        }
    }
}
