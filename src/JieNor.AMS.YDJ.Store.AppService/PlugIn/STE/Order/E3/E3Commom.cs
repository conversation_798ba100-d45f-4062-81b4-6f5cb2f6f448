using JieNor.AMS.YDJ.Store.AppService.MuSi;
using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs.MerChant;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AppStore;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Utils;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.E3
{
    public class E3Commom
    {
        /// <summary>
        /// 模型服务
        /// </summary>
        [InjectProperty]
        protected static IMetaModelService MetaModelService { get; set; }
        protected static IDBService dBService { get; set; }
        protected static UserContext userCtx { get; set; }
        protected static StringBuilder sb { get; set; }
        private static readonly string e3htmlId = "ydj_e3logisticsprogress";
        public E3Commom(UserContext ctx)
        {
            dBService = ctx.Container.GetService<IDBService>();
            userCtx = ctx;
            MetaModelService = ctx.Container.GetService<IMetaModelService>();
        }

        /// <summary>
        /// 获取E3进度并存到表里
        /// </summary>
        /// <returns></returns>
        public IOperationResult GetE3LogisticsProcess(List<string> _data = null)
        {
            sb = new StringBuilder();
            List<string> successNo = new List<string>();
            List<string> failNo = new List<string>();
            List<string> failMes = new List<string>();
            var result = userCtx.Container.GetService<IOperationResult>();
            var orders = GetPendingPurchaseOrders(_data);
            List<DynamicObject> objs = new List<DynamicObject>();
            var e3Obj = MuSiApi.GetE3ExtApp(userCtx);
            if (e3Obj == null)
            {
                result.IsSuccess = false;
                failMes.Add("获取E3应用信息失败！");
                result.SimpleMessage = "获取E3应用信息失败！";
                return result;
            }

            string appKey = Convert.ToString(e3Obj["fappkey"]);
            var e3Html = MetaModelService.LoadFormModel(userCtx, e3htmlId);
            if (appKey.IsNullOrEmptyOrWhiteSpace())
            {
                result.IsSuccess = false;
                failMes.Add("外部应用匹配不正确！");
                result.SimpleMessage = "外部应用匹配不正确！";
                return result;
            }
            if (orders == null || orders.Count == 0)
            {
                result.IsSuccess = false;
                result.SimpleMessage = "未查询到需要符合条件的数据！";
                failMes.Add("未查询到需要符合条件的数据！");
                return result;
            }
            foreach (var dataItem in orders)
            {
                MSE3LogisticsRequestDTO e3Request = new MSE3LogisticsRequestDTO();
                e3Request.khck = Convert.ToString(dataItem["pobillno"]);
                if (Convert.ToInt32(dataItem["fseq_e"]) >= 10)
                {
                    e3Request.dealCodeLineSn = "000" + Convert.ToInt32(dataItem["fseq_e"]);
                }
                else
                {
                    e3Request.dealCodeLineSn = "0000" + Convert.ToInt32(dataItem["fseq_e"]) * 10;
                }
                //e3Request.dealCodeLineSn = Convert.ToString(dataItem["fseq_e"]);

                JObject configObj = JObject.Parse(appKey);
                string key = configObj.GetJsonValue("key", "");
                string requestTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string secret = configObj.GetJsonValue("secret", "");
                string version = configObj.GetJsonValue("version", "");
                string serviceType = configObj.GetJsonValue("serviceType", "");
                string data = e3Request.ToJson();
                //data = "{\"express_sn\":\"2103032029547718\",\"dealCode\":\"\",\"khck\":\"\"}";

                var sign = SecurityUtil.HashString($@"key={key}&requestTime={requestTime}&secret={secret}&version={version}&serviceType={serviceType}&data={data}");
                MSE3LogisticsDTO dto = new MSE3LogisticsDTO();
                dto.sign = sign;
                dto.key = key;
                dto.requestTime = requestTime;
                dto.version = version;
                dto.serviceType = serviceType;
                dto.data = data;

                var e3result = MuSiApi.GetE3LogisticsProgress(userCtx, e3Html, dto);
                //step1:将获取的数据存到中间表
                if (e3result != null)
                {
                    //JObject _obj = result.data;
                    //if (_obj == null)
                    //{

                    //}
                    //JArray array = _obj["data"] as JArray;
                    JArray array = e3result.data as JArray;
                    foreach (var item in array)
                    {
                        // 获取物流单号
                        string expressSn = Convert.ToString(item["express_sn"]);

                        // 获取送货日期
                        string deliveryDate = "";
                        string signDate = "";
                        var trackList = item["track_list"] as JArray;
                        if (trackList != null)
                        {
                            var signNode = trackList.FirstOrDefault(t => Convert.ToString(t["shipping_status"]) == "3");
                            if (signNode != null)
                            {
                                signDate = Convert.ToString(signNode["shipping_time"]);
                            }
                            var deliveryNode = trackList.FirstOrDefault(t => Convert.ToString(t["shipping_status"]) == "2");
                            if (deliveryNode != null)
                            {
                                deliveryDate = Convert.ToString(deliveryNode["shipping_time"]);
                            }
                        }

                        // 遍历 items_list 匹配 dataItem
                        foreach (var subItem in item["items_list"])
                        {
                            string itemCode = Convert.ToString(subItem["itemCode"]);
                            string dealCodeLineSn = Convert.ToString(subItem["dealCodeLineSn"]);
                            string itemQuantity = Convert.ToString(subItem["itemQuantity"]);

                            // 匹配 dataItem 的商品和行编码
                            if (Convert.ToString(dataItem["fnumber"]) == itemCode &&
                                Convert.ToInt32(dataItem["fseq_e"]) * 10 == Convert.ToInt32(dealCodeLineSn))
                            {
                                // 设置 obj 的值
                                DynamicObject obj = new DynamicObject(e3Html.GetDynamicObjectType(userCtx));
                                obj["fjson"] = e3result.ToJson();
                                obj["forderid"] = dataItem["fid"]; // 合同ID
                                obj["fsourcetype"] = "ydj_order"; // 合同
                                obj["fsourcenumber"] = dataItem["fbillno"]; // 合同编号
                                obj["fentryid"] = dataItem["fentryid"]; // 合同明细ID
                                obj["fmaterialid"] = dataItem["fproductid"]; // 商品ID
                                obj["fattrinfo"] = dataItem["fattrinfo"]; // 辅助属性
                                obj["fattrinfo_e"] = dataItem["fattrinfo_e"]; // 辅助属性
                                obj["fcustomdes_e"] = dataItem["fcustomdes_e"]; // 定制说明
                                obj["fpoid"] = dataItem["poId"]; // 采购订单ID
                                obj["fpobillno"] = dataItem["pobillno"]; // 采购订单号
                                obj["fhqderno"] = dataItem["fhqderno"]; // 采购订单号
                                obj["fposeq"] = dataItem["fseq_e"]; // 采购订单行号
                                obj["flogisticsno"] = expressSn; // 物流送货单号
                                if (!string.IsNullOrWhiteSpace(deliveryDate))
                                {
                                    obj["fdeliverydate"] = deliveryDate; // 送货日期
                                }
                                obj["fbizqty"] = 0; // 签收数量
                                // 判断行签收状态
                                if (Convert.ToDecimal(dataItem["pobizqty"]) == Convert.ToDecimal(itemQuantity))
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                                }
                                else if (Convert.ToDecimal(dataItem["pobizqty"]) > Convert.ToDecimal(itemQuantity) && Convert.ToDecimal(itemQuantity) > 0)
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                                }
                                else if (Convert.ToDecimal(itemQuantity) == 0)
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                                }
                                if (!string.IsNullOrWhiteSpace(signDate))
                                {
                                    obj["fbizqty"] = itemQuantity; // 签收数量
                                    obj["fsigndate"] = signDate; // 签收日期
                                }
                                obj["fmodifydate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"); // 数据更新时间
                                objs.Add(obj);
                            }
                        }
                    }

                }
                else
                {
                    failNo.Add(e3Request.khck);
                    failMes.Add($"获取E3物流进度失败，采购订单号：{e3Request.khck}，采购订单行号：{Convert.ToInt32(dataItem["fseq_e"])}，请检查E3系统！");
                }
            }
            // 1. 按fentryid分组，汇总签收日期不为空的签收数量
            var entrySignSummary = objs
                .GroupBy(o => Convert.ToString(o["fentryid"]))
                .Select(g => new
                {
                    fentryid = g.Key,
                    TotalSignQty = g.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["fsigndate"]))).Sum(x => Convert.ToDecimal(x["fbizqty"]))
                })
                .ToDictionary(x => x.fentryid, x => x.TotalSignQty);


            foreach (var _dataItem in entrySignSummary)
            {
                var objItem = objs.Where(o => Convert.ToString(o["fentryid"]) == _dataItem.Key).FirstOrDefault();
                var orderItem = orders.Where(o => Convert.ToString(o["fentryid"]) == _dataItem.Key).FirstOrDefault();
                if (objItem != null && orderItem != null)
                {
                    decimal fbizqty = Convert.ToDecimal(orderItem["pobizqty"]);
                    decimal TotalSignQty = Convert.ToDecimal(_dataItem.Value);
                    if (TotalSignQty == 0)
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                    }
                    else if (TotalSignQty >= fbizqty)
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                    }
                    else
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                    }
                }
            }
            if (objs.Any())
            {
                var topCtx = userCtx.CreateTopOrgDBContext();
                var orderIds = objs.Select(a => Convert.ToString(a["forderid"])).Distinct().ToList();
                var logicData = topCtx.LoadBizDataByFilter(e3htmlId, $" forderid in ('{string.Join("','", orderIds)}')");
                var deleteResult = topCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(topCtx, e3htmlId, logicData, "delete", null);
                deleteResult.ThrowIfHasError(true, "E3物流进度查询中间表删除失败！");
                var billnos = objs.Select(a => Convert.ToString(a["fpobillno"])).ToList();
                var auditResult = topCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(topCtx, e3htmlId, objs, "save", null);
                if (auditResult.IsSuccess)
                {
                    successNo.AddRange(billnos);
                }
                else failNo.AddRange(billnos);
            }
            else
            {
                failNo.AddRange(failNo);
            }
            result.IsSuccess = true;
            result.SrvData = new { successNo = successNo, failNo = failNo, failMes = failMes };
            return result;
        }

        /// <summary>
        /// 更新销售合同物流进度
        /// </summary>
        /// <returns></returns>
        public IOperationResult UpdateOrderLogisticsProgress(List<string> _data = null)
        {
            sb = new StringBuilder();
            List<string> successNo = new List<string>();
            List<string> failNo = new List<string>();
            List<string> failMes = new List<string>();
            var result = userCtx.Container.GetService<IOperationResult>();

            string tmpName = dBService.CreateTemporaryTableName(userCtx, true);
            GetPendingPurchaseOrders(tmpName, _data);
            string sql = $@"select a.*,b.pobizqty,b.fentryid as fordentryid from t_ydj_e3logisticsprogress a  
                                inner join {tmpName} b on a.fposeq=b.fseq_e and a.fpoid=b.poId ";
            var data = dBService.ExecuteDynamicObject(userCtx, sql);
            if (data == null || data.Count == 0)
            {
                result.IsSuccess = false;
                failMes.Add("没有符合条件的销售合同物流进度数据！");
                return result;
            }
            var groupedData = data
               .GroupBy(d => new { forderid = d["forderid"], fbillno = d["fsourcenumber"], fentryid = d["fordentryid"] })
               .Select(g => new
               {
                   forderid = g.Key.forderid,
                   fbillno = g.Key.fbillno,
                   fentryid = g.Key.fentryid,
                   TotalPobizqty = g.Sum(x => Convert.ToDecimal(x["pobizqty"])),//采购订单数量
                   TotalFbizqty = g.Sum(x => Convert.ToDecimal(x["fbizqty"]))//签收数量
               })
               .ToList();
            sb.AppendLine($"共查询到{data.Count}条销售合同物流进度数据，分组后共{groupedData.Count}条。");
            sb.AppendLine($"开始更新销售合同签收状态...");
            sb.AppendLine($"待更新数据：" + groupedData.Select(g => $"{g.fbillno} - {g.forderid} - {g.fentryid} - 采购数量: {g.TotalPobizqty}, 签收数量: {g.TotalFbizqty}").ToJson());
            List<DynamicObject> needSaveObjs = new List<DynamicObject>();

            List<string> orderIds = data.Select(f => f["forderid"].ToString()).Distinct().ToList();
            List<DynamicObject> orders = userCtx.LoadBizDataById("ydj_order", orderIds);
            foreach (var item in groupedData)
            {
                var orderItem = orders.Where(a => Convert.ToString(a["id"]).Equals(item.forderid)).FirstOrDefault();
                if (orderItem == null) continue;
                var orderEntry = orderItem["fentry"] as DynamicObjectCollection;
                var orderEntryItem = orderEntry.Where(a => Convert.ToString(a["id"]).Equals(item.fentryid)).FirstOrDefault();
                if (orderEntryItem == null) continue;
                // 判断行签收状态
                if (item.TotalPobizqty == item.TotalFbizqty)
                {
                    orderEntryItem["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                }
                else if (item.TotalPobizqty > item.TotalFbizqty && item.TotalFbizqty > 0)
                {
                    orderEntryItem["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                }
                else
                {
                    orderEntryItem["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                }
                if (needSaveObjs.Where(a => Convert.ToString(a["id"]).Equals(orderItem["id"])).FirstOrDefault() != null)
                {
                    needSaveObjs.Remove(needSaveObjs.Where(a => Convert.ToString(a["id"]).Equals(orderItem["id"])).FirstOrDefault());
                }
                needSaveObjs.Add(orderItem);
            }
            foreach (var order in needSaveObjs)
            {
                var orderEentry = order["fentry"] as DynamicObjectCollection;
                if (orderEentry == null || orderEentry.Count == 0) continue;
                var orderEntries = orderEentry.Where(a => Convert.ToString(a["fdeliverytype"]).Equals("delivery_type_01")).ToList();
                if (orderEntries == null || orderEntries.Count == 0) continue;

                // 判断明细签收状态
                bool allSigned = orderEntries.All(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_01");
                bool hasPartialSign = orderEntries.Any(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_02" || Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_01");
                bool allNotSigned = orderEntries.All(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_03");

                // 处理套件头签收状态
                var suit = orderEntries.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["fsuitcombnumber"]))).ToList();
                foreach (var suitItem in suit)
                {
                    var suitNo = Convert.ToString(suitItem["fsuitcombnumber"]);
                    // 找到同套件号的子件，且fsubqty != 0
                    var subItems = orderEntries.Where(a => Convert.ToString(a["fsuitcombnumber"]) == suitNo && Convert.ToDecimal(a["fsubqty"]) != 0).ToList();
                    if (subItems.Count == 0) continue;

                    // 统计签收状态
                    bool _allNotSigned = subItems.All(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_03");
                    bool _allSigned = subItems.All(a => Convert.ToString(a["fentrysignstatus"]) == "entrysign_type_01");

                    var head = orderEntries.Where(a => Convert.ToString(a["fsuitcombnumber"]) == suitNo && Convert.ToDecimal(a["fsubqty"]) == 0).FirstOrDefault();
                    if (_allNotSigned)
                    {
                        head["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                    }
                    else if (_allSigned)
                    {
                        head["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                    }
                    else
                    {
                        head["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                    }
                }

                // 更新表头签收状态
                if (allSigned)
                {
                    order["fordersignstatus"] = "ordersign_type_01"; // 已签收
                }
                else if (hasPartialSign)
                {
                    order["fordersignstatus"] = "ordersign_type_02"; // 部分签收
                }
                else if (allNotSigned)
                {
                    order["fordersignstatus"] = "ordersign_type_03"; // 未签收
                }
                else
                {
                    order["fordersignstatus"] = "ordersign_type_03"; // 未签收
                }
            }
            var grpOrgDatas = needSaveObjs.GroupBy(f => f["fmainorgid"].ToString()).ToList();
            var orderHtml = MetaModelService.LoadFormModel(userCtx, "ydj_order");
            foreach (var item in grpOrgDatas)
            {
                var ctx = userCtx.CreateAgentDBContext(item.Key);
                var objs = needSaveObjs.Where(a => Convert.ToString(a["fmainorgid"]).Equals(item.Key)).ToList();
                var dm = ctx.Container.GetService<IDataManager>();
                dm.InitDbContext(ctx, orderHtml.GetDynamicObjectType(ctx));
                dm.Save(objs);
                //var saveResult = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, "ydj_order", objs, "save", null);

                var billnos = objs.Select(o => Convert.ToString(o["fbillno"])).ToList();
                //if (saveResult.IsSuccess)
                //{
                successNo.AddRange(billnos);
                //}
                //else
                //{
                //    failNo.AddRange(billnos);
                //}
            }
            sb.AppendLine($"更新销售合同签收状态完成，共{needSaveObjs.Count}条数据。");
            sb.AppendLine($"成功更新销售合同编号：" + string.Join(",", successNo));
            result.IsSuccess = true;
            result.SrvData = new { successNo = successNo, failNo = failNo, message = new List<string>() { sb.ToString() } };
            return result;
        }

        /// <summary>
        /// 一件代发销售出库单自动创建并审核
        /// </summary>
        /// <returns></returns>
        public IOperationResult AutoCreateSoStockOut(List<string> _data = null)
        {
            sb = new StringBuilder();
            var result = userCtx.Container.GetService<IOperationResult>();
            //根据采购入库单找源头的销售合同，通过销售合同下推出库单
            // 获取符合条件的销售合同
            var order = GetPendingOrders(_data);

            if (order == null || !order.Any())
            {
                result.IsSuccess = false;
                result.SimpleMessage = "没有符合条件的采购入库单需要处理。";
                return result;
            }
            result = CheckDefaultData();
            if (!result.IsSuccess)
            {
                return result;
            }

            List<string> successPoStockInIds = new List<string>();
            List<string> billnos = new List<string>();
            List<string> successNo = new List<string>();
            List<string> failNo = new List<string>();

            var grpOrgDatas = order.GroupBy(f => f["fmainorgid"].ToString()).ToList();
            foreach (var item in grpOrgDatas)
            {
                var ctx = userCtx.CreateAgentDBContext(item.Key);
                var defaultStore = ctx.LoadBizDataByNo("ydj_storehouse", "fnumber", new List<string>() { "YJDFZBCK" });
                if (defaultStore == null || !defaultStore.Any())
                {
                    sb.Append($"经销商【{ctx.Company}】未查询到预设的仓库【YJDFZBCK】。");
                    continue;
                }
                var ordids = item.ToList().Select(f => f["fid"].ToString()).Distinct().ToList();
                var ordList = ctx.LoadBizDataById("ydj_order", ordids);
                var _ord = order.Where(a => ordList.Where(c => Convert.ToString(c["id"]).Equals(Convert.ToString(a["fid"]))).FirstOrDefault() != null).ToList();
                foreach (var ordItem in ordList)
                {
                    var entry = ordItem["fentry"] as DynamicObjectCollection;
                    foreach (var entryItem in entry)
                    {
                        var _orderItem = order.Where(a => Convert.ToString(a["fentryid"]).Equals(Convert.ToString(entryItem["id"]))).FirstOrDefault();
                        if (_orderItem != null)
                        {
                            entryItem["fqty"] = _orderItem["bizqty"];
                            entryItem["fbizqty"] = _orderItem["bizqty"];
                        }
                    }
                }
                foreach (var ordItem in ordList)
                {
                    string billno = Convert.ToString(ordItem["fbillno"]);
                    var entryids = order.Where(a => Convert.ToString(a["fid"]).Equals(Convert.ToString(ordItem["id"]))).Select(a => Convert.ToString(a["fentryid"])).ToList();
                    List<Row> rows = new List<Row>();
                    var entry = ordItem["fentry"] as DynamicObjectCollection;
                    entry.Where(a => entryids.IndexOf(Convert.ToString(a["id"])) >= 0).ForEach(a => rows.Add(new Row() { Id = Convert.ToString(a["id"]) }));
                    //rows.Add(new )
                    if (rows.Count == 0)
                    {
                        failNo.Add(billno);
                        continue;
                    }
                    var dic = new Dictionary<string, object>();
                    dic.Add("rowIds", JsonConvert.SerializeObject(rows));
                    IOperationResult pushResult = null;
                    try
                    {
                        pushResult = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, "ydj_order", new List<DynamicObject> { ordItem }, "piecesendorderpushsostock", dic);
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine($"销售合同{billno} 下推出库单失败；下推结果：" + JsonConvert.SerializeObject(pushResult?.ComplexMessage.ErrorMessages) + "；异常描述：" + ex.Message);
                        failNo.Add(billno);
                    }
                    finally
                    {
                        if (pushResult != null)
                        {
                            if (pushResult.IsSuccess)
                            {
                                successNo.Add(billno);
                                //通过成功的合同，找到下游采购入库单，更新入库单是否已下推标记
                                var _successOrder = order.Where(a => Convert.ToString(a["fbillno"]).Equals(billno));
                                successPoStockInIds.AddRange(_successOrder.Select(a => Convert.ToString(a["postockInId"])).ToList());
                                sb.AppendLine($"销售合同{billno} 下推出库单成功；下推结果：" + JsonConvert.SerializeObject(pushResult?.ComplexMessage.SuccessMessages));
                            }
                            else
                            {
                                sb.AppendLine($"销售合同{billno} 下推出库单失败；下推结果：" + JsonConvert.SerializeObject(pushResult?.ComplexMessage.ErrorMessages));
                                failNo.Add(billno);
                            }
                        }
                        else
                        {
                            sb.AppendLine($"销售合同{billno} 下推出库单失败；下推结果：" + JsonConvert.SerializeObject(pushResult));
                            failNo.Add(billno);
                        }
                    }

                }
            }

            if (successPoStockInIds.Count > 0)
            {
                sb.AppendLine($"共{successPoStockInIds.Count}条采购入库单需要更新已下推出库标识。");
                var postockObj = userCtx.LoadBizDataById("stk_postockin", successPoStockInIds);
                postockObj.ForEach(a => a["fisoutstock"] = "1");
                var draftResult = userCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(userCtx, "stk_postockin", postockObj, "draft", null);
                if (draftResult.IsSuccess)
                {
                    billnos = postockObj.Select(o => Convert.ToString(o["fbillno"])).ToList();
                }
                else
                {
                    sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 暂存失败，更新采购入库已下推出库标识失败；" + JsonConvert.SerializeObject(draftResult.ComplexMessage.ErrorMessages));
                    billnos = postockObj.Select(o => Convert.ToString(o["fbillno"])).ToList();
                }
            }
            result.SimpleMessage = sb.ToString();
            result.IsSuccess = true;
            result.SrvData = new { successNo = successNo.Distinct().ToList(), failNo = failNo.Distinct().ToList(), message = new List<string>() { sb.ToString() } };
            return result;
        }


        /// <summary>
        /// 一件代发销售出库单自动创建并审核
        /// </summary>
        /// <returns></returns>
        public IOperationResult AutoAuditPoStockIn(List<string> _data = null)
        {
            sb = new StringBuilder();
            var result = userCtx.Container.GetService<IOperationResult>();

            // 获取符合条件的采购入库单
            var purchaseOrders = GetPendingPurchaseOrdersAsync(_data);

            if (purchaseOrders == null || !purchaseOrders.Any())
            {
                result.IsSuccess = false;
                sb.AppendLine("没有符合条件的采购入库单需要处理。");
                //result.SimpleMessage = "没有符合条件的采购入库单需要处理。";
                return result;
            }
            var defaultStaff = userCtx.LoadBizDataByNo("ydj_staff", "fnumber", new List<string>() { "YJDFXNYG" });
            if (defaultStaff == null || !defaultStaff.Any())
            {
                result.IsSuccess = false;
                sb.AppendLine("未在总部查询到预设的员工【YJDFXNYG】。");
                //result.SimpleMessage = "未在总部查询到预设的员工【YJDFXNYG】。";
                return result;
            }
            var defaultDept = userCtx.LoadBizDataByNo("ydj_dept", "fnumber", new List<string>() { "YJDFXNBM" });
            if (defaultDept == null || !defaultDept.Any())
            {
                result.IsSuccess = false;
                sb.AppendLine("未在总部查询到预设的部门【YJDFXNBM】。");
                //result.SimpleMessage = "未在总部查询到预设的部门【YJDFXNBM】。";
                return result;
            }
            Dictionary<string, object> keys = new Dictionary<string, object>();
            keys.Add("e3Auto", "true");
            List<string> successNo = new List<string>();
            List<string> failNo = new List<string>();

            List<string> billnos = new List<string>();
            var grpOrgDatas = purchaseOrders.GroupBy(f => f["fmainorgid"].ToString()).ToList();
            foreach (var item in grpOrgDatas)
            {
                var ctx = userCtx.CreateAgentDBContext(item.Key);
                var defaultStore = ctx.LoadBizDataByNo("ydj_storehouse", "fnumber", new List<string>() { "YJDFZBCK" });
                if (defaultStore == null || !defaultStore.Any())
                {
                    sb.AppendLine($"经销商【{ctx.Company}】未查询到预设的仓库【YJDFZBCK】。");
                    continue;
                }
                var purids = item.ToList().Select(f => f["fid"].ToString()).Distinct().ToList();
                var purOrderList = userCtx.LoadBizDataById("stk_postockin", purids);
                purOrderList.ForEach(a =>
                {
                    a["fstockstaffid"] = defaultStaff.FirstOrDefault()["id"];
                    a["fstockdeptid"] = defaultDept.FirstOrDefault()["id"];
                    var entry = a["fentity"] as DynamicObjectCollection;
                    entry.ForEach(e =>
                    {
                        e["fstorehouseid"] = defaultStore.FirstOrDefault()["id"];
                        e["fstockstatus"] = "311858936800219137";
                    });
                }
                );
                //创建和重新审核的数据，做提交
                var needSubmitDatas = purOrderList.Where(a => Convert.ToString(a["fstatus"]).ToUpper().Equals("C") || Convert.ToString(a["fstatus"]).ToUpper().Equals("B")).ToList();
                if (needSubmitDatas.Any())
                {
                    billnos = needSubmitDatas.Select(o => Convert.ToString(o["fbillno"])).ToList();
                    IOperationResult submitResult = null;
                    try
                    {
                        submitResult = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, "stk_postockin", needSubmitDatas, "submit", keys);
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 提交失败；" + JsonConvert.SerializeObject(submitResult?.ComplexMessage.ErrorMessages) + "；异常描述：" + ex.Message);
                        failNo.AddRange(billnos);
                    }
                    finally
                    {
                        if (submitResult != null)
                        {
                            if (submitResult.IsSuccess)
                            {
                                successNo.AddRange(billnos);
                                sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 提交成功。");
                            }
                            else
                            {
                                failNo.AddRange(billnos);
                                sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 提交失败；" + JsonConvert.SerializeObject(submitResult.ComplexMessage.ErrorMessages));
                            }
                        }
                        else
                        {
                            sb.AppendLine($" 采购入库单{billnos.JoinEx(",", false)} 提交失败；" + JsonConvert.SerializeObject(submitResult));
                            failNo.AddRange(billnos);
                        }
                    }
                }
                //提交的数据做审核
                var needAuditDatas = purOrderList.Where(a => Convert.ToString(a["fstatus"]).ToUpper().Equals("D")).ToList();
                if (needAuditDatas.Any())
                {
                    IOperationResult auditResult = null;
                    billnos = needAuditDatas.Select(o => Convert.ToString(o["fbillno"])).ToList();
                    try
                    {
                        auditResult = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, "stk_postockin", needAuditDatas, "audit", keys);
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 审核成功；" + JsonConvert.SerializeObject(auditResult?.ComplexMessage.ErrorMessages) + "；异常描述：" + ex.Message);
                        failNo.AddRange(billnos);
                    }
                    finally
                    {
                        if (auditResult != null)
                        {
                            if (auditResult.IsSuccess)
                            {
                                successNo.AddRange(billnos);
                                sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 审核成功。");
                            }
                            else
                            {
                                failNo.AddRange(billnos);
                                sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 审核成功；" + JsonConvert.SerializeObject(auditResult.ComplexMessage.ErrorMessages));
                            }
                        }
                        else
                        {
                            sb.AppendLine($" 采购入库单{billnos.JoinEx(",", false)} 审核成功；" + JsonConvert.SerializeObject(auditResult));
                            failNo.AddRange(billnos);
                        }
                    }
                }
            }

            result.SimpleMessage = sb.ToString();
            result.IsSuccess = true;
            result.SrvData = new { successNo = successNo, failNo = failNo, message = new List<string>() { sb.ToString() } };
            return result;
        }




        /// <summary>
        /// 【直营】一件代发销售出库单自动创建并审核
        /// </summary>
        /// <returns></returns>
        public IOperationResult AutoAuditZYPoStockIn(List<string> _data = null)
        {
            sb = new StringBuilder();
            var result = userCtx.Container.GetService<IOperationResult>();

            // 获取符合条件的采购入库单
            var purchaseOrders = GetZYPendingPurchaseOrdersAsync(_data);

            if (purchaseOrders == null || !purchaseOrders.Any())
            {
                result.IsSuccess = false;
                sb.AppendLine("没有符合条件的采购入库单需要处理。");
                //result.SimpleMessage = "没有符合条件的采购入库单需要处理。";
                return result;
            }
            //var defaultStaff = userCtx.LoadBizDataByNo("ydj_staff", "fnumber", new List<string>() { "YJDFXNYG" });
            //if (defaultStaff == null || !defaultStaff.Any())
            //{
            //    result.IsSuccess = false;
            //    sb.AppendLine("未在总部查询到预设的员工【YJDFXNYG】。");
            //    //result.SimpleMessage = "未在总部查询到预设的员工【YJDFXNYG】。";
            //    return result;
            //}
            //var defaultDept = userCtx.LoadBizDataByNo("ydj_dept", "fnumber", new List<string>() { "YJDFXNBM" });
            //if (defaultDept == null || !defaultDept.Any())
            //{
            //    result.IsSuccess = false;
            //    sb.AppendLine("未在总部查询到预设的部门【YJDFXNBM】。");
            //    //result.SimpleMessage = "未在总部查询到预设的部门【YJDFXNBM】。";
            //    return result;
            //}
            Dictionary<string, object> keys = new Dictionary<string, object>();
            keys.Add("e3Auto", "true");
            keys.Add("IgnoreCheckPermssion", "true");
            List<string> successNo = new List<string>();
            List<string> failNo = new List<string>();

            List<string> billnos = new List<string>();
            var grpOrgDatas = purchaseOrders.GroupBy(f => f["fmainorgid"].ToString()).ToList();
            foreach (var item in grpOrgDatas)
            {
                var ctx = userCtx.CreateAgentDBContext(item.Key);
                //var defaultStore = ctx.LoadBizDataByNo("ydj_storehouse", "fnumber", new List<string>() { "YJDFZBCK" });
                //if (defaultStore == null || !defaultStore.Any())
                //{
                //    sb.AppendLine($"经销商【{ctx.Company}】未查询到预设的仓库【YJDFZBCK】。");
                //    continue;
                //}
                var purids = item.ToList().Select(f => f["fid"].ToString()).Distinct().ToList();
                var purOrderList = userCtx.LoadBizDataById("stk_postockin", purids);
                //purOrderList.ForEach(a =>
                //{
                //    //a["fstockstaffid"] = defaultStaff.FirstOrDefault()["id"];
                //    //a["fstockdeptid"] = defaultDept.FirstOrDefault()["id"];
                //    //var entry = a["fentity"] as DynamicObjectCollection;
                //    //entry.ForEach(e =>
                //    //{
                //    //    e["fstorehouseid"] = defaultStore.FirstOrDefault()["id"];
                //    //    e["fstockstatus"] = "311858936800219137";
                //    //});
                //}
                //);
                //创建和重新审核的数据，做提交
                var needSubmitDatas = purOrderList.Where(a => Convert.ToString(a["fstatus"]).ToUpper().Equals("C") || Convert.ToString(a["fstatus"]).ToUpper().Equals("B")).ToList();
                if (needSubmitDatas.Any())
                {
                    billnos = needSubmitDatas.Select(o => Convert.ToString(o["fbillno"])).ToList();
                    IOperationResult submitResult = null;
                    try
                    {
                        submitResult = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, "stk_postockin", needSubmitDatas, "submit", keys);
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 提交失败；" + JsonConvert.SerializeObject(submitResult?.ComplexMessage.ErrorMessages) + "；异常描述：" + ex.Message);
                        failNo.AddRange(billnos);
                    }
                    finally
                    {
                        if (submitResult != null)
                        {
                            if (submitResult.IsSuccess)
                            {
                                successNo.AddRange(billnos);
                                sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 提交成功。");
                            }
                            else
                            {
                                failNo.AddRange(billnos);
                                sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 提交失败；" + JsonConvert.SerializeObject(submitResult.ComplexMessage.ErrorMessages));
                            }
                        }
                        else
                        {
                            sb.AppendLine($" 采购入库单{billnos.JoinEx(",", false)} 提交失败；" + JsonConvert.SerializeObject(submitResult));
                            failNo.AddRange(billnos);
                        }
                    }
                }
                //提交的数据做审核
                var needAuditDatas = purOrderList.Where(a => Convert.ToString(a["fstatus"]).ToUpper().Equals("D")).ToList();
                if (needAuditDatas.Any())
                {
                    IOperationResult auditResult = null;
                    billnos = needAuditDatas.Select(o => Convert.ToString(o["fbillno"])).ToList();
                    try
                    {
                        auditResult = ctx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(ctx, "stk_postockin", needAuditDatas, "audit", keys);
                    }
                    catch (Exception ex)
                    {
                        sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 审核成功；" + JsonConvert.SerializeObject(auditResult?.ComplexMessage.ErrorMessages) + "；异常描述：" + ex.Message);
                        failNo.AddRange(billnos);
                    }
                    finally
                    {
                        if (auditResult != null)
                        {
                            if (auditResult.IsSuccess)
                            {
                                successNo.AddRange(billnos);
                                sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 审核成功。");
                            }
                            else
                            {
                                failNo.AddRange(billnos);
                                sb.AppendLine($"采购入库单{billnos.JoinEx(",", false)} 审核成功；" + JsonConvert.SerializeObject(auditResult.ComplexMessage.ErrorMessages));
                            }
                        }
                        else
                        {
                            sb.AppendLine($" 采购入库单{billnos.JoinEx(",", false)} 审核成功；" + JsonConvert.SerializeObject(auditResult));
                            failNo.AddRange(billnos);
                        }
                    }
                }
            }

            result.SimpleMessage = sb.ToString();
            result.IsSuccess = true;
            result.SrvData = new { successNo = successNo, failNo = failNo, message = new List<string>() { sb.ToString() } };
            return result;
        }


        private IOperationResult CheckDefaultData()
        {
            var result = userCtx.Container.GetService<IOperationResult>();
            result.IsSuccess = true;
            var defaultStaff = userCtx.LoadBizDataByNo("ydj_staff", "fnumber", new List<string>() { "YJDFXNYG" });
            if (defaultStaff == null || !defaultStaff.Any())
            {
                result.IsSuccess = false;
                result.SimpleMessage = "未在总部查询到预设的员工【YJDFXNYG】。";
                return result;
            }
            var defaultDept = userCtx.LoadBizDataByNo("ydj_dept", "fnumber", new List<string>() { "YJDFXNBM" });
            if (defaultDept == null || !defaultDept.Any())
            {
                result.IsSuccess = false;
                result.SimpleMessage = "未在总部查询到预设的部门【YJDFXNBM】。";
                return result;
            }
            return result;
        }


        /// <summary>
        /// 获取符合条件的采购入库单
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetPendingOrders(List<string> _data = null)
        {
            string sql = $@"select distinct ord.fid,ord.fbillno,a.fid as postockInId,ord.fmainorgid,ordentry.fentryid,ordentry.fproductid,SUM(b.fbizqty) bizqty
                from T_STK_POSTOCKIN  a with(nolock)
                inner join T_STK_POSTOCKINENTRY b with(nolock) on a.fid=b.fid
                inner join T_YDJ_POORDERENTRY poentry with(nolock) on b.fsourceentryid=poentry.fentryid
                inner join T_YDJ_ORDER ord with(nolock) on poentry.fsoorderinterid=ord.fid
                inner join T_YDJ_ORDEREntry ordentry with(nolock) on ord.fid=ordentry.fid and poentry.fsoorderentryid=ordentry.fentryid
                where a.fpiecesendtag=1 and a.fstatus='E' and a.fcancelstatus=0 and a.fisoutstock=0  and ordentry.ftransoutqty=0  and ord.fpiecesendtag=1  AND a.fmanagemodel='0'
                group by ord.fid,ord.fbillno,a.fid,ord.fmainorgid,ordentry.fentryid,ordentry.fproductid";
            if (_data != null && _data.Count > 0)
            {
                sql += $" AND a.fbillno in ('{string.Join("','", _data)}')";
            }

            return dBService.ExecuteDynamicObject(userCtx, sql);
        }

        /// <summary>
        /// 获取符合条件的订单
        /// </summary>
        /// <returns></returns>
        protected DynamicObjectCollection GetPendingPurchaseOrders(List<string> _data = null)
        {
            string sql = $@"select a.fid,a.fbillno,a.fmainorgid,b.fentryid,b.fproductid,mat.fnumber,mat.fname,b.fattrinfo,b.fattrinfo_e,b.fcustomdes_e,poentry.fseq_e,poentry.fentryid poentryid,poentry.fid as poId,poentry.fbizqty pobizqty,po.fhqderno ,po.fbillno  pobillno
                        from t_ydj_order a with(nolock)
                        inner join t_ydj_orderentry b with(nolock) on a.fid=b.fid
                        inner join t_ydj_poorderentry poentry with(nolock) on b.fentryid=poentry.fsoorderentryid
                        inner join t_ydj_purchaseorder po with(nolock) on poentry.fid=po.fid
                        inner join t_bd_material mat with(nolock) on b.fproductid=mat.fid
                        where a.fpiecesendtag=1 and fordersignstatus<>'entrysign_type_01' and b.fbizpurqty>0 and b.fdeliverytype='delivery_type_01'  AND a.fmanagemodel='0'  ";
            if (_data != null && _data.Count > 0)
            {
                sql += $" AND po.fbillno in ('{string.Join("','", _data)}')";
            }

            return dBService.ExecuteDynamicObject(userCtx, sql);
        }


        /// <summary>
        /// 获取符合条件的订单
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetPendingPurchaseOrders(string tmpName, List<string> _data = null)
        {
            string sql = $@"select a.fid,a.fbillno,a.fmainorgid,b.fentryid,poentry.fseq_e,poentry.fentryid poentryid,poentry.fid as poId,poentry.fbizqty pobizqty 
                        into {tmpName}  
                        from t_ydj_order a with(nolock)
                        inner join t_ydj_orderentry b with(nolock) on a.fid=b.fid
                        inner join t_ydj_poorderentry poentry with(nolock) on b.fentryid=poentry.fsoorderentryid
                        inner join t_ydj_purchaseorder po with(nolock) on poentry.fid=po.fid
                        where a.fpiecesendtag=1 and fordersignstatus<>'entrysign_type_01' and b.fbizpurqty>0 and b.fdeliverytype='delivery_type_01'  AND a.fmanagemodel='0' ";

            if (_data != null && _data.Count > 0)
            {
                sql += $" AND a.fbillno in ('{string.Join("','", _data)}')";
            }

            return dBService.ExecuteDynamicObject(userCtx, sql);
        }


        /// <summary>
        /// 获取符合条件的采购入库单
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetPendingPurchaseOrdersAsync(List<string> _data = null)
        {
            string sql = "select T_STK_POSTOCKIN.fid,T_STK_POSTOCKIN.fmainorgid from T_STK_POSTOCKIN with(nolock) where fpiecesendtag=1 and fstatus<>'E' and fcancelstatus=0 AND fmanagemodel='0' ";
            if (_data != null && _data.Count > 0)
            {
                sql += $" AND T_STK_POSTOCKIN.fbillno in ('{string.Join("','", _data)}')";
            }
            return dBService.ExecuteDynamicObject(userCtx, sql);
        }



        /// <summary>
        /// 【直营】获取符合条件的采购入库单
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection GetZYPendingPurchaseOrdersAsync(List<string> _data = null)
        {
            string sql = "select T_STK_POSTOCKIN.fid,T_STK_POSTOCKIN.fmainorgid from T_STK_POSTOCKIN with(nolock) where fpiecesendtag=1 and fstatus<>'E' and fcancelstatus=0 AND fmanagemodel='1' ";
            if (_data != null && _data.Count > 0)
            {
                sql += $" AND T_STK_POSTOCKIN.fbillno in ('{string.Join("','", _data)}')";
            }
            return dBService.ExecuteDynamicObject(userCtx, sql);
        }


        /// <summary>
        /// 获取E3进度并存到表里
        /// </summary>
        /// <returns></returns>
        public IOperationResult ZYGetE3LogisticsProcess(List<string> _data = null)
        {
            sb = new StringBuilder();
            List<string> successNo = new List<string>();
            List<string> failNo = new List<string>();
            List<string> failMes = new List<string>();
            var result = userCtx.Container.GetService<IOperationResult>();
            var orders = ZYGetPendingPurchaseOrders(_data);
            List<DynamicObject> objs = new List<DynamicObject>();
            var e3Obj = MuSiApi.GetE3ExtApp(userCtx);
            if (e3Obj == null)
            {
                result.IsSuccess = false;
                failMes.Add("获取E3应用信息失败！");
                result.SimpleMessage = "获取E3应用信息失败！";
                return result;
            }

            string appKey = Convert.ToString(e3Obj["fappkey"]);
            var e3Html = MetaModelService.LoadFormModel(userCtx, e3htmlId);
            if (appKey.IsNullOrEmptyOrWhiteSpace())
            {
                result.IsSuccess = false;
                failMes.Add("外部应用匹配不正确！");
                result.SimpleMessage = "外部应用匹配不正确！";
                return result;
            }
            if (orders == null || orders.Count == 0)
            {
                result.IsSuccess = false;
                result.SimpleMessage = "未查询到需要符合条件的数据！";
                failMes.Add("未查询到需要符合条件的数据！");
                return result;
            }
            foreach (var dataItem in orders)
            {
                MSE3LogisticsRequestDTO e3Request = new MSE3LogisticsRequestDTO();
                e3Request.khck = Convert.ToString(dataItem["fbillno"]);
                if (Convert.ToInt32(dataItem["fseq_e"]) >= 10)
                {
                    e3Request.dealCodeLineSn = "000" + Convert.ToInt32(dataItem["fseq_e"]);
                }
                else
                {
                    e3Request.dealCodeLineSn = "0000" + Convert.ToInt32(dataItem["fseq_e"]) * 10;
                }
                //e3Request.dealCodeLineSn = Convert.ToString(dataItem["fseq_e"]);

                JObject configObj = JObject.Parse(appKey);
                string key = configObj.GetJsonValue("key", "");
                string requestTime = DateTime.Now.ToString("yyyyMMddHHmmss");
                string secret = configObj.GetJsonValue("secret", "");
                string version = configObj.GetJsonValue("version", "");
                string serviceType = configObj.GetJsonValue("serviceType", "");
                string data = e3Request.ToJson();
                //data = "{\"express_sn\":\"2103032029547718\",\"dealCode\":\"\",\"khck\":\"\"}";

                var sign = SecurityUtil.HashString($@"key={key}&requestTime={requestTime}&secret={secret}&version={version}&serviceType={serviceType}&data={data}");
                MSE3LogisticsDTO dto = new MSE3LogisticsDTO();
                dto.sign = sign;
                dto.key = key;
                dto.requestTime = requestTime;
                dto.version = version;
                dto.serviceType = serviceType;
                dto.data = data;

                var e3result = MuSiApi.GetE3LogisticsProgress(userCtx, e3Html, dto);
                //step1:将获取的数据存到中间表
                if (e3result != null)
                {
                    //JObject _obj = result.data;
                    //if (_obj == null)
                    //{

                    //}
                    //JArray array = _obj["data"] as JArray;
                    JArray array = e3result.data as JArray;
                    foreach (var item in array)
                    {
                        // 获取物流单号
                        string expressSn = Convert.ToString(item["express_sn"]);

                        // 获取送货日期
                        string deliveryDate = "";
                        string signDate = "";
                        var trackList = item["track_list"] as JArray;
                        if (trackList != null)
                        {
                            var signNode = trackList.FirstOrDefault(t => Convert.ToString(t["shipping_status"]) == "3");
                            if (signNode != null)
                            {
                                signDate = Convert.ToString(signNode["shipping_time"]);
                            }
                            var deliveryNode = trackList.FirstOrDefault(t => Convert.ToString(t["shipping_status"]) == "2");
                            if (deliveryNode != null)
                            {
                                deliveryDate = Convert.ToString(deliveryNode["shipping_time"]);
                            }
                        }

                        // 遍历 items_list 匹配 dataItem
                        foreach (var subItem in item["items_list"])
                        {
                            string itemCode = Convert.ToString(subItem["itemCode"]);
                            string dealCodeLineSn = Convert.ToString(subItem["dealCodeLineSn"]);
                            string itemQuantity = Convert.ToString(subItem["itemQuantity"]);

                            // 匹配 dataItem 的商品和行编码
                            if (Convert.ToString(dataItem["fnumber"]) == itemCode &&
                                Convert.ToInt32(dataItem["fseq_e"]) * 10 == Convert.ToInt32(dealCodeLineSn))
                            {
                                // 设置 obj 的值
                                DynamicObject obj = new DynamicObject(e3Html.GetDynamicObjectType(userCtx));
                                obj["fjson"] = e3result.ToJson();
                                obj["forderid"] = dataItem["fid"]; // 合同ID
                                obj["fsourcetype"] = "ydj_order"; // 合同
                                obj["fsourcenumber"] = dataItem["fbillno"]; // 合同编号
                                obj["fentryid"] = dataItem["fentryid"]; // 合同明细ID
                                obj["fmaterialid"] = dataItem["fproductid"]; // 商品ID
                                obj["fattrinfo"] = dataItem["fattrinfo"]; // 辅助属性
                                obj["fattrinfo_e"] = dataItem["fattrinfo_e"]; // 辅助属性
                                obj["fcustomdes_e"] = dataItem["fcustomdes_e"]; // 定制说明
                                obj["fpoid"] = dataItem["poId"]; // 采购订单ID
                                obj["fpobillno"] = dataItem["pobillno"]; // 采购订单号
                                obj["fhqderno"] = dataItem["fhqderno"]; // 采购订单号
                                obj["fposeq"] = dataItem["fseq_e"]; // 采购订单行号
                                obj["flogisticsno"] = expressSn; // 物流送货单号
                                if (!string.IsNullOrWhiteSpace(deliveryDate))
                                {
                                    obj["fdeliverydate"] = deliveryDate; // 送货日期
                                }
                                obj["fbizqty"] = 0; // 签收数量
                                // 判断行签收状态
                                if (Convert.ToDecimal(dataItem["pobizqty"]) == Convert.ToDecimal(itemQuantity))
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                                }
                                else if (Convert.ToDecimal(dataItem["pobizqty"]) > Convert.ToDecimal(itemQuantity) && Convert.ToDecimal(itemQuantity) > 0)
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                                }
                                else if (Convert.ToDecimal(itemQuantity) == 0)
                                {
                                    obj["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                                }
                                if (!string.IsNullOrWhiteSpace(signDate))
                                {
                                    obj["fbizqty"] = itemQuantity; // 签收数量
                                    obj["fsigndate"] = signDate; // 签收日期
                                }
                                obj["fmodifydate"] = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"); // 数据更新时间
                                objs.Add(obj);
                            }
                        }
                    }

                }
                else
                {
                    failNo.Add(e3Request.khck);
                    failMes.Add($"获取E3物流进度失败，采购订单号：{e3Request.khck}，采购订单行号：{Convert.ToInt32(dataItem["fseq_e"])}，请检查E3系统！");
                }
            }
            // 1. 按fentryid分组，汇总签收日期不为空的签收数量
            var entrySignSummary = objs
                .GroupBy(o => Convert.ToString(o["fentryid"]))
                .Select(g => new
                {
                    fentryid = g.Key,
                    TotalSignQty = g.Where(a => !string.IsNullOrWhiteSpace(Convert.ToString(a["fsigndate"]))).Sum(x => Convert.ToDecimal(x["fbizqty"]))
                })
                .ToDictionary(x => x.fentryid, x => x.TotalSignQty);


            foreach (var _dataItem in entrySignSummary)
            {
                var objItem = objs.Where(o => Convert.ToString(o["fentryid"]) == _dataItem.Key).FirstOrDefault();
                var orderItem = orders.Where(o => Convert.ToString(o["fentryid"]) == _dataItem.Key).FirstOrDefault();
                if (objItem != null && orderItem != null)
                {
                    decimal fbizqty = Convert.ToDecimal(orderItem["pobizqty"]);
                    decimal TotalSignQty = Convert.ToDecimal(_dataItem.Value);
                    if (TotalSignQty == 0)
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_03"; // 未签收
                    }
                    else if (TotalSignQty >= fbizqty)
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_01"; // 已签收
                    }
                    else
                    {
                        objItem["fentrysignstatus"] = "entrysign_type_02"; // 部分签收
                    }
                }
            }
            if (objs.Any())
            {
                var topCtx = userCtx.CreateTopOrgDBContext();
                var orderIds = objs.Select(a => Convert.ToString(a["forderid"])).Distinct().ToList();
                var logicData = topCtx.LoadBizDataByFilter(e3htmlId, $" forderid in ('{string.Join("','", orderIds)}')");
                var deleteResult = topCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(topCtx, e3htmlId, logicData, "delete", null);
                deleteResult.ThrowIfHasError(true, "E3物流进度查询中间表删除失败！");
                var billnos = objs.Select(a => Convert.ToString(a["fsourcenumber"])).ToList();
                var auditResult = topCtx.Container.GetService<IHttpServiceInvoker>().InvokeBillOperation(topCtx, e3htmlId, objs, "save", null);
                if (auditResult.IsSuccess)
                {
                    successNo.AddRange(billnos);
                }
                else failNo.AddRange(billnos);
            }
            else
            {
                failNo.AddRange(failNo);
            }
            result.IsSuccess = true;
            result.SrvData = new { successNo = successNo, failNo = failNo, failMes = failMes };
            return result;
        }

        /// <summary>
        /// 获取符合条件的订单
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection ZYGetPendingPurchaseOrders(List<string> _data = null)
        {
            string sql = "";
                sql = $@"select a.fid,a.fbillno,a.fmainorgid,b.fentryid,b.fproductid,b.fbizqty,mat.fnumber,mat.fname,b.fattrinfo,b.fattrinfo_e,b.fcustomdes_e,b.fseq_e fseq_e,'' poentryid,'' as poId,b.fbizqty pobizqty,'0' fhqderno ,'' pobillno
                        from t_ydj_order a with(nolock)
                        inner join t_ydj_orderentry b with(nolock) on a.fid=b.fid
                        inner join T_STK_POSTOCKINEntry poStockIn with(nolock) on a.fid=poStockIn.fsoorderinterid AND b.fentryid=poStockIn.fsoorderentryid
                        inner join t_bd_material mat with(nolock) on b.fproductid=mat.fid
                        where a.fpiecesendtag=1 and fordersignstatus<>'entrysign_type_01'  and b.fdeliverytype='delivery_type_01' 
                         AND a.fmanagemodel='1' ";
          
            return dBService.ExecuteDynamicObject(userCtx, sql);
        }
    }
}
