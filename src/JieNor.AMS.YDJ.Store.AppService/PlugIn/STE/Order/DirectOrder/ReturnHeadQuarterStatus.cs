using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.MS.API.DTO.Order.DirectOrder;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using Newtonsoft.Json;

namespace JieNor.AMS.YDJ.Store.AppService.PlugIn.STE.Order.DirectOrder
{
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("returnheadquarterstatus")]
    public class ReturnHeadQuarterStatus : AbstractOperationServicePlugIn
    {
        private List<ReturnDirectOrderHeadquarterStatusDTO> SapInfo { set; get; }
        public override void InitializeOperationDataEntity(InitializeDataEntityEventArgs e)
        {
            base.InitializeOperationDataEntity(e);
            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;
            var sapInfoStr = this.GetQueryOrSimpleParam<string>("sapInfo", "");
            if (!sapInfoStr.IsNullOrEmptyOrWhiteSpace())
            {
               this.SapInfo = JsonConvert.DeserializeObject<List<ReturnDirectOrderHeadquarterStatusDTO>>(sapInfoStr);
            }
        }

        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            //错误消息
            var errorMessage = "";
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var isTrue = true;
                //总部状态
                var chstatus = Convert.ToString(newData["fchstatus"]);
                if (!chstatus.IsNullOrEmptyOrWhiteSpace())
                {
                    //'1':'已提交总部','2':'已驳回','3':'已终审'
                    switch (chstatus)
                    {
                        case "3":
                            var tempStr = string.Empty;
                            var sapOrderStatus = this.GetQueryOrSimpleParam<string>("sapOrderStatus","");
                            if (this.SapInfo != null && this.SapInfo.Any())
                            {
                                var findSapInfo = this.SapInfo.FirstOrDefault(x => x.OrderBillNo.Equals(Convert.ToString(newData["fbillno"])));
                                if (findSapInfo != null)
                                {
                                    sapOrderStatus = findSapInfo.SapOrderStatus;
                                }
                            }
                            if (!sapOrderStatus.IsNullOrEmptyOrWhiteSpace())
                            {
                                if (sapOrderStatus.Equals("2"))
                                {
                                    tempStr = "驳回";
                                }
                                else if (sapOrderStatus.Equals("3"))
                                {
                                    tempStr = "再次终审";
                                }
                                else if(sapOrderStatus.EqualsIgnoreCase("4"))
                                {
                                    tempStr = "修改成【锁单失败】状态";
                                }
                            }
                            errorMessage = $"该销售订单【{Convert.ToString(newData["fbillno"])}】已终审，不能{tempStr}！";
                            isTrue = false;
                            break;
                    }
                }
                return isTrue;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
            
            /*e.Rules.Add(this.RuleFor("fbillhead",data=>data).IsTrue((newData, oldData) =>
            {
                var isTrue = true;
                var orderNo = Convert.ToString(newData["fbillno"]);
                var channel = Convert.ToString(newData["fchannel"]);
                if (this.SapInfo != null && this.SapInfo.Any() && !channel.IsNullOrEmptyOrWhiteSpace())
                {
                    var findSapInfo = this.SapInfo.FirstOrDefault(x => x.OrderBillNo.Equals(orderNo));
                    //说明找到了，那就在这里判断这个是不是为共享计提单号是不是为空的
                    if (findSapInfo != null)
                    {
                        var sapShareCostBillNo = findSapInfo.SapShareCostBillNo;
                        if (sapShareCostBillNo == null || sapShareCostBillNo.IsNullOrEmptyOrWhiteSpace())
                        {
                            errorMessage = $"销售合同【{orderNo}】关联了合作渠道，对应的共享计提单号不能为空！";
                            isTrue = false;;
                        }
                    }
                }

                return isTrue;
            }).WithMessage("{0}",(billObj, propObj) => errorMessage));*/
            
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            
            if(e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var saveOrderDys = new List<DynamicObject>();
            if(this.Result.ComplexMessage.SuccessMessages != null && this.Result.ComplexMessage.SuccessMessages.Any())
            {
                this.Result.ComplexMessage.SuccessMessages.Clear();
            }
            /*//总部终审时间
            var sapFinalAuditTime = this.GetQueryOrSimpleParam<DateTime>("sapFinalAuditTime");
            
            //sap的合同号
            var sapOrderNo = this.GetQueryOrSimpleParam<string>("sapOrderNo");
            
            //驳回原因
            var sapRejectReason = this.GetQueryOrSimpleParam<string>("sapRejectReason");
            
            //sap订单状态
            var sapOrderStatus = this.GetQueryOrSimpleParam<string>("sapOrderStatus","");
            
            var orderBillNo = this.GetQueryOrSimpleParam<string>("orderBillNo");

            var sapOrderType = this.GetQueryOrSimpleParam<string>("sapOrderType");

            var sapShareCostBillNo = this.GetQueryOrSimpleParam<string>("sapShareCostBillNo","");

            var findOrderDy = e.DataEntitys.FirstOrDefault(x=>Convert.ToString(x["fbillno"]).Equals(orderBillNo));*/


            foreach (var orderDy in e.DataEntitys)
            {
                if (this.SapInfo != null && this.SapInfo.Any())
                {
                    var orderNo = Convert.ToString(orderDy["fbillno"]);

                    var channel = Convert.ToString(orderDy["fchannel"]);
                    
                    var findSapInfo = this.SapInfo.FirstOrDefault(x=>x.OrderBillNo.Equals(orderNo));
                    
                    if (findSapInfo != null)
                    {
                        switch (findSapInfo.SapOrderStatus.ToLower())
                        {
                            case "2":
                                //总部状态
                                orderDy["fchstatus"] = "2";
                                //总部驳回原因(终审了，那就要把这个清除)
                                orderDy["fheadquartsyncmessage"] = findSapInfo.SapRejectReason;
                                orderDy["fheadcontracttype"] = findSapInfo.SapOrderType;
                                //总部终审时间
                                orderDy["fheadquartfrtime"] = null;
                                break;
                            //'1':'已提交总部','2':'已驳回','3':'已终审'
                            case "3":
                                //总部状态
                                orderDy["fchstatus"] = "3";
                                //sap合同号
                                orderDy["fheadquartno"] = findSapInfo.SapOrderNo;
                                //总部终审时间
                                orderDy["fheadquartfrtime"] = findSapInfo.SapFinalAuditTime;
                                //总部驳回原因(终审了，那就要把这个清除)
                                if (!findSapInfo.SapRejectReason.IsNullOrEmptyOrWhiteSpace())
                                {
                                    orderDy["fheadquartsyncmessage"] = findSapInfo.SapRejectReason;
                                }
                                else
                                {
                                    orderDy["fheadquartsyncmessage"] = string.Empty;
                                }
                                orderDy["fheadcontracttype"] = findSapInfo.SapOrderType;
                                orderDy["fsharecostbillno"] = findSapInfo.SapShareCostBillNo;
                                SetSapItemSeqValue(orderDy, findSapInfo);
                                break;
                            case "4":
                                //总部状态
                                orderDy["fchstatus"] = "4";
                                //总部驳回原因(终审了，那就要把这个清除)
                                orderDy["fheadquartsyncmessage"] = findSapInfo.SapRejectReason;
                                break;
                            default:
                                break;
                        }
                        saveOrderDys.Add(orderDy);
                        this.Result.ComplexMessage.SuccessMessages.Add($"终端销售合同【{Convert.ToString(orderDy["fbillno"])}】回传审批状态成功！");
                    }
                    else
                    {
                        this.Result.ComplexMessage.ErrorMessages.Add($"终端销售合同【{Convert.ToString(orderDy["fbillno"])}】找不到对应传进来的中台数据包，请检查!");
                    }
                    
                }
                else
                {
                    this.Result.ComplexMessage.ErrorMessages.Add("没有传对应的数据包进来，请检查");
                }
            }

            if (saveOrderDys != null && saveOrderDys.Any())
            {
                e.DataEntitys = saveOrderDys.ToArray();
                this.Context.SaveBizData("ydj_order", saveOrderDys);
                this.Result.IsSuccess = true;
            }
            else
            {
                this.Result.IsSuccess = false;
            }
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);
            
            if(e.DataEntitys == null || e.DataEntitys.Length <= 0) return;
            
            // e.DataEntitys.Where(x=>Convert.ToString(x["fchstatus]).EqualsIgnoreCase("3")).ToList();
            
            // 、总部终审状态
            // '1':'已提交总部',
            // '2':'已驳回',
            // '3':'已终审
            var hQAuditOrderDys = e.DataEntitys.Where(x=>Convert.ToString(x["fchstatus"]).Equals("3") && !Convert.ToString(x["fchannel"]).IsNullOrEmptyOrWhiteSpace()).ToList();

            if (hQAuditOrderDys != null && hQAuditOrderDys.Any())
            {
                var musiSerivce = this.Context.Container.GetService<IMuSiService>();
                
                musiSerivce.SyncDirectOrderShareCostBillNo(this.Context,this.HtmlForm,hQAuditOrderDys);
            }
            
        }

        /// <summary>
        /// 设置sap那边传过来的行号
        /// </summary>
        /// <param name="orderDy"></param>
        /// <param name="findSapInfo"></param>
        private void SetSapItemSeqValue(DynamicObject orderDy,ReturnDirectOrderHeadquarterStatusDTO findSapInfo)
        {
            var crmEntrys = orderDy["fentry"] as DynamicObjectCollection;

            var orderItems = findSapInfo.fentry;

            foreach (var crmEntry in crmEntrys)
            {
                var subHqSeq = Convert.ToString(crmEntry["fseq_e"]);

                if (orderItems != null && orderItems.Any())
                {
                    var findOrderItem = orderItems.FirstOrDefault(x=>x.OrderItemSeq.Equals(subHqSeq));

                    if (findOrderItem != null)
                    {
                        crmEntry["fsapitemseq"] = Convert.ToString(findOrderItem.SapItemSeq);
                    }
                }
            }
            
        }
    }
}