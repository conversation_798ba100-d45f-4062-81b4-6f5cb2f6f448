using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.Utils;
using System;
using System.Collections.Generic;
using System.Dynamic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order
{
    /// <summary>
    /// 销售合同：查询焕新订单类型
    /// </summary>
    [InjectService]
    [FormId("ydj_order")]
    [OperationNo("getrenewtypebyid")]
    public class GetRenewTypeById : AbstractOperationServicePlugIn
    {
        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            this.Result.IsSuccess = false;
            base.BeforeExecuteOperationTransaction(e);


            var frenewtype = this.GetQueryOrSimpleParam("frenewtype", "");
            if (string.IsNullOrWhiteSpace(frenewtype))
            {
                throw new BusinessException("frenewtype不能为空!");
            }

            var renewtypeObjs = this.Context.LoadBizDataById("ydj_renewtype", frenewtype);
            if (renewtypeObjs != null)
            {
                List<string> strings = new List<string>();
                var fcategoryentry = renewtypeObjs["fcategoryentry"] as DynamicObjectCollection;
                fcategoryentry.ForEach(a => strings.Add(Convert.ToString(a["fcustomcategory"])));
                this.Result.IsSuccess = true;
                this.Result.SrvData = new
                {
                    fisincome = Convert.ToInt32(renewtypeObjs["fisincome"]),
                    fcategoryentry = strings,
                    fbizorgid = Convert.ToString(renewtypeObjs["fbizorgid"])//所属组织名称
                };
            }
        }
    }
}
