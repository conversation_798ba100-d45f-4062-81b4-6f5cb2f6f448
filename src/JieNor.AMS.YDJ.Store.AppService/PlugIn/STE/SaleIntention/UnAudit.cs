using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.STE.SaleIntention
{
    /// <summary>
    /// 销售订单：反审核
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention")]
    [OperationNo("UnAudit")]
    public class UnAudit: AbstractOperationServicePlugIn
    {
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            e.DataEntitys = checkIncomeDisburse(e.DataEntitys);
            base.BeginOperationTransaction(e);
        }

        /// <summary>
        /// 检查单据是否有已对账或对账中的收支记录
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkIncomeDisburse(DynamicObject[] dataEntities)
        {
            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return dataEntities;
            }
            var ids = dataEntities.Select(x => Convert.ToString(x["id"]));
            var incomeForm = this.MetaModelService.LoadFormModel(this.Context, "coo_incomedisburse");
            var multiValueQueryService = this.Container.GetService<IMultiValueQueryService>();
            var where = "fmainorgid=@fmainorgid and fsourceformid=@fsourceformid";
            var sqlParams = new List<SqlParam>
            {
                new SqlParam("@fmainorgid",System.Data.DbType.String,this.Context.Company),
                new SqlParam("@fsourceformid",System.Data.DbType.String,this.HtmlForm.Id)
            };
            var incomeEntities = multiValueQueryService.Select(this.Context, where, sqlParams, incomeForm, "fsourceid", ids);
            if (incomeEntities == null || incomeEntities.Count <= 0)
            {
                return dataEntities;
            }

            var validEntities = new List<DynamicObject>();
            foreach (var dataEntity in dataEntities)
            {
                var id = Convert.ToString(dataEntity["id"]);
                var incomeItems = incomeEntities.Where(x => Convert.ToString(x["fsourceid"]).EqualsIgnoreCase(id)).ToList();
                if (incomeItems == null || incomeItems.Count <= 0)
                {
                    validEntities.Add(dataEntity);
                    continue;
                }
                var isValid = true;
                foreach (var incomeItem in incomeItems)
                {
                    var statementStatus = Convert.ToString(incomeItem["fstatementstatus"]);
                    if (statementStatus == "2" || statementStatus == "3")
                    {
                        isValid = false;
                        this.Result.ComplexMessage.ErrorMessages.Add($"编号[{dataEntity["fbillno"]}]的单据已有收支记录为卖场对账中或已对账状态，不允许反审核!");
                        break;
                    }
                }
                if (isValid)
                {
                    validEntities.Add(dataEntity);
                }
            }

            return checkStoreStatement(validEntities, incomeEntities);
        }

        /// <summary>
        /// 检查销售意向的收支记录是否加入店面结算对账单明细
        /// </summary>
        /// <param name="dataEntities"></param>
        /// <param name="incomeEntities"></param>
        /// <returns></returns>
        private DynamicObject[] checkStoreStatement(List<DynamicObject> dataEntities, List<DynamicObject> incomeEntities)
        {
            if (dataEntities == null || dataEntities.Count <= 0 || incomeEntities == null || incomeEntities.Count <= 0)
            {
                return dataEntities.ToArray();
            }

            var incomeIds = incomeEntities.Select(x => Convert.ToString(x["id"])).ToList();
            var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_storestatement");
            var multiValueQuertyService = this.Container.GetService<IMultiValueQueryService>();
            var where = "fmainorgid=@fmainorgid";
            var sqlParams = new List<SqlParam> { new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company) };
            var storeEntities = multiValueQuertyService.Select(this.Context, where, sqlParams, htmlForm, "fincomeid", incomeIds);

            if (storeEntities == null || storeEntities.Count <= 0)
            {
                return dataEntities.ToArray();
            }

            var sourceIds = new List<string>();
            foreach(var storeEntity in storeEntities)
            {
                var incomeEntries = storeEntity["fincomeentry"] as DynamicObjectCollection;
                if (incomeEntries == null || incomeEntries.Count <= 0)
                {
                    continue;
                }
                foreach(var incomeEntry in incomeEntries)
                {
                    var incomeId = Convert.ToString(incomeEntry["fincomeid"]);
                    var incomeEntity = incomeEntities.FirstOrDefault(x => Convert.ToString(x["id"]).EqualsIgnoreCase(incomeId));
                    if (incomeEntity == null)
                    {
                        continue;
                    }
                    var sourceId = Convert.ToString(incomeEntity["fsourceid"]);
                    if (false == sourceIds.Contains(sourceId))
                    {
                        sourceIds.Add(sourceId);
                    }
                }
            }

            var validEntities = new List<DynamicObject>();
            foreach (var dataEntity in dataEntities)
            {
                var id = Convert.ToString(dataEntity["id"]);
                if (sourceIds.Contains(id))
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"编号[{dataEntity["fbillno"]}]单据的收支记录已关联{htmlForm.Caption},不允许反审核!");
                    continue;
                }
                validEntities.Add(dataEntity);
            }
            return validEntities.ToArray();
        }
    }
}
