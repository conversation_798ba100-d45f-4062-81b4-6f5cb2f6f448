using System;
using System.Linq;
using System.Collections.Generic;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.Helpers;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
using System.Data;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.Framework.MetaCore.FormOp.FormService;
using System.Text;
using JieNor.Framework.DataTransferObject.Poco;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.HqSubPurOrder
{
    /// <summary>
    /// 采购单重新提交总部：提交总部
    /// </summary>
    [InjectService]
    [FormId("ydj_hqsubpurorder")]
    [OperationNo("submitpur")]
    public class SubmitPur : AbstractOperationServicePlugIn
    {
        ///// <summary>
        ///// 预处理校验规则
        ///// </summary>
        ///// <param name="e"></param>
        //public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        //{
        //    base.PrepareValidationRules(e);
        //}

        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0) return;

            List<string> purNos = e.DataEntitys.Select(x => Convert.ToString(x["fpurnumber"])).Distinct().ToList();
            StringBuilder strWhere = new StringBuilder();
            strWhere.Append(string.Join(",", purNos.Select((x, i) => "@fbillno" + i)));
            List<SqlParam> param = new List<SqlParam>();
            param.AddRange(purNos.Select((x, i) => new SqlParam($"@fbillno{i}", System.Data.DbType.String, x)));
            var purOrderForm = this.Context.Container.GetService<IMetaModelService>()?.LoadFormModel(Context, "ydj_purchaseorder");
            var dataEntitys = this.DBService.ExecuteDynamicObject(this.Context, purOrderForm.GetDynamicObjectType(this.Context), "select fid,fbillno,fmainorgid,frenewalflag from t_ydj_purchaseorder where fbillno in (" + strWhere + ")", param);
            
            if (dataEntitys == null || !dataEntitys.Any())
            {
                //this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, e.DataEntitys, "delete", null);
                this.Result.IsSuccess = false;
                this.Result.ComplexMessage.ErrorMessages.Add("您提交的单号于系统中全部未匹配到采购订单，请检查！");
                //this.AddRefreshPageAction();
                return;
            }
            var noExistNos = e.DataEntitys.Where(x => !dataEntitys.Select(g=>Convert.ToString(g["fbillno"])).Contains(Convert.ToString(x["fpurnumber"])))?.Select(x=> Convert.ToString(x["fpurnumber"]));
            if(noExistNos!=null&& noExistNos.Any())
            {
                foreach (var item in noExistNos)
                {
                    this.Result.ComplexMessage.ErrorMessages.Add($"您提交的单号为【{item}】的数据于系统中未匹配到对应采购订单！");
                }
            }
            Dictionary<string, List<DynamicObject>> data = dataEntitys.GroupBy(x => x["fmainorgid"]).ToDictionary(g => g.Key.ToString(), g=>g.ToList());
            foreach (var item in data)
            {
                var agentCtx= this.Context.CreateDBContextByCompanyId(item.Key);
                var lstSelRows = item.Value.Select(o => new SelectedRow()
                {
                    PkValue = o["id"] as string
                })
                .ToArray();

                var lstSelRows_Renew = item.Value
                .Where(o => Convert.ToString(o["frenewalflag"]).ToLower().EqualsIgnoreCase("true") || Convert.ToString(o["frenewalflag"]).EqualsIgnoreCase("1"))
                .Select(o => new SelectedRow()
                {
                    PkValue = o["fid"] as string
                }).ToArray();

                Dictionary<string, object> option = new Dictionary<string, object>();
                option.Add("IgnoreCheckPermssion", "true");//去掉权限校验
                option.Add("istopoper", "1");
                if (lstSelRows.Any()) 
                {
                    var result = this.Gateway.InvokeListOperation(agentCtx, "ydj_purchaseorder", lstSelRows, "SubmitHQ", option);
                    result.ThrowIfHasError(true, $"{this.HtmlForm.Caption}{this.OperationName}失败！");
                    if (!result.IsSuccess)
                    {
                        this.Result = result;
                        return;
                    }
                }

                if (lstSelRows_Renew.Any()) 
                {
                    option.Add("IgnoreValidateDataEntities", "true");
                    var result = this.Gateway.InvokeListOperation(agentCtx, "ydj_purchaseorder", lstSelRows_Renew, "renewsubmithq", option);
                    if (!result.IsSuccess)
                    {
                        this.Result = result;
                        return;
                    }
                }
            }
            foreach (var item in dataEntitys)
            {
                this.Result.ComplexMessage.SuccessMessages.Add($"{this.OperationContext.HtmlForm.Caption}{item["fbillno"]}{this.OperationName}操作成功！");
            }
            this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, e.DataEntitys.Where(x=>dataEntitys.Select(g => Convert.ToString(g["fbillno"])).Contains(Convert.ToString(x["fpurnumber"]))), "delete", null);
            this.Result.IsSuccess = true;
            //this.Result.SimpleMessage = "提交总部成功！";
            this.AddRefreshPageAction();
        }
    }
}