using System;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchasePrice
{
    /// <summary>
    /// 采购价目：改价
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseprice|ydj_selfpurchaseprice")]
    [OperationNo("Push")]
    public class Push : AbstractOperationServicePlugIn
    {

        public override void BeforeExecuteOperationTransaction(BeforeExecuteOperationTransaction e)
        {
            base.BeforeExecuteOperationTransaction(e);
            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            foreach (var bill in e.DataEntitys)
            {
                var fmainorgid = bill["fmainorgid"]?.ToString();
                var fnumber = bill["fnumber"]?.ToString();
                if (bill.DataEntityState.FromDatabase && !fmainorgid.EqualsIgnoreCase(this.Context.Company))
                {
                    throw new Exception(@"所选价目表【{0}】是总部授权的价目表，只能列表查看，不能进行编辑！".Fmt(fnumber));
                }
            }

        }


    }



}
 
