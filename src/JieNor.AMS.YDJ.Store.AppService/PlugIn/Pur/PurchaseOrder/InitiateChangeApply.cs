using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.Validation.Pur.PurchaseOrder;
using JieNor.Framework;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 发起变更申请
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("initiatechangeapply")]
    public class InitiateChangeApply : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);
            // e.Rules.Add(this.);
            // e.Rules.Add(this.RuleFor("fbillhead",data=>data).IsTrue((newData, oldData) =>
            // {
            //     return true;
            // }).WithMessage(""));
            e.Rules.Add(new InitiateChangeApplyValidation());
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);
            if(e.DataEntitys == null  || e.DataEntitys.Length <= 0) return;
            //先查找是不是有驳回状态的采购变更申请单，有的话那就用驳回的那个采购变更申请单
            var purchaseOrderIds = e.DataEntitys.Select(x=>Convert.ToString(x["id"])).ToList();

            //'01':'变更中','02':'变更完成','03':'驳回'
            var sqlStr = $" fsourceid in ( {string.Join(",", purchaseOrderIds.Select(x=>$"'{x}'"))} ) and fchangeapplystatus = '03' and fmainorgid = '{this.Context.Company}' ";

            var findPurOrderApplyDys = this.Context.LoadBizDataByACLFilter("ydj_purchaseorderapply_chg",sqlStr,false);

            var purchaseOrderIdSet = new HashSet<string>();

            if (findPurOrderApplyDys != null && findPurOrderApplyDys.Any())
            {
                findPurOrderApplyDys.Select(x => Convert.ToString(x["fsourceid"])).ToList().ForEach(x => purchaseOrderIdSet.Add(x));
            }

            if (purchaseOrderIdSet != null && purchaseOrderIdSet.Any())
            {
                //说明当前的数据包全部都有驳回状态的采购变更申请单，不需要再次下推采购变更申请单
                if (purchaseOrderIdSet.Count == e.DataEntitys.Length)
                {
                    var purchaseOrderApplyChgHtmlForm = MetaModelService.LoadFormModel(this.Context,"ydj_purchaseorderapply_chg");
                    foreach (var findPurOrderApplyDy in findPurOrderApplyDys)
                    {
                        //弹出弹窗
                        var action = this.OperationContext.UserContext.ShowSpecialForm(purchaseOrderApplyChgHtmlForm,
                            findPurOrderApplyDy,
                            false,
                            this.OperationContext.PageId,
                            Enu_OpenStyle.Modal,
                            Enu_DomainType.Bill,
                            new Dictionary<string, object>(), formPara =>
                            {
                                formPara.Status = Enu_BillStatus.Modify;
                            });
                        this.OperationContext.Result.HtmlActions.Add(action);
                    }
                }
                else
                {
                    var notHasPurOrderApplyDys = e.DataEntitys.Where(x=>!purchaseOrderIdSet.Any(y=>y.EqualsIgnoreCase(Convert.ToString(x["id"])))).ToArray();
                    if (notHasPurOrderApplyDys != null && notHasPurOrderApplyDys.Any())
                    {
                        PushPurchaseOrderApplyChg(notHasPurOrderApplyDys);
                    }
                }
            }
            //说明是没有驳回状态的采购变更申请单，那么就直接下推采购变更申请单
            else
            {
                PushPurchaseOrderApplyChg(e.DataEntitys);
            }
            
        }
        
        /// <summary>
        /// 下推采购订单变更申请单
        /// </summary>
        /// <param name="dataEntities"></param>
        public void PushPurchaseOrderApplyChg(DynamicObject [] dataEntities)
        {
            BillConvertContext billCvtCtx = null;
            var ruleId = "ydj_purchaseorder2ydj_purchaseorderapply_chg";
            var sourceFormId = "ydj_purchaseorder";
            var targetFormId = "ydj_purchaseorderapply_chg";
            var selRows = new List<SelectedRow>();

            selRows.AddRange(dataEntities.Select(x =>
            {
                var sel = new SelectedRow()
                {
                    PkValue = Convert.ToString(x["id"]),
                    BillNo = Convert.ToString(x["fbillno"]),
                    EntityKey = "fentity"
                };
                return sel;
            }));

            billCvtCtx = new BillConvertContext()
            {
                RuleId = ruleId,
                SourceFormId = sourceFormId,
                TargetFormId = targetFormId,
                SelectedRows = selRows.ToConvertSelectedRows(),
                Option = OperateOption.Create()
            };

            var convertService = this.Container.GetService<IConvertService>();

            var result = convertService.Push(this.Context, billCvtCtx);

            var convertResult = result.SrvData as ConvertResult;

            var targetDataObjects = convertResult.TargetDataObjects.ToList();

            foreach (var targetDataObject in targetDataObjects)
            {
                //弹出弹窗
                var action = this.OperationContext.UserContext.ShowSpecialForm(convertResult.HtmlForm,
                    targetDataObject,
                    false,
                    this.OperationContext.PageId,
                    Enu_OpenStyle.Modal,
                    Enu_DomainType.Bill,
                    new Dictionary<string, object>(), formPara =>
                    {
                        formPara.Status = Enu_BillStatus.Push;
                    });
                this.OperationContext.Result.HtmlActions.Add(action);
            }
        }

        
    }
}