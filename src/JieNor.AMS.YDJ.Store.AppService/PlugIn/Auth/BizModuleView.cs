using JieNor.Framework;
using JieNor.Framework.DataEntity.MainFw;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Auth
{
    /// <summary>
    /// 经销商用户，屏蔽部分菜单
    /// </summary>
    [InjectService]
    public class BizModuleView : IBizModuleView
    {
        //要屏蔽的业务对象菜单
        static List<string> notVisibleMenu = new List<string>()
        {
            "sel_category",
            "sel_component",
            "sel_constraint",
            "sel_fittingsmap",
            "sel_priceformula",
            "sel_prop",
            "sel_propvalue",
            "sel_range",
            "sel_selectionform",
            "sel_suite",
            "sel_suitemap",
            "sel_type",
            "sel_standardprop",
            "bas_organization",
            "ydj_regional",
            "ydj_city",
            "ms_markingassistant",
            //"bas_agent",
            //"bas_deliver",
        };


        public IEnumerable<BizModuleItem> FilterBizModule(UserContext ctx, IEnumerable<Framework.DataEntity.MainFw.BizModuleItem> bizMdlItem)
        {
            if (ctx.IsTopOrg)//总部用户，不需要屏蔽
            {
                return bizMdlItem;
            }
            this.FilterAgentIsKeyAccount(ctx);
            List<BizModuleItem> ret = new List<BizModuleItem>();
            foreach (var mdl in bizMdlItem)
            {
                var m = mdl.ToJson().FromJson<BizModuleItem>();
                m.MenuGroups = new List<MenuGroupItem>();
                foreach (var grp in mdl.MenuGroups)
                {
                    var g = grp.ToJson().FromJson<MenuGroupItem>();
                    g.MenuItems = new List<MenuEntryItem>();
                    foreach (var menu in grp.MenuItems)
                    {
                        if (!notVisibleMenu.Any(f => f.EqualsIgnoreCase(menu.BillFormId)))
                        {
                            g.MenuItems.Add(menu);
                        }
                    }
                    if (g.MenuItems.Count > 0)
                    {
                        m.MenuGroups.Add(g);
                    }
                }
                if (m.MenuGroups.Count > 0)
                {
                    ret.Add(m);
                }
            }

            return ret;
        }


        public void ClearMenuCache(UserContext ctx)
        {
            return;
        }

        public bool FilterMenu(UserContext userCtx, JObject menu, Dictionary<string, object> parameters)
        {
            return false;
        }


        public void FilterOuterParamsMenu(UserContext ctx, IEnumerable<JObject> menu, string formId)
        {
            return;
        }

        /// <summary>
        /// 当经销商.【大客户渠道】=是，任意用户登录该组织时，均无法查看以下表单：
        ///①综合价目表
        ///②采购价目表
        /// </summary>
        /// <param name="ctx"></param>
        public void FilterAgentIsKeyAccount(UserContext ctx)
        {
            notVisibleMenu = notVisibleMenu.Where(p => p != "ydj_purchaseprice" && p != "rpt_pricesynthesize").ToList();
            var sqlParams = new List<SqlParam>();
            //sqlParams.Add(new SqlParam("@fid", DbType.String, "1196817774359478273"));
            sqlParams.Add(new SqlParam("@fid", DbType.String, ctx.BizOrgId));
            var sql = $@"/*dialect*/ 
SELECT
	fcustomchannel 
FROM
	t_bas_agent age
	JOIN t_bas_organization org ON org.fid = @fid
WHERE
	age.fnumber = org.fnumber";
            var agents = ctx.ExecuteDynamicObject(sql, sqlParams);
            if (agents.Count > 0 && Convert.ToString(agents.First()?["fcustomchannel"]) == "1")
            {
                notVisibleMenu.Add("ydj_purchaseprice");
                notVisibleMenu.Add("rpt_pricesynthesize");
            }
        }
    }
}
