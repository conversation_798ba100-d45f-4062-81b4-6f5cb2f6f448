using JieNor.AMS.YDJ.Core;
using JieNor.AMS.YDJ.Core.DataEntity;
using JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Series
{
    [InjectService]
    [FormId("ydj_series")]
    [OperationNo("Save")]
    public class Save : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            //监测慕思品牌字段是否改变，
            var billSnapshotObjs = this.Option.GetBillSaveSnapshot();
            List<string> sql = new List<string>();

            var brandids = e.DataEntitys.Select(x => Convert.ToString(x["fbrandid"]));
            var brandObjs = this.Context.LoadBizBillHeadDataByACLFilter("ydj_brand", $"fid in ('{brandids.JoinEx("','", false)}')", "fid,fnumber,fmusibrand").ToList();
            foreach (var item in e.DataEntitys)
            {
                var brandObj = brandObjs.Where(o => o["fid"].ToString().EqualsIgnoreCase(Convert.ToString(item["fbrandid"])))?.FirstOrDefault();
                if (brandObj != null)
                {
                    item["fmusiseries"] = brandObj["fmusibrand"];
                }

                var fid = item["id"] as string;
                var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(fid));
                if (existSnapshot != null)
                {
                    if (!(item["fmusiseries"] as string).EqualsIgnoreCase(existSnapshot["fmusiseries"] as string))
                    {
                        //联动修改关联系列的商品（慕思商品标记）
                        sql.Add($@"/*dialect*/update t_bd_material set fismusiproduct='{item["fmusiseries"] as string}',fmodifydate=GETDATE() where fseriesid='{fid}'");
                    }
                }
            }
            if (sql != null && sql.Count > 0)
            {
                var dbSvc = this.Container.GetService<IDBServiceEx>();
                dbSvc.ExecuteBatch(this.Context, sql);
            }

            ChangWriteLog(e.DataEntitys);
        }

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Length == 0)
            {
                return;
            }

            // 判断是否存在总部数据
            if (!e.DataEntitys.Any(s => Convert.ToString(s["fmainorgid"]).EqualsIgnoreCase(this.Context.TopCompanyId)))
            {
                return;
            }

            //清除缓存
            ProductDataIsolateHelper.ClearCacheByBiz(this.Context, new PrdDataIsolateChannelMessage
            {
                Message = $"{this.HtmlForm?.Caption}-{this.OperationNo}",
                TopCompanyId = this.Context.TopCompanyId
            });
        }


        /// <summary>
        /// 系列修改记录日志
        /// 操作日志-操作描述记录是否慕思系列原值和新值。
        /// </summary>
        /// <param name="dataEntities"></param>
        private void ChangWriteLog(DynamicObject[] dataEntities)
        {
            var billSnapshotObjs = this.Option.GetBillSaveSnapshot();
            foreach (var item in dataEntities)
            {
                var existSnapshot = billSnapshotObjs.FirstOrDefault(o => (o["id"] as string).EqualsIgnoreCase(item["id"] as string));
                if (existSnapshot != null)
                {
                    if (Convert.ToString(existSnapshot["fmusiseries"]) != Convert.ToString(item["fmusiseries"]))
                    {
                        this.Logger.WriteLog(this.Context, new LogEntry
                        {
                            BillIds = item["id"] as string,
                            BillNos = item["fnumber"] as string,
                            BillFormId = this.HtmlForm.Id,
                            OpName = "系列保存",
                            OpCode = this.OperationNo,
                            Content = "执行了【保存】操作，【慕思系列】发生变化，原值：{0}；新值：{1}。".Fmt(GetName(existSnapshot["fmusiseries"]), GetName(item["fmusiseries"])),
                            DebugData = "执行了【保存】操作，【慕思系列】发生变化，原值：{0}；新值：{1}。".Fmt(GetName(existSnapshot["fmusiseries"]), GetName(item["fmusiseries"])),
                            Category = (long)(Enu_LogCategory.BizOp | Enu_LogCategory.BizOp_Complete),
                            Level = Enu_LogLevel.Info.ToString(),
                            LogType = Enu_LogType.RecordType_03
                        });
                    }
                }
            }
        }

        private string GetName(object id)
        {
            var name = string.Empty;
            switch (id)
            {
                case "0":
                    name = "否";
                    break;
                case "1":
                    name = "是";
                    break;
                default:
                    break;
            }
            return name;
        }
    }
}
