using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.MetaCore.Validator;
using System.Text.RegularExpressions;
using JieNor.Framework;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BASService.Case
{
    [InjectService]
    [FormId("ydj_case")]
    [OperationNo("save")]
    public class Save : AbstractOperationServicePlugIn
    {
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            e.Rules.Add(this.RuleFor("fbillhead", (billObj) => billObj["fcaseentry"] as DynamicObjectCollection)
                .IsTrue((billObj, imageObjs) =>
                {
                    return imageObjs?.Any() == true;
                }).WithMessage("不允许保存，至少要上传一张明细图片！"));

            const string sRegex = @"^(((file|gopher|news|nntp|telnet|http|ftp|https|ftps|sftp)://)|(www\.))+(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(/[a-zA-Z0-9\&%_\./-~-]*)?$";
            //验证vr地址是否是合法的地址
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                var vrlink =Convert.ToString(newData["fvrlink"]);
                if (vrlink.IsNullOrEmptyOrWhiteSpace()) return true;//为空则不验证是否合法
                Regex myrx = new Regex(sRegex);
                Match match = myrx.Match(vrlink);
                return match.Success;
            }).WithMessage("填写的VR地址不合法"));

            //检查案例说明是否超过200
            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (Convert.ToString(newData["fremark"]).Length > 200)
                {
                    return false;
                }
                return true;
            }).WithMessage("案例说明长度不能超过200！"));
        }

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null) return;
            foreach(var dataEntity in e.DataEntitys)
            {
                var firstImageObj = (dataEntity["fcaseentry"] as DynamicObjectCollection)?.FirstOrDefault();
                if (firstImageObj != null)
                {
                    dataEntity["fimageid"] = firstImageObj["ffileid"];
                }
            }
        }
    }
}
