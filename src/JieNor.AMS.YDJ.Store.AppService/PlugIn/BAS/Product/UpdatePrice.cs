using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.AMS.YDJ.DataTransferObject.ProductPrice;
using JieNor.AMS.YDJ.Core.Interface;
using Newtonsoft.Json.Linq;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 商品：更新商品价格
    /// </summary>
    [InjectService]
    [FormId("ydj_product")]
    [OperationNo("UpdatePrice")]
    public class UpdatePrice : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            var priceService = this.Container.GetService<IPriceService>();
            var dbServiceEx = this.Container.GetService<IDBServiceEx>();

            bool updateAll = e.DataEntitys == null || e.DataEntitys.Length == 0;
            if (updateAll)
            {
                // 更新全部

                // 一次取100条记录处理
                string preProductId = string.Empty;
                int limit = 100;
                int totalCount = GetTotalCount();
                int times = Convert.ToInt32(Math.Ceiling(1.0 * totalCount / limit));

                for (int i = 0; i < times; i++)
                {
                    string sql =
                        $"select top {limit} fid,fdefattrinfo from T_BD_MATERIAL where fmainorgid='{this.Context.Company}' and fforbidstatus<>'1' and fispresetprop='1' ";

                    if (!preProductId.IsNullOrEmptyOrWhiteSpace())
                    {
                        sql += $" and fid>'{preProductId}' ";
                    }

                    sql += " order by fid ";

                    var products = this.DBService.ExecuteDynamicObject(this.Context, sql).Select(s => new ProductItem
                    {
                        ProductId = Convert.ToString(s["fid"]),
                        AuxPropValId = Convert.ToString(s["fdefattrinfo"])
                    }).ToList();

                    UpdateLocalPrices(dbServiceEx, priceService, products);

                    preProductId = products.LastOrDefault()?.ProductId;
                }

                this.Result.IsSuccess = true;
                this.Result.SimpleMessage = "商品价格更新成功！";
                this.OperationContext.AddRefreshPageAction();
            }
            else
            {
                // 批量更新
                var products = e.DataEntitys.Where(s => Convert.ToBoolean(s["fispresetprop"])).Select(s => new ProductItem
                {
                    ProductId = Convert.ToString(s["id"]),
                    AuxPropValId = Convert.ToString(s["fdefattrinfo"])
                }).ToList();

                UpdateLocalPrices(dbServiceEx, priceService, products);

                this.Result.IsSuccess = true;
                this.OperationContext.AddRefreshPageAction();
            }
        }

        private int GetTotalCount()
        {
            int totalCount = 0;

            string sql = $"select COUNT(1) as total_count from T_BD_MATERIAL where fmainorgid='{this.Context.Company}' and fforbidstatus<>'1' and fispresetprop='1' ";

            using (var reader = this.DBService.ExecuteReader(this.Context, sql))
            {
                if (reader.Read())
                {
                    totalCount = Convert.ToInt32(reader[0]);
                }
            }

            return totalCount;
        }


        /// <summary>
        /// 批量更新商品本地价目
        /// </summary>
        /// <param name="dbServiceEx">数据库扩展接口</param>
        /// <param name="priceService">取价服务</param>
        /// <param name="products">商品信息</param> 
        /// <returns>商品本地价目列表</returns>
        public void UpdateLocalPrices(IDBServiceEx dbServiceEx, IPriceService priceService, List<ProductItem> products)
        {
            if (products == null || products.Count == 0) return;

            JArray productInfos = new JArray();
            foreach (var product in products)
            {
                productInfos.Add(JToken.FromObject(new
                {
                    clientId = product.ProductId,
                    productId = product.ProductId,
                    bizDate = DateTime.Now,
                    length = 0,
                    width = 0,
                    thick = 0,
                    attrInfo = new
                    {
                        id = product.AuxPropValId
                    }
                }));
            }

            var prices = priceService.GetPrice(this.Context, 1, productInfos);

            List<string> sqls = new List<string>();
            foreach (var price in prices)
            {
                if ((bool)price["success"])
                {
                    string productId = (string)price["clientId"];

                    decimal salPrice = (decimal)price["salPrice"];
                    decimal guidePrice = (decimal)price["guidePrice"];

                    sqls.Add($"update T_BD_MATERIAL set fmodifydate=GETDATE(), fmodifierid='{this.Context.UserId}', fsalprice={salPrice}, fguideprice={guidePrice} where fid='{productId}'");
                }
                else
                {
                    string productId = (string)price["clientId"];

                    sqls.Add($"update T_BD_MATERIAL set fmodifydate=GETDATE(), fmodifierid='{this.Context.UserId}', fsalprice=0, fguideprice=0 where fid='{productId}'");
                }
            }

            if (sqls.Any())
            {
                dbServiceEx.ExecuteBatch(this.Context, sqls);
            }
        }

        public class ProductItem
        {
            /// <summary>
            /// 商品id
            /// </summary>
            public string ProductId { get; set; }

            /// <summary>
            /// 辅助属性值id
            /// </summary>
            public string AuxPropValId { get; set; }
        }

    }
}