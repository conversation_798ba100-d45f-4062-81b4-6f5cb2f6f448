using JieNor.AMS.YDJ.Store.AppService.Plugin.Channel;
using JieNor.AMS.YDJ.WeiXin.AppService.Weixin.MP.AdvancedAPIs;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.BAS.Product
{
    /// <summary>
    /// 
    /// </summary>
    public class Validation_Save : AbstractBaseValidation
    {
        /// <summary>
        /// 校验器作用实体
        /// </summary>
        public override string EntityKey
        {
            get
            {
                return "fbillhead";
            }
            set
            {
                base.EntityKey = value;
            }
        }

        public bool IsImportant { get; set; }
        public Validation_Save(bool isImportant)
        {
            IsImportant = isImportant;
        }

        public override ValidationResult Validate(UserContext userCtx, HtmlForm formInfo, DynamicObject[] dataEntities, OperateOption option, string operationNo)
        {
            ValidationResult result = new ValidationResult();
            if (IsImportant)
            {
                var profileService = this.Context.Container.GetService<ISystemProfile>();
                var matbarcoderulecheck = Convert.ToInt32(profileService.GetSystemParameter<string>(this.Context, "stk_stockparam", "fmatbarcoderulecheck"));//参数【商品条码包装规则维护校验】
                if (matbarcoderulecheck == 0) return result;
                foreach (var item in dataEntities)
                {
                    var fpackagtype = Convert.ToString(item["fpackagtype"]);
                    if (string.IsNullOrWhiteSpace(fpackagtype) || fpackagtype.Equals("0"))
                    {
                        if (matbarcoderulecheck == 2)
                        {
                            result.Errors.Add(new ValidationResultEntry()
                            {
                                ErrorMessage = "当前商品未维护【打包类型】，不允许保存，请核查！",
                                DataEntity = item,
                            });
                        }
                    }
                }
            }
            return result;
        }
    }
}
