//using JieNor.AMS.YDJ.Core.Interface;
//using JieNor.AMS.YDJ.DataTransferObject.Enums;
//using JieNor.AMS.YDJ.DataTransferObject.Poco;
//using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order.Common;
//using JieNor.Framework;
//using JieNor.Framework.CustomException;
//using JieNor.Framework.DataTransferObject;
//using JieNor.Framework.DataTransferObject.Poco;
//using JieNor.Framework.Enums;
//using JieNor.Framework.Interface;
//using JieNor.Framework.Interface.CloudChain;
//using JieNor.Framework.IoC;
//using JieNor.Framework.MetaCore.FormMeta;
//using JieNor.Framework.MetaCore.FormOp;
//using JieNor.Framework.MetaCore.FormOp.FormService;
//using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
//using JieNor.Framework.MetaCore.Validator;
//using JieNor.Framework.SuperOrm.DataEntity;
//using JieNor.Framework.SuperOrm.DataManager;
//using Newtonsoft.Json.Linq;
//using System;
//using System.Collections.Generic;
//using System.Linq;

//namespace JieNor.AMS.YDJ.Store.AppService.Plugin.IncomeDisburse
//{
//    /// <summary>
//    /// 收支记录：确认
//    /// </summary>
//    [InjectService]
//    [FormId("coo_incomedisburse")]
//    [OperationNo("Confirm")]
//    public class Confirm : AbstractOperationServicePlugIn
//    {
//        /// <summary>
//        /// 预处理校验规则
//        /// </summary>
//        /// <param name="e"></param>
//        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
//        {
//            base.PrepareValidationRules(e);

//            var sysProfile = this.Container.GetService<ISystemProfile>();

//            /*
//                定义表头校验规则
//            */
//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                var incomeConfirmAudit = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fincomeconfirmaudit", false);
//                if (incomeConfirmAudit && !Convert.ToString(newData["fstatus"]).EqualsIgnoreCase("E"))
//                {
//                    return false;
//                }
//                return true;

//            }).WithMessage("收支记录未审核，无法确认！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (Convert.ToString(newData["foperationmode"]).EqualsIgnoreCase("1"))
//                {
//                    return true;
//                }
//                var purpose = newData["fpurpose"] as string;
//                if (Convert.ToBoolean(newData["fissyn"])
//                    && Convert.ToString(newData["fcreatecompanyid"]).EqualsIgnoreCase(this.Context.Company)
//                    && !purpose.EqualsIgnoreCase("bizpurpose_07"))
//                {
//                    //由总部供应商发起的充值收支记录，总部自己可以确认
//                    if (purpose.EqualsIgnoreCase("bizpurpose_01"))
//                    {
//                        var cooCompanyId = newData["fcoocompanyid"] as string;
//                        return this.CheckIsSupplierCreate(cooCompanyId);
//                    }
//                    return false;
//                }
//                return true;

//            }).WithMessage("该操作是由协同接收方执行，您无法执行该操作！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (!Convert.ToString(newData["fbizstatus"]).EqualsIgnoreCase("bizstatus_01"))
//                {
//                    return false;
//                }
//                return true;

//            }).WithMessage("状态不是【待确认】，不允许确认！"));

//            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
//            {
//                if (Convert.ToString(newData["fway"]).EqualsIgnoreCase("payway_06")
//                    && newData["fmybankid"].IsNullOrEmptyOrWhiteSpace())
//                {
//                    return false;
//                }
//                return true;
//            }).WithMessage("我方银行字段为必填项，请先填写我方银行保存后再执行确认操作！"));
//        }

//        /// <summary>
//        /// 检查收支记录是否由总部供应商发起
//        /// </summary>
//        /// <param name="cooCompanyId"></param>
//        /// <returns></returns>
//        private bool CheckIsSupplierCreate(string cooCompanyId)
//        {
//            var cooForm = this.MetaModelService.LoadFormModel(this.Context, "coo_company");
//            var dm = this.GetDataManager();
//            dm.InitDbContext(this.Context, cooForm.GetDynamicObjectType(this.Context));
//            var where = "fmainorgid=@fmainorgid and fcompanyid=@fcompanyid and fmycompanyid=@fmainorgid";
//            var paramList = new List<SqlParam>
//            {
//                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
//                new SqlParam("@fcompanyid", System.Data.DbType.String, cooCompanyId)
//            };
//            var reader = this.Context.GetPkIdDataReader(cooForm, where, paramList);
//            var cooCompany = dm.SelectBy(reader)?.OfType<DynamicObject>()?.FirstOrDefault();
//            if (cooCompany != null
//                 && Convert.ToString(cooCompany["fservicetype"]).EqualsIgnoreCase("客户")
//                 && Convert.ToString(cooCompany["fcoostatus"]).EqualsIgnoreCase("已协同"))
//            {
//                return true;
//            }
//            return false;
//        }

//        //public override void PrepareBusinessServices(PrepareBusinessServiceEventArgs e)
//        //{
//        //    base.PrepareBusinessServices(e);

//        //    var formServiceLoader = this.Container.GetService<IFormServiceLoader>();
//        //    var syncServiceInst = formServiceLoader.CreateK3SyncService(this.Context, this.OperationContext.HtmlForm, Enu_SyncTimePoint.SyncWhenAudit, "save", this.OperationContext.Option);
//        //    if (syncServiceInst != null)
//        //    {
//        //        e.Services.AddRange(syncServiceInst);
//        //    }
//        //}

//        /// <summary>
//        /// 调用操作事物前触发的事件
//        /// </summary>
//        /// <param name="e"></param>
//        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
//        {
//            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
//            {
//                if (this.Result.ComplexMessage.ErrorMessages.Count <= 0)
//                {
//                    this.Result.ComplexMessage.WarningMessages.Add("请选择一行或多行数据！");
//                    this.Result.IsSuccess = false;
//                }
//                return;
//            }

//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));

//            var syncService = this.Container.GetService<ISynergyService>();

//            foreach (var dataEntity in e.DataEntitys)
//            {
//                var isSyn = Convert.ToBoolean(dataEntity["fissyn"]);
//                var sceneType = dataEntity["fpurpose"] as string;

//                //初始化协同时，不检查余额控制
//                if (Convert.ToString(dataEntity["foperationmode"]).EqualsIgnoreCase("1") == false
//                    && sceneType.EqualsIgnoreCase("bizpurpose_07") == false)
//                {
//                    //检查协同账户余额
//                    this.CheckSynAccountBalance(dataEntity);
//                }

//                //1.反写本地订单数据
//                //2.更新对方订单数据
//                //3.更新对方收支记录

//                this.UpdateOrderInfo(dataEntity);

//                //协同账户余额服务
//                var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();

//                //更新收支记录
//                dataEntity["fbizstatus"] = "bizstatus_02";
//                dataEntity["fbalance"] = synAccountBalanceService.GetAccountBalance(this.Context, dataEntity);
//                dataEntity["fmodifierid"] = this.Context.UserId;
//                dataEntity["fmodifydate"] = DateTime.Now;
//                dataEntity["fconfirmorid"] = this.Context.UserId;
//                dataEntity["fconfirmdate"] = DateTime.Now;
//                dm.Save(dataEntity);

//                //更新账户余额
//                synAccountBalanceService.UpdateAccountBalance(this.Context, dataEntity);

//                if (isSyn)
//                {
//                    if (sceneType.EqualsIgnoreCase("bizpurpose_07") == false)
//                    {
//                        this.SendSynUpdateOrder(dataEntity);
//                    }

//                    //在对应的业务单据上面也需要记录一笔动态
//                    var linkBillId = dataEntity["fsourceid"] as string;
//                    var linkFormId = dataEntity["fsourceformid"] as string;
//                    if (!linkBillId.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        var linkForm = this.MetaModelService.LoadFormModel(this.Context, linkFormId);
//                        if (linkForm != null)
//                        {
//                            syncService.WriteLog(this.Context, linkForm, linkBillId, "setttlefirm", "结算确认");
//                        }
//                    }
//                }
//            }
//            // 下推K3 收款单
//            this.Pushk3Receivable(e.DataEntitys);

//            //this.AddRefreshPageAction();

//            new OrderCommon(this.Context).UpdateTransferOrder(e.DataEntitys);

//            //刷新父页面的父页面
//            var formParameter = this?.ParentPageSession?.FormParameter as FormParameter;
//            var ppPageId = formParameter?.ParentPageId;
//            if (!ppPageId.IsNullOrEmptyOrWhiteSpace())
//            {
//                this.AddRefreshPageAction(ppPageId);
//            }

//            this.Result.IsSuccess = true;
//        }

//        /// <summary>
//        /// 下推K3 应收单
//        /// 判断销售参数（销售合同结算时不做资金协同）勾选 ，是直营门店，销售合同审核
//        /// 发送数据包前往k3 
//        /// K3返回信息
//        /// </summary>
//        /// <returns></returns>
//        private void Pushk3Receivable(DynamicObject[] DataEntitys)
//        {
//            //效验 参数（销售合同结算时不做资金协同） 默认不勾引选 false 做 true 不做
//            var coordination = CheckOrderCoordination();
//            if (coordination) { return; }
//            foreach (var dataEntity in DataEntitys)
//            {
//                Dictionary<string, string> simpleData = new Dictionary<string, string>();
//                //参数勾选 && 源单类型 = 销售合同 && 运营模式 = 总部直营
//                if (!coordination && Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order") && Convert.ToString(dataEntity["foperationmode"]).EqualsIgnoreCase("1") == true)
//                {
//                    var sourcenumber = Convert.ToString(dataEntity["fsourcenumber"]);//合同编号
//                    if (sourcenumber.IsNullOrEmptyOrWhiteSpace()) { throw new BusinessException($"源单编号不能为空！"); }
//                    var strSql = @"select top 1 ftranid,fstatus from t_ydj_order where fbillno= @fbillno and fstatus ='E'and fmainorgid =@fmainorgid";
//                    var paramList = new List<SqlParam>
//                     {
//                         new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
//                         new SqlParam("@fbillno", System.Data.DbType.String, sourcenumber)
//                     };
//                    var dbService = this.Context.Container.GetService<IDBService>();
//                    var sqlData = dbService.ExecuteDynamicObject(this.Context, strSql, paramList);
//                    if (!sqlData.Any()) { return; }
//                    var orderTranid = string.Empty;
//                    var orderStatus = string.Empty;
//                    foreach (var item in sqlData)
//                    {
//                        orderTranid = Convert.ToString(item["ftranid"]);//交易流水
//                        orderStatus = Convert.ToString(item["fstatus"]);//状态
//                    }
//                    if (!orderStatus.EqualsIgnoreCase("E")) { throw new BusinessException($"销售合同{sourcenumber}未审核,请前往销售管理参数取消【销售合同结算时不做资金协同】！"); }

//                    //需要传输的数据包
//                    simpleData = new Dictionary<string, string>
//                                    {
//                                        { "forderno", sourcenumber },//销售合同编号
//                                        { "orderTranid", orderTranid },//销售合同交易流水
//                                        { "fway", Convert.ToString(dataEntity?["fway"])},//支付方式,暂时接收方为:现金
//                                        { "famount", Convert.ToString(dataEntity?["famount"])},//结算金额 
//                                        { "ftranid", Convert.ToString(dataEntity?["ftranid"])}, //交易流水
//                                        { "fmybankid", Convert.ToString(dataEntity?["fsynbankid"])}, //对方银行-k3来说是我方银行
//                                    };
//                    //{ "opcode", opcode} //操作：bizpurpose_02 订单付款->下推收款单  bizpurpose_06 退款->下推收款退款单
//                    switch (Convert.ToString(dataEntity?["fpurpose"]))
//                    {
//                        case "bizpurpose_02"://订单付款
//                            simpleData.Add("opcode", "OrderPayment");//订单付款->下推收款单 
//                            this.SynK3Push(dataEntity, simpleData);//协同K3销售订单下推收款单
//                            break;
//                        case "bizpurpose_06"://退款
//                            simpleData.Add("opcode", "Refund");//退款->下推收款退款单
//                            this.SynK3Push(dataEntity, simpleData); //协同K3收款单下推收款退款单
//                            break;
//                    }
//                }
//            }
//        }

//        /// <summary>
//        ///  效验 参数（销售合同结算时不做资金协同） false 做 true 不做
//        /// </summary>
//        /// <returns></returns>
//        private bool CheckOrderCoordination()
//        {
//            //销售合同结算时不做资金协同 默认是false
//            var profileService = this.Container.GetService<ISystemProfile>();
//            string stockParamJson = profileService.GetProfile(this.Context, "fw", "bas_storesysparam_parameter");
//            JObject stockParam = null;
//            if (!string.IsNullOrWhiteSpace(stockParamJson))
//            {
//                stockParam = JObject.Parse(stockParamJson);
//            }
//            var nocoordination = false;
//            var property = stockParam?.Property("fnocapitalcoordination");
//            if (property != null)
//            {
//                nocoordination = (bool)property.Value;
//            }
//            return nocoordination;
//        }

//        /// <summary>
//        ///  协同K3下推
//        ///  公用一个接口，根据传递的数据包里面的opcode执行
//        /// </summary>
//        private void SynK3Push(DynamicObject dataEntity, Dictionary<string, string> simpleData)
//        {
//            TargetSEP target = new TargetSEP(Convert.ToString(dataEntity["fcoocompanyid"]), Convert.ToString(dataEntity["fcooproductid"]));
//            //数据发送时采用异步消息模式发送，消息中指定回调类型
//            var responseResult = this.Gateway.Invoke(
//                            this.Context,
//                            target,
//                            new CommonBillDTO()
//                            {
//                                FormId = "ydj_order",
//                                OperationNo = "pushreceipt",//找到k3 对应的销售意向单，->销售订单下推收款单
//                                BillData = "",
//                                ExecInAsync = false,
//                                AsyncMode = (int)Enu_AsyncMode.Background,
//                                SimpleData = simpleData
//                            }.SetOptionFlag((long)Enu_OpFlags.TPSRequest)
//                        ) as CommonBillDTOResponse;
//            var invokeResult = responseResult?.OperationResult;
//            if (invokeResult != null && !invokeResult.IsSuccess)
//            {
//                var errorMsgs = invokeResult.ComplexMessage.ErrorMessages;
//                var msgs = invokeResult.SimpleMessage;
//                if (errorMsgs?.Count > 0 || !msgs.IsNullOrEmptyOrWhiteSpace())
//                {
//                    this.Result.ComplexMessage.ErrorMessages.AddRange(errorMsgs);
//                    throw new BusinessException("同步数据失败！协同系统信息：" + msgs);
//                }
//            }
//            invokeResult?.ThrowIfHasError(true, $"同步数据失败，对方系统未返回任何响应！");
//        }

//        /// <summary>
//        /// 获取数据包
//        /// </summary>
//        /// <param name="incomeDisburses"></param>
//        /// <returns></returns>
//        private string GetIncomeDisburseBillDatas(List<DynamicObject> incomeDisburses)
//        {
//            List<Dictionary<string, object>> billDatas = new List<Dictionary<string, object>>();

//            foreach (var incomeDisburse in incomeDisburses)
//            {
//                Dictionary<string, object> billData = new Dictionary<string, object>();
//                billData["ftranid"] = incomeDisburse["ftranid"];
//                billData["fdate"] = incomeDisburse["fdate"];
//                billData["fway"] = incomeDisburse["fway"];
//                billData["faccount"] = incomeDisburse["faccount"];

//                billDatas.Add(billData);
//            }
//            return billDatas.ToJson();
//        }


//        /// <summary>
//        /// 事务后
//        /// </summary>
//        /// <param name="e"></param>
//        public override void EndOperationTransaction(EndOperationTransactionArgs e)
//        {
//            if (e.DataEntitys == null || e.DataEntitys.Length <= 0)
//            {
//                return;
//            }

//            //foreach (var dataEntity in e.DataEntitys)
//            //{
//            //    if (Convert.ToString(dataEntity["fsourceformid"]).EqualsIgnoreCase("ydj_order"))
//            //    {
//            //        string sourceNumber = Convert.ToString(dataEntity["fsourcenumber"]);
//            //        if (sourceNumber.IsNullOrEmptyOrWhiteSpace()) continue;

//            //        var order = this.Context.LoadBizDataByNo("ydj_order", $"fbillno", new[] { sourceNumber }).FirstOrDefault();

//            //        //// 当源单编号 源单类型 = 销售合同的时候， 查询销售合同的【总支付金额】等于【整单金额】自动审核《销售合同》
//            //        //SubmitAndAuditBizData(order);

//            //    }
//            //} 
//        }

//        ///// <summary>
//        ///// 确认收款=整单金额 自动审核销售合同
//        ///// </summary>
//        //private void SubmitAndAuditBizData(DynamicObject order)
//        //{
//        //    if (order == null) return;

//        //    decimal freceivable = Convert.ToDecimal(order["freceivable"]);
//        //    decimal fsumamount = Convert.ToDecimal(order["fsumamount"]);

//        //    if (freceivable == fsumamount)
//        //    {
//        //        order["fstatus"] = BillStatus.E.ToString();
//        //        var htmlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");
//        //        var dm = this.Container.GetService<IDataManager>();
//        //        dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
//        //        dm.Save(order);
//        //    }

//        //    //if (!sourcenumber.IsNullOrEmptyOrWhiteSpace())
//        //    //{
//        //    //    var orderReqSub = this.Context.LoadBizDataByFilter("ydj_order", $"fbillno= '{sourcenumber}'and freceivable = fsumamount").FirstOrDefault();
//        //    //    if (!orderReqSub.IsNullOrEmptyOrWhiteSpace())
//        //    //    {
//        //    //        orderReqSub["fstatus"] = BillStatus.E.ToString();
//        //    //        var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_order");
//        //    //        var dm = this.Container.GetService<IDataManager>();
//        //    //        dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
//        //    //        dm.Save(orderReqSub);
//        //    //    }
//        //    //}
//        //}

//        /// <summary>
//        /// 检查协同账户余额
//        /// </summary>
//        /// <param name="dataEntity"></param>
//        private void CheckSynAccountBalance(DynamicObject dataEntity)
//        {
//            //账户为空的收支记录不参与余额增减逻辑，因为这类收支记录是由“线下支付”入口生成的，不存在账户的概念
//            var account = Convert.ToString(dataEntity["faccount"]);
//            if (account.IsNullOrEmptyOrWhiteSpace()) return;

//            //如果金额方向不是“支出”，则无需检查余额
//            if (!Convert.ToString(dataEntity["fdirection"]).EqualsIgnoreCase("direction_02")) return;

//            //金额
//            var amount = Convert.ToDecimal(dataEntity["famount"]);

//            var accountSynService = this.Container.GetService<ISynAccountBalanceService>();

//            //如果是客户
//            var supplierId = Convert.ToString(dataEntity["fsupplierid"]);
//            var customerId = Convert.ToString(dataEntity["fcustomerid"]);
//            if (!customerId.IsNullOrEmptyOrWhiteSpace())
//            {
//                #region 如果是客户，那么直接取客户协同账户配置
//                IEnumerable<AccountInfo> lstAllAccount = accountSynService.GetAllAccountByCustomerId(this.Context, customerId);
//                var accountInfo = lstAllAccount.LastOrDefault(t => t.AccountId.EqualsIgnoreCase(account));
//                if (accountInfo == null) return;
//                if (amount > accountInfo.Balance)
//                {
//                    if (accountInfo.CreditLimit > 0 && Math.Abs(accountInfo.Balance - amount) > accountInfo.CreditLimit)
//                    {
//                        throw new BusinessException($"{accountInfo.AccountName}账户信用额度不足，无法完成确认，当前信用额度为：{accountInfo.CreditLimit.ToString("f2")}！");
//                    }
//                    throw new BusinessException($"{accountInfo.AccountName}账户余额不足，无法完成确认，当前余额为：{accountInfo.Balance.ToString("f2")}！");
//                }
//                #endregion
//            }

//            //如果是供应商
//            else if (!supplierId.IsNullOrEmptyOrWhiteSpace())
//            {
//                //是否是协同收支记录
//                var isSyn = Convert.ToBoolean(dataEntity["fissyn"]);
//                if (isSyn)
//                {
//                    #region 如果是供应商，则需要跨站获取销售方的协同账户配置
//                    var cooCompanyId = dataEntity["fcoocompanyid"] as string;
//                    var cooProductId = dataEntity["fcooproductid"] as string;

//                    //同步发送：向销售方获取协同账户设置信息
//                    var responseResult = this.Gateway.Invoke(
//                        this.Context,
//                        new TargetSEP(cooCompanyId, cooProductId),
//                        new CommonBillDTO()
//                        {
//                            FormId = "ydj_customer",
//                            OperationNo = "PurGetSynAccount",
//                            BillData = "",
//                            ExecInAsync = false,
//                            SimpleData = new Dictionary<string, string>
//                            {
//                                  { "cooCompanyId", cooCompanyId }
//                            }
//                        }) as CommonBillDTOResponse;
//                    responseResult?.OperationResult?.ThrowIfHasError(true, $"获取销售方的账户设置信息失败！");

//                    List<AccountInfo> lstAllAccount = null;
//                    var srvData = responseResult?.OperationResult?.SrvData as string;
//                    if (!srvData.IsNullOrEmptyOrWhiteSpace())
//                    {
//                        lstAllAccount = srvData.FromJson<List<AccountInfo>>();
//                    }
//                    if (lstAllAccount == null) throw new BusinessException("获取销售方的账户设置信息失败！");
//                    var accountInfo = lstAllAccount.LastOrDefault(t => t.AccountId.EqualsIgnoreCase(account));
//                    if (accountInfo == null) return;
//                    if (amount > accountInfo.Balance)
//                    {
//                        if (accountInfo.CreditLimit > 0 && Math.Abs(accountInfo.Balance - amount) > accountInfo.CreditLimit)
//                        {
//                            throw new BusinessException($"{accountInfo.AccountName}账户信用额度不足，无法完成确认，当前信用额度为：{accountInfo.CreditLimit.ToString("f2")}！");
//                        }
//                        throw new BusinessException($"{accountInfo.AccountName}账户余额不足，无法完成确认，当前余额为：{accountInfo.Balance.ToString("f2")}！");
//                    }
//                    #endregion
//                }
//                else
//                {
//                    #region 如果不是协同，那么直接取供应商账户信息
//                    IEnumerable<AccountInfo> lstAllAccount = accountSynService.GetAllAccountBySupplierId(this.Context, supplierId);
//                    var accountInfo = lstAllAccount.LastOrDefault(t => t.AccountId.EqualsIgnoreCase(account));
//                    if (accountInfo == null) return;
//                    if (amount > accountInfo.Balance)
//                    {
//                        throw new BusinessException($"{accountInfo.AccountName}账户余额不足，无法完成确认，当前余额为：{accountInfo.Balance.ToString("f2")}！");
//                    }
//                    #endregion
//                }
//            }
//            else
//            {
//                if (Convert.ToString(dataEntity["foperationmode"]).EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()) == false)
//                {
//                    throw new BusinessException("供应商Id 和 客户Id 为空，请检查！");
//                }
//            }
//        }

//        /// <summary>
//        /// 检查指定企业与我是否已建立协同
//        /// </summary>
//        /// <param name="coocompanyId"></param>
//        /// <returns></returns>
//        private bool CheckCompanyIsSynergy(string coocompanyId)
//        {
//            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "coo_company");
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

//            string where = $"fmycompanyid=@fmycompanyid and fcompanyid=@fcompanyid and fcoostatus=N'已协同'";
//            var sqlParam = new SqlParam[]
//            {
//                new SqlParam("fmycompanyid", System.Data.DbType.String, this.Context.Company),
//                new SqlParam("fcompanyid", System.Data.DbType.String, coocompanyId)
//            };
//            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
//            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
//            if (dataEntity == null)
//            {
//                return false;
//            }
//            return true;
//        }

//        /// <summary>
//        /// 发送协同更新请求
//        /// 1.更新对方订单数据。
//        /// 2.更新对方收支记录。
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        private void SendSynUpdateOrder(DynamicObject incomeDisburse)
//        {
//            //协同企业ID
//            var cooCompanyId = incomeDisburse["fcoocompanyid"] as string;
//            var cooProductId = incomeDisburse["fcooproductid"] as string;
//            var incomeDisburses = new List<DynamicObject>
//            {
//                incomeDisburse
//            };
//            var synAccountBalanceService = this.Container.GetService<ISynAccountBalanceService>();
//            var chainDataSyncService = this.Container.GetService<IChainDataSyncService>();
//            var target = new TargetSEP(cooCompanyId, cooProductId);
//            var chainDataJson = chainDataSyncService.PublishDataToChainAndPack(
//                        this.Context, this.HtmlForm, incomeDisburses, target, new List<string> { "fcustomerid", "fway", "fdeptid" });
//            var billDatas = synAccountBalanceService.GetIncomeDisburseBillDatas(this.Context, incomeDisburses);

//            //数据发送时采用异步消息模式发送，消息中指定回调类型
//            var responseResult = this.Gateway.Invoke(
//                this.Context,
//                target,
//                new CommonBillDTO()
//                {
//                    FormId = "coo_incomedisburse",
//                    OperationNo = "ConfirmSynergy",
//                    BillData = "",
//                    ExecInAsync = false,
//                    AsyncMode = (int)Enu_AsyncMode.Background,
//                    SimpleData = new Dictionary<string, string>
//                    {
//                        { "tranId", incomeDisburse["ftranid"] as string },
//                        { "chainDataJson",chainDataJson},
//                        { "billDatas",billDatas}
//                    }
//                }) as CommonBillDTOResponse;
//            responseResult?.OperationResult?.ThrowIfHasError(true, $"协同收支记录确认失败，对方系统未返回任何响应！");
//        }

//        /// <summary>
//        /// 反写本地订单数据
//        /// </summary>
//        /// <param name="incomeDisburse"></param>
//        private void UpdateOrderInfo(DynamicObject incomeDisburse)
//        {
//            //如果源单不是以下单据，则无需反写源单数据
//            if (!Convert.ToString(incomeDisburse["fsourceformid"]).EqualsIgnoreCase("ydj_purchaseorder")
//                && !Convert.ToString(incomeDisburse["fsourceformid"]).EqualsIgnoreCase("ydj_saleintention")
//                && !Convert.ToString(incomeDisburse["fsourceformid"]).EqualsIgnoreCase("ydj_order")
//                && !Convert.ToString(incomeDisburse["fsourceformid"]).EqualsIgnoreCase("ydj_merchantorder")) return;

//            var fsourceid = incomeDisburse["fsourceid"] as string;
//            var fsourceformid = incomeDisburse["fsourceformid"] as string;
//            var famount = Convert.ToDecimal(incomeDisburse["famount"]);
//            var foldtranid = Convert.ToString(incomeDisburse["foldtranid"]);
//            //账户收支方向
//            var direction = Convert.ToString(incomeDisburse["fdirection"]).Trim().ToLower();
//            //用途
//            var purpose = Convert.ToString(incomeDisburse["fpurpose"]).Trim().ToLower();
//            //是否协同
//            var isSyn = Convert.ToBoolean(incomeDisburse["fissyn"]);
//            //运营模式
//            var operationMode = Convert.ToString(incomeDisburse["foperationmode"]).Trim();

//            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, fsourceformid);
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

//            var customerService = this.Container.GetService<ICustomerService>();

//            var sourceOrder = dm.Select(fsourceid) as DynamicObject;
//            if (sourceOrder != null)
//            {
//                // 计算本次确认收款/退款的金额
//                decimal confirmAmount = 0,
//                    prevReceivable = 0,         // 更新前的确认已收
//                    nextReceivable = 0;         // 更新后的确认已收

//                switch (fsourceformid.Trim().ToLower())
//                {
//                    case "ydj_purchaseorder":
//                        #region 采购订单

//                        if (isSyn)
//                        {
//                            //用途是“订单付款”
//                            if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                            {
//                                //累加已结算金额
//                                sourceOrder["fpaidamount"] = Convert.ToDecimal(sourceOrder["fpaidamount"]) + famount;
//                                //扣减待确认金额
//                                sourceOrder["fconfirmamount"] = Convert.ToDecimal(sourceOrder["fconfirmamount"]) - famount;
//                            }
//                            //用途是“红冲”
//                            else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                            {
//                                //扣减已结算金额
//                                sourceOrder["fpaidamount"] = Convert.ToDecimal(sourceOrder["fpaidamount"]) - famount;
//                                //增加待结算金额
//                                sourceOrder["fpayamount"] = Convert.ToDecimal(sourceOrder["fpayamount"]) + famount;
//                            }
//                            //用途是“退款”
//                            else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//                            {
//                                throw new BusinessException("采购订单的收支记录目前系统并不支持退款操作!");
//                            }

//                            //订单金额
//                            var purbillamount = Convert.ToDecimal(sourceOrder["ffbillamount"]);
//                            //已结算金额
//                            var fpaidamount = Convert.ToDecimal(sourceOrder["fpaidamount"]);
//                            //结算状态
//                            if (fpaidamount == 0)
//                            {
//                                //全款未付
//                                sourceOrder["fpaystatus"] = "paystatus_type_01";
//                            }
//                            else if (fpaidamount < purbillamount)
//                            {
//                                //部分付款
//                                sourceOrder["fpaystatus"] = "paystatus_type_02";
//                            }
//                            else if (fpaidamount == purbillamount)
//                            {
//                                //全款已付
//                                sourceOrder["fpaystatus"] = "paystatus_type_03";
//                            }
//                        }
//                        else
//                        {
//                            //用途是“订单付款”
//                            if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                            {
//                                //累加已结算金额
//                                sourceOrder["fpaidamount"] = Convert.ToDecimal(sourceOrder["fpaidamount"]) + famount;
//                                //扣减待确认金额
//                                sourceOrder["fconfirmamount"] = Convert.ToDecimal(sourceOrder["fconfirmamount"]) - famount;
//                            }
//                            //用途是“红冲”
//                            else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                            {
//                                throw new BusinessException("非协同的收支记录不可以红冲操作！");
//                            }
//                            //用途是“退款”
//                            else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//                            {
//                                if (famount > Convert.ToDecimal(sourceOrder["fpaidamount"]))
//                                {
//                                    throw new BusinessException($"退款金额不能大于已结算金额！");
//                                }
//                                //扣减已结算金额
//                                sourceOrder["fpaidamount"] = Convert.ToDecimal(sourceOrder["fpaidamount"]) - famount;
//                                //退款用途的收支记录在已确认时，累计增加到“实退金额”字段
//                                sourceOrder["factrefundamount"] = Convert.ToDecimal(sourceOrder["factrefundamount"]) + famount;
//                            }
//                        }
//                        #endregion
//                        break;

//                    case "ydj_saleintention":

//                        prevReceivable = Convert.ToDecimal(sourceOrder["fconfirmedamount"]);

//                        #region 销售意向单
//                        if (isSyn)
//                        {
//                            if (operationMode.EqualsIgnoreCase(((int)Enu_OperateMode.HQDirect).ToString()))
//                            {
//                                throw new BusinessException("目前系统不支持直营模式的销售意向单的收支记录协同!");
//                            }
//                            #region 购销协同模式（科凡）
//                            //用途是“订单付款”
//                            if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                            {
//                                //增加已结算金额
//                                sourceOrder["freceivedamount"] = Convert.ToDecimal(sourceOrder["freceivedamount"]) + famount;
//                                //扣减待确认金额
//                                sourceOrder["fconfirmamount"] = Convert.ToDecimal(sourceOrder["fconfirmamount"]) - famount;
//                            }
//                            //用途是“红冲”
//                            else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                            {
//                                switch (direction)
//                                {
//                                    case "direction_01":
//                                        //扣减已结算金额
//                                        sourceOrder["freceivedamount"] = Convert.ToDecimal(sourceOrder["freceivedamount"]) - famount;
//                                        //增加待结算金额
//                                        sourceOrder["freceiptamount"] = Convert.ToDecimal(sourceOrder["freceiptamount"]) + famount;
//                                        break;
//                                    case "direction_02":
//                                        //增加已结算金额
//                                        sourceOrder["freceivedamount"] = Convert.ToDecimal(sourceOrder["freceivedamount"]) + famount;
//                                        //扣减待结算金额
//                                        sourceOrder["freceiptamount"] = Convert.ToDecimal(sourceOrder["freceiptamount"]) - famount;
//                                        break;
//                                    default:
//                                        break;
//                                }
//                                //判断当前收支记录的原收支记录是否为退款，如果是，减少源单的实退金额字段
//                                var orginobj = this.Context.LoadBizDataByFilter("coo_incomedisburse", "ftranid = '{0}'".Fmt(foldtranid)).FirstOrDefault();
//                                if (!orginobj.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(orginobj["fpurpose"]).EqualsIgnoreCase("bizpurpose_06"))
//                                {
//                                    sourceOrder["factrefundamount"] = Convert.ToDecimal(sourceOrder["factrefundamount"]) - famount;
//                                }
//                            }
//                            //用途是“退款”
//                            else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//                            {
//                                //退款用途的收支记录在已确认时，累计增加到“实退金额”字段
//                                sourceOrder["factrefundamount"] = Convert.ToDecimal(sourceOrder["factrefundamount"]) + famount;
//                            }
//                            //订单金额
//                            var salbillamount = Convert.ToDecimal(sourceOrder["ffbillamount"]);
//                            //已结算金额
//                            var freceivedamount = Convert.ToDecimal(sourceOrder["freceivedamount"]);
//                            //结算状态
//                            if (freceivedamount == 0)
//                            {
//                                //全款未收
//                                sourceOrder["freceiptstatus"] = "receiptstatus_type_01";
//                            }
//                            else if (freceivedamount < salbillamount)
//                            {
//                                //部分收款
//                                sourceOrder["freceiptstatus"] = "receiptstatus_type_02";
//                            }
//                            else if (freceivedamount == salbillamount)
//                            {
//                                //全款已收
//                                sourceOrder["freceiptstatus"] = "receiptstatus_type_03";
//                            }
//                            #endregion
//                        }
//                        else
//                        {
//                            //非协同（敬天、优丽文）
//                            //检查是否已经下推销售合同
//                            this.CheckIsPushOrder(incomeDisburse["fsourcenumber"] as string);

//                            //用途是“订单付款”
//                            if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                            {
//                                var sysProfile = this.Container.GetService<ISystemProfile>();
//                                var enableCollectAmount = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fenablecollectamount", false);
//                                //如果未启用销售意向定金启用应收控制,允许超额收款
//                                if (enableCollectAmount && (famount > Convert.ToDecimal(sourceOrder["fcollectamount"]) - Convert.ToDecimal(sourceOrder["fconfirmedamount"])))
//                                {
//                                    throw new BusinessException($"（本次收定额 + 确认已收定金）不允许大于应收定金！");
//                                }
//                                //增加确认已收
//                                sourceOrder["fconfirmedamount"] = Convert.ToDecimal(sourceOrder["fconfirmedamount"]) + famount;
//                            }
//                            //用途是“红冲”
//                            else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                            {
//                                throw new BusinessException("非协同的收支记录不可以红冲操作！");
//                            }
//                            //用途是“退款”
//                            else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//                            {
//                                if (famount > Convert.ToDecimal(sourceOrder["fcollectedamount"]))
//                                {
//                                    throw new BusinessException($"退款金额不能大于已收定金！");
//                                }
//                                //扣减确认已收
//                                sourceOrder["fconfirmedamount"] = Convert.ToDecimal(sourceOrder["fconfirmedamount"]) - famount;
//                                //退款用途的收支记录在已确认时，累计增加到“实退金额”字段
//                                sourceOrder["factrefundamount"] = Convert.ToDecimal(sourceOrder["factrefundamount"]) + famount;
//                            }
//                            if (Convert.ToDecimal(sourceOrder["fconfirmedamount"]) < 0)
//                            {
//                                throw new BusinessException($"确认已收不允许为负数！");
//                            }
//                        }
//                        #endregion

//                        nextReceivable = Convert.ToDecimal(sourceOrder["fconfirmedamount"]);
//                        confirmAmount = nextReceivable - prevReceivable;
//                        customerService.CalculateAvailableIntegralAndSumAmount(this.Context, Convert.ToString(sourceOrder["fcustomerid"]), confirmAmount);

//                        break;

//                    case "ydj_order":

//                        prevReceivable = Convert.ToDecimal(sourceOrder["freceivable"]);

//                        //反写销售合同
//                        this.UpdateSalOrderInfo(sourceOrder, incomeDisburse, foldtranid, direction, purpose, isSyn);

//                        nextReceivable = Convert.ToDecimal(sourceOrder["freceivable"]);
//                        confirmAmount = nextReceivable - prevReceivable;
//                        customerService.CalculateAvailableIntegralAndSumAmount(this.Context, Convert.ToString(sourceOrder["fcustomerid"]), confirmAmount);

//                        break;

//                    case "ydj_merchantorder":
//                        #region 商户订单
//                        //已结算金额
//                        var fsettleamount = Convert.ToDecimal(sourceOrder["fsettleamount"]);
//                        //订单金额
//                        var fexpectamount = Convert.ToDecimal(sourceOrder["fexpectamount"]);

//                        //用途是“订单付款”
//                        if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//                        {
//                            if (famount + fsettleamount > fexpectamount)
//                            {
//                                throw new BusinessException($"收款金额加已结算金额之和不能大于订单金额！");
//                            }
//                            //增加已结算金额
//                            fsettleamount += famount;
//                        }
//                        //用途是“红冲”
//                        else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//                        {
//                            switch (direction)
//                            {
//                                case "direction_01":
//                                    //扣减已结算金额
//                                    fsettleamount -= famount;
//                                    break;
//                                case "direction_02":
//                                    //增加已结算金额
//                                    fsettleamount += famount;
//                                    break;
//                                default:
//                                    break;
//                            }
//                        }
//                        //用途是“退款”
//                        else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//                        {
//                            if (famount > fsettleamount)
//                            {
//                                throw new BusinessException($"退款金额不能大于已收金额！");
//                            }
//                            //扣减已结算金额
//                            fsettleamount -= famount;
//                        }
//                        if (fsettleamount < 0)
//                        {
//                            throw new BusinessException($"已收金额不允许为负数！");
//                        }
//                        //结算状态
//                        if (fsettleamount == 0)
//                        {
//                            //未结算
//                            sourceOrder["fsettlestatus"] = "settle_status01";
//                        }
//                        else if (fsettleamount < fexpectamount)
//                        {
//                            //部分结算
//                            sourceOrder["fsettlestatus"] = "settle_status02_01";
//                        }
//                        else if (fsettleamount >= fexpectamount)
//                        {
//                            //全款结算
//                            sourceOrder["fsettlestatus"] = "settle_status02";
//                        }
//                        sourceOrder["fsettleamount"] = fsettleamount;
//                        #endregion
//                        break;

//                    default:
//                        break;
//                }

//                dm.Save(sourceOrder);
//            }

//            //处理销售意向单和销售合同收款、退款和红冲确认时，对加盟商自动充值和扣款确认
//            dealInnerCustomerAccountInfo(sourceOrder, incomeDisburse, htmlForm);

//        }

//        /// <summary>
//        /// 反写销售合同
//        /// </summary>
//        /// <param name="sourceOrder"></param>
//        /// <param name="incomeDisburse">收支记录</param>
//        /// <param name="foldtranid"></param>
//        /// <param name="direction"></param>
//        /// <param name="purpose"></param>
//        /// <param name="isSyn"></param>
//        private void UpdateSalOrderInfo(DynamicObject sourceOrder, DynamicObject incomeDisburse, string foldtranid, string direction, string purpose, bool isSyn)
//        {
//            decimal famount = Convert.ToDecimal(incomeDisburse["famount"]);

//            //获取是否可以超额收款参数
//            var profileService = this.Container.GetService<ISystemProfile>();
//            string stockParamJson = profileService.GetProfile(this.Context, "fw", "bas_storesysparam_parameter");
//            JObject stockParam = null;
//            if (!string.IsNullOrWhiteSpace(stockParamJson))
//            {
//                stockParam = JObject.Parse(stockParamJson);
//            }
//            var fcanexcess = false;
//            var property = stockParam?.Property("fcanexcess");
//            if (property != null)
//            {
//                fcanexcess = (bool)property.Value;
//            }

//            //订单总额
//            var fsumamount = Convert.ToDecimal(sourceOrder["fsumamount"]);

//            //用途是“订单付款”
//            if (purpose.EqualsIgnoreCase("bizpurpose_02"))
//            {
//                //增加确认已收金额
//                sourceOrder["freceivable"] = checkReceivable(famount, sourceOrder, fcanexcess, fsumamount);
//                //从收款待确认金额减去现在确认的金额
//                sourceOrder["freceivabletobeconfirmed"] = Convert.ToDecimal(sourceOrder["freceivabletobeconfirmed"]) - famount;
//                if (Convert.ToDecimal(sourceOrder["freceivabletobeconfirmed"]) < 0)
//                {
//                    sourceOrder["freceivabletobeconfirmed"] = 0;
//                }
//            }
//            //用途是“红冲”
//            else if (purpose.EqualsIgnoreCase("bizpurpose_04"))
//            {
//                if (false == isSyn)
//                {
//                    throw new BusinessException("非协同的收支记录不可以红冲操作！");
//                }

//                decimal freducedbrokerage = Convert.ToDecimal(incomeDisburse["freducedbrokerage"]);
//                switch (direction)
//                {
//                    case "direction_01":
//                        //扣减已收金额
//                        sourceOrder["freceivable"] = Convert.ToDecimal(sourceOrder["freceivable"]) - famount;
//                        //扣减收款金额
//                        sourceOrder["fsumreceivable"] = Convert.ToDecimal(sourceOrder["fsumreceivable"]) - famount;
//                        //扣减已扣佣金
//                        sourceOrder["freducedbrokerage"] = Convert.ToDecimal(sourceOrder["freducedbrokerage"]) - freducedbrokerage;
//                        break;
//                    case "direction_02":
//                        //增加已收金额
//                        sourceOrder["freceivable"] = Convert.ToDecimal(sourceOrder["freceivable"]) + famount;
//                        //增加收款金额
//                        sourceOrder["fsumreceivable"] = Convert.ToDecimal(sourceOrder["fsumreceivable"]) + famount;
//                        //增加已扣佣金
//                        sourceOrder["freducedbrokerage"] = Convert.ToDecimal(sourceOrder["freducedbrokerage"]) + freducedbrokerage;
//                        break;
//                    default:
//                        break;
//                }
//                //判断当前收支记录的原收支记录是否为退款，如果是，减少源单的实退金额字段
//                var orginobj = this.Context.LoadBizDataByFilter("coo_incomedisburse", "ftranid = '{0}'".Fmt(foldtranid)).FirstOrDefault();
//                if (!orginobj.IsNullOrEmptyOrWhiteSpace() && Convert.ToString(orginobj["fpurpose"]).EqualsIgnoreCase("bizpurpose_06"))
//                {
//                    sourceOrder["factrefundamount"] = Convert.ToDecimal(sourceOrder["factrefundamount"]) - famount;
//                }
//            }
//            //用途是“退款”
//            else if (purpose.EqualsIgnoreCase("bizpurpose_06"))
//            {
//                if (famount > Convert.ToDecimal(sourceOrder["freceivable"]))
//                {
//                    throw new BusinessException($"退款金额不能大于已收金额！");
//                }
//                //扣减已收金额
//                sourceOrder["freceivable"] = Convert.ToDecimal(sourceOrder["freceivable"]) - famount;
//                //退款用途的收支记录在已确认时，累计增加到“实退金额”字段
//                sourceOrder["factrefundamount"] = Convert.ToDecimal(sourceOrder["factrefundamount"]) + famount;

//            }

//            if (Convert.ToDecimal(sourceOrder["freceivable"]) < 0)
//            {
//                throw new BusinessException($"已收金额不允许为负数！");
//            }

//            #region 旧逻辑
//            //计算未收款
//            //(1) 勾选，退款成功未收款是需要加上退款金额，[未收款] = 订单金额 - 确认收款。则确认收款 = 300，收款待确认 = 0，未收款 = 700，实退金额 = 300
//            // (2) 不勾选，退款成功未收款是不加上退款金额，[未收款] = 订单金额 - 确认收款 - 实退金额。则确认收款 = 300，收款待确认 = 0，未收款 = 400，实退金额 = 300
//            // var dontReflect = Convert.ToBoolean(stockParam?["fdontreflect"] ?? false); //销售合同实退不体现未收
//            // if (dontReflect)
//            // {
//            //     //反写未收，未收 = 订单总额 - 确认收款
//            //     sourceOrder["funreceived"] = Convert.ToDecimal(sourceOrder["fsumamount"]) - Convert.ToDecimal(sourceOrder["freceivable"]);
//            // }
//            // else
//            // {
//            //     //[未收款] = 订单金额 - 确认收款 - 实退金额
//            //     sourceOrder["funreceived"] = Convert.ToDecimal(sourceOrder["fsumamount"]) -Convert.ToDecimal(sourceOrder["freceivable"]) - Convert.ToDecimal(sourceOrder["factrefundamount"]);
//            // }

//            //未收金额=订单总额-确认已收-申请退货金额
//            //sourceOrder["funreceived"] = fsumamount - freceivable - Convert.ToDecimal(sourceOrder["frefundamount"]);

//            ////销售合同实退不体现未收(指的是：退货金额不体现未收)
//            //var dontReflect = Convert.ToBoolean(stockParam?["fdontreflect"] ?? false);
//            //if (dontReflect)
//            //{
//            //    //反写未收，未收 = 订单总额 - 确认已收-申请退货金额
//            //    sourceOrder["funreceived"] = Convert.ToDecimal(sourceOrder["fsumamount"]) - Convert.ToDecimal(sourceOrder["freceivable"]) - Convert.ToDecimal(sourceOrder["frefundamount"]);
//            //}
//            //else
//            //{
//            //    //反写未收，未收 = 订单总额 - 确认已收
//            //    sourceOrder["funreceived"] = Convert.ToDecimal(sourceOrder["fsumamount"]) - Convert.ToDecimal(sourceOrder["freceivable"]);
//            //}

//            #endregion

//            var orderService = this.Container.GetService<IOrderService>();

//            orderService.CalculateUnreceived(this.Context, new[] { sourceOrder });
//            orderService.CalculateReceiptStatus(this.Context, new[] { sourceOrder });

//            ////确认已收金额
//            //var freceivable = Convert.ToDecimal(sourceOrder["freceivable"]);
//            ////结算状态
//            //if (freceivable == 0)
//            //{
//            //    //全款未收
//            //    sourceOrder["freceiptstatus"] = "receiptstatus_type_01";
//            //}
//            //else if (freceivable < fsumamount)
//            //{
//            //    //部分收款
//            //    sourceOrder["freceiptstatus"] = "receiptstatus_type_02";
//            //}
//            //else if (freceivable >= fsumamount)
//            //{
//            //    //全款已收
//            //    sourceOrder["freceiptstatus"] = "receiptstatus_type_03";
//            //}
//        }

//        /// <summary>
//        /// 检查销售合同确认已收是否超额收款
//        /// </summary>
//        /// <param name="famount"></param>
//        /// <param name="sourceOrder"></param>
//        /// <param name="fcanexcess"></param>
//        /// <param name="fsumamount"></param>
//        /// <returns></returns>
//        private static decimal checkReceivable(decimal famount, DynamicObject sourceOrder, bool fcanexcess, decimal fsumamount)
//        {
//            var oldReceivable = Convert.ToDecimal(sourceOrder["freceivable"]);
//            var newReceivable = oldReceivable + famount;

//            if (!fcanexcess && newReceivable > fsumamount)
//            {
//                throw new BusinessException($"系统不允许超额收款！销售合同[{sourceOrder["fbillno"]}]的确认已收[{oldReceivable}]加上[{famount}]已超过订单金额[{fsumamount}]");
//            }

//            return newReceivable;
//        }

//        /// <summary>
//        /// 处理销售意向单和销售合同收款、退款和红冲确认时，对加盟商自动充值和扣款确认
//        /// </summary>
//        /// <param name="dataEntity"></param>
//        /// <param name="htmlForm"></param>
//        private void dealInnerCustomerAccountInfo(DynamicObject dataEntity, DynamicObject incomeDisburse, HtmlForm htmlForm)
//        {
//            if (dataEntity == null || incomeDisburse == null || htmlForm == null || htmlForm.Id != "ydj_saleintention" && htmlForm.Id != "ydj_order")
//            {
//                return;
//            }

//            string innerCustomerId = Convert.ToString(dataEntity["finnercustomerid"]);
//            if (string.IsNullOrWhiteSpace(innerCustomerId))
//            {
//                return;
//            }

//            //用途
//            var purpose = Convert.ToString(incomeDisburse["fpurpose"]).Trim().ToLower();
//            //账户收支方向
//            var direction = Convert.ToString(incomeDisburse["fdirection"]).Trim().ToLower();

//            var fway = string.Empty;
//            var operationNo = string.Empty;
//            var operationName = string.Empty;
//            var operationFormId = string.Empty;

//            switch (purpose)
//            {
//                //用途是“订单付款”
//                case "bizpurpose_02":
//                    operationName = "充值";
//                    fway = "payway_10";
//                    operationNo = "Recharge";
//                    operationFormId = "coo_inpourdialog";
//                    break;
//                //用途是“红冲”
//                case "bizpurpose_04":
//                    switch (direction)
//                    {
//                        case "direction_01":
//                            operationName = "扣款";
//                            fway = string.Empty;
//                            operationNo = "Charge";
//                            operationFormId = "coo_chargedialog";
//                            break;
//                        case "direction_02":
//                            operationName = "充值";
//                            fway = "payway_10";
//                            operationNo = "Recharge";
//                            operationFormId = "coo_inpourdialog";
//                            break;
//                    }
//                    break;
//                //用途是“退款”
//                case "bizpurpose_06":
//                    operationName = "扣款";
//                    fway = string.Empty;
//                    operationNo = "Charge";
//                    operationFormId = "coo_chargedialog";
//                    break;
//            }

//            var metaModelService = this.Container.GetService<IMetaModelService>();
//            var inpForm = metaModelService.LoadFormModel(this.Context, operationFormId);
//            var inpEntity = inpForm.GetDynamicObjectType(this.Context).CreateInstance() as DynamicObject;

//            inpEntity["fmoney"] = Convert.ToDecimal(incomeDisburse["famount"]);
//            inpEntity["fusagetype"] = "settleaccount_type_01";
//            inpEntity["fdate"] = DateTime.Now;
//            inpEntity["fdescription"] = string.Format("本记录是由{0}[{1}]相关的{2}[{3}]确认生成的{4}记录!",
//                                                       htmlForm.Caption,
//                                                       Convert.ToString(dataEntity[htmlForm.NumberFldKey]),
//                                                       this.HtmlForm.Caption,
//                                                       Convert.ToString(incomeDisburse[this.HtmlForm.NumberFldKey]),
//                                                       operationName);

//            if (false == string.IsNullOrWhiteSpace(fway))
//            {
//                inpEntity["fway"] = fway;
//            }

//            var result = this.Gateway.InvokeBillOperation(this.Context, inpForm.Id, new[] { inpEntity }, operationNo, new Dictionary<string, object>
//            {
//                { "fsourceid",innerCustomerId} ,
//                { "fsourceformid","ydj_customer"},
//                { "foldtranid", incomeDisburse["ftranid"]}
//            });
//            result?.ThrowIfHasError(true, $"加盟商自动{operationName}失败!");

//            var incomeDisburseIds = (result.SrvData as Dictionary<string, object>)?["incomeDisburseIds"] as string[];

//            if (incomeDisburseIds == null || incomeDisburseIds.Length <= 0)
//            {
//                throw new BusinessException($"加盟商自动{operationName}失败!没有获取到返回结果信息!");
//            }

//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
//            var incomeDisburses = dm.Select(incomeDisburseIds).OfType<DynamicObject>().ToList();

//            if (incomeDisburses == null || incomeDisburses.Count <= 0)
//            {
//                throw new BusinessException($"加盟商自动{operationName}失败!没有获取到相关的{this.HtmlForm.Caption}!");
//            }

//            //确认前是否需要审核，如果需要则自动审核
//            var sysProfile = this.Container.GetService<ISystemProfile>();
//            var incomeConfirmAudit = sysProfile.GetSystemParameter<bool>(this.Context, "bas_storesysparam", "fincomeconfirmaudit", false);
//            if (incomeConfirmAudit)
//            {
//                foreach (var item in incomeDisburses)
//                {
//                    //自动审核（直接改状态）
//                    item["fstatus"] = BillStatus.E.ToString();
//                }
//                dm.Save(incomeDisburses);
//            }

//            result = this.Gateway.InvokeBillOperation(this.Context, this.HtmlForm.Id, incomeDisburses, this.OperationNo, new Dictionary<string, object>());
//            result?.ThrowIfHasError(true, $"加盟商自动{operationName}确认失败!");
//        }

//        /// <summary>
//        /// 检查是否已经下推销售合同
//        /// </summary>
//        /// <param name="sourceNumber"></param>
//        private void CheckIsPushOrder(string sourceNumber)
//        {
//            var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, "ydj_order");
//            var dm = this.Container.GetService<IDataManager>();
//            dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));

//            string where = $@"fmainorgid=@fmainorgid and fsourcenumber=@fsourcenumber";
//            var sqlParam = new SqlParam[]
//            {
//                new SqlParam("fmainorgid", System.Data.DbType.String, this.Context.Company),
//                new SqlParam("fsourcenumber", System.Data.DbType.String, sourceNumber),
//            };
//            var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
//            var dataEntity = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
//            if (dataEntity != null)
//            {
//                throw new BusinessException($"销售意向【{sourceNumber}】已经下推销售合同，不允许再次收款和退款！");
//            }
//        }
//    }
//}