using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin
{
    /// <summary>
    /// 通用保存插件：
    /// 1、保存时如果商品图片字段为空，则自动从商品图库中取得图片信息填充。
    /// 2、如果商品图片不为空，则检查此图片是否已在商品图库中存在，若不存在，则自动创建商品图库记录（以备后续其它开单时复用）。
    /// </summary>
    [InjectService]
    [FormId("ydj_saleintention|ydj_order|pur_reqorder|ydj_purchaseorder|stk_scheduleapply|stk_postockin|pur_receiptnotice|pur_returnnotice|stk_postockreturn|sal_deliverynotice|stk_sostockout|sal_returnnotice|stk_sostockreturn|stk_reservebill|stk_inventorytransferreq|stk_inventorytransfer|stk_otherstockinreq|stk_otherstockin|stk_otherstockoutreq|stk_otherstockout|stk_inventoryverify|base_stkbilltmpl")]
    [OperationNo("save")]
    public class CommonSave : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物前触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            if (e.DataEntitys == null || e.DataEntitys.Length <= 0) return;

            var profileService = this.Container.GetService<ISystemProfile>();
            var fenablesyncgallery = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fenablesyncgallery", false);
            var fonlyproductimg = profileService.GetSystemParameter(this.Context, "bas_storesysparam", "fonlyproductimg", false);


            if (false == fenablesyncgallery || fonlyproductimg)
            {
                return;
            }

            //商品明细实体
            var entityKey = this.HtmlForm.Id.EqualsIgnoreCase("ydj_order") ? "fentry" : "fentity";
            var entity = this.HtmlForm.GetEntryEntity(entityKey);
            if (entity == null) return;

            //商品图片字段
            var mtrlImageField = this.HtmlForm.GetField("fmtrlimage") as HtmlMaterialImageField;
            if (mtrlImageField == null || !mtrlImageField.EntityKey.EqualsIgnoreCase(entity.Id)) return;

            //商品图片字段关联的商品字段
            var mtrlField = this.HtmlForm.GetField(mtrlImageField.ControlFieldKey) as HtmlBaseDataField;
            if (mtrlField == null) return;

            //商品字段关联的辅助属性字段
            var auxPropField = this.HtmlForm.GetFieldList()
                .FirstOrDefault(o => o is HtmlAuxPropertyField
                    && (o as HtmlAuxPropertyField).ControlFieldKey.EqualsIgnoreCase(mtrlField.Id));

            //定制说明字段
            var customDesField = this.HtmlForm.GetFieldList().FirstOrDefault(x => x.EntityKey.EqualsIgnoreCase(entity.Id) && 
                                                                                 (x.Id.EqualsIgnoreCase("fcustomdes_e") || 
                                                                                  x.Id.EqualsIgnoreCase("fcustomdesc")));

            //商品
            var mtrlForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_product");
            var mtrlDm = this.GetDataManager();
            mtrlDm.InitDbContext(this.Context, mtrlForm.GetDynamicObjectType(this.Context));

            //商品图库
            var mtrlImageForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_commoditygallery");
            var mtrlImageDm = this.GetDataManager();
            mtrlImageDm.InitDbContext(this.Context, mtrlImageForm.GetDynamicObjectType(this.Context));
            var where = "fmainorgid=@fmainorgid and fproductid=@fproductid and fattrinfo=@fattrinfo  and fcustomdesc=@fcustomdesc";
            List<DynamicObject> mtrlImageList = new List<DynamicObject>();

            foreach (var dataEntity in e.DataEntitys)
            {
                var entrys = entity.DynamicProperty.GetValue<DynamicObjectCollection>(dataEntity);
                foreach (var entry in entrys)
                {
                    //定制说明不为空时不保存商品图片到图库
                    //if (customDesField != null)
                    //{
                    //    var customDes = customDesField.DynamicProperty.GetValue<string>(entry);
                    //    if (false == string.IsNullOrWhiteSpace(customDes))
                    //    {
                    //        continue;
                    //    }
                    //}

                    var productId = mtrlField.DynamicProperty.GetValue<string>(entry) ?? "";
                    if (productId.IsNullOrEmptyOrWhiteSpace()) continue;
                    var auxPropId = auxPropField?.DynamicProperty.GetValue<string>(entry) ?? "";
                    var customerDesId = customDesField?.DynamicProperty.GetValue<string>(entry) ?? "";

                    //查询商品图库
                    var reader = this.Context.GetPkIdDataReader(mtrlImageForm, where, new List<SqlParam>
                    {
                        new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                        new SqlParam("@fproductid", System.Data.DbType.String, productId),
                          new SqlParam("@fcustomdesc", System.Data.DbType.String, customerDesId),
                        new SqlParam("@fattrinfo", System.Data.DbType.String, auxPropId)
                    });
                    var mtrlImage = mtrlImageDm.SelectBy(reader).OfType<DynamicObject>()?.FirstOrDefault();

                    var mtrlImageTxt = mtrlImageField.TxtDynamicProperty.GetValue<string>(entry) ?? "";
                    var mtrlImageId = mtrlImageField.DynamicProperty.GetValue<string>(entry) ?? "";
                    if (mtrlImageId.IsNullOrEmptyOrWhiteSpace())
                    {
                        if (mtrlImage != null)
                        {
                            //将图库中的商品图片填充到当前明细行中
                            var imageId = Convert.ToString(mtrlImage["fimage"]) ?? "";
                            var imageTxt = Convert.ToString(mtrlImage["fimage_txt"]) ?? "";
                            mtrlImageField.DynamicProperty.SetValue(entry, imageId);
                            mtrlImageField.TxtDynamicProperty.SetValue(entry, imageTxt);
                        }
                        else
                        {
                            //辅助属性为空时，并且在商品图库中找不到图库信息时，直接取商品主图
                            if (auxPropId.IsNullOrEmptyOrWhiteSpace())
                            {
                                var product = mtrlDm.Select(productId) as DynamicObject;
                                if (product != null)
                                {
                                    mtrlImageField.DynamicProperty.SetValue(entry, product["fimage"]);
                                    mtrlImageField.TxtDynamicProperty.SetValue(entry, ""); //商品主图是单图片字段，没有存图片文件名
                                }
                            }
                        }
                    }
                    else
                    {
                        if (mtrlImage == null)
                        {
                            //新增商品图库
                            mtrlImage = new DynamicObject(mtrlImageForm.GetDynamicObjectType(this.Context));
                            mtrlImage["fproductid"] = productId;
                            mtrlImage["fattrinfo"] = auxPropId;
                            mtrlImage["fcustomdesc"] = customerDesId;
                            mtrlImage["fimage"] = mtrlImageId;
                            mtrlImage["fimage_txt"] = mtrlImageTxt;
                        }
                        else
                        {
                            //补齐商品图库
                            var imageId = Convert.ToString(mtrlImage["fimage"]) ?? "";
                            var imageTxt = Convert.ToString(mtrlImage["fimage_txt"]) ?? "";
                            if (imageId.IsNullOrEmptyOrWhiteSpace())
                            {
                                mtrlImage["fimage"] = mtrlImageId;
                                mtrlImage["fimage_txt"] = mtrlImageTxt;
                            }
                            else
                            {
                                var imageIds = imageId.Split(',').ToList();
                                var imageTxts = imageTxt.Split(',').ToList();
                                var mtrlImageIds = mtrlImageId.Split(',').ToList();
                                var mtrlImageTxts = mtrlImageTxt.Split(',').ToList();
                                for (int i = 0; i < mtrlImageIds.Count; i++)
                                {
                                    if (!mtrlImageIds[i].IsNullOrEmptyOrWhiteSpace() && !imageIds.Contains(mtrlImageIds[i]))
                                    {
                                        imageIds.Add(mtrlImageIds[i]);
                                        if (i < mtrlImageTxts.Count)
                                        {
                                            imageTxts.Add(mtrlImageTxts[i]);
                                        }
                                    }
                                }
                                mtrlImage["fimage"] = string.Join(",", imageIds);
                                mtrlImage["fimage_txt"] = string.Join(",", imageTxts);
                            }
                        }
                        mtrlImageList.Add(mtrlImage);
                    }
                }
            }

            //保存商品图库
            if (mtrlImageList.Count > 0)
            {
                var preService = this.Container.GetService<IPrepareSaveDataService>();
                preService.PrepareDataEntity(this.Context, mtrlImageForm, mtrlImageList.ToArray(), this.Option);

                mtrlImageDm.Save(mtrlImageList);
            }
        }
    }
}