using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.CustomException;
using Tea;
using Tea.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Sys.SmsTemplate
{
    /// <summary>
    /// 短信模板：删除
    /// </summary>
    [InjectService]
    [FormId("sys_smstemplate")]
    [OperationNo("delete")]
    public class Delete : AbstractOperationServicePlugIn
    {
        ///// <summary>
        ///// 预处理校验规则
        ///// </summary>
        ///// <param name="e"></param>
        //public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        //{
        //    base.PrepareValidationRules(e);

        //    ///*
        //    //    定义表头校验规则
        //    //*/
        //    //e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
        //    //{
        //    //    if (!(Convert.ToString(newData["ffeedstatus"]).EqualsIgnoreCase("aft_service_01") && newData["fsourcecancl"].Equals("1")))
        //    //    {
        //    //        return false;
        //    //    }
        //    //    return true;
        //    //}).WithMessage("【{0}】删除失败，仅允许删除售后状态为【待处理】且来源渠道为【自建】的单据！", (billObj, propObj) => propObj["fbillno"]));
        //}

        public override void BeginOperationTransaction(BeginOperationTransactionArgs e)
        {
            base.BeginOperationTransaction(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;

            if (this.Context.Company != this.Context.TopCompanyId)
            {
                throw new BusinessException("您暂无该操作权限，请联系管理员统一配置短信模版！");
            }
            if (e.DataEntitys == null) return;

            AlibabaCloud.SDK.Dysmsapi20170525.Client client = AlibabaCloudUtil.CreateClient(this.Context);
            AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime = new AlibabaCloud.TeaUtil.Models.RuntimeOptions();

            var allObj = e.DataEntitys.ToList();
            List<DynamicObject> delObj = new List<DynamicObject>();

            for (int i = 0; i < e.DataEntitys.Length; i++)
            {
                var item = e.DataEntitys[i];
                if (!item["fcode"].IsNullOrEmptyOrWhiteSpace())
                {
                    if(!DeleteSmsTemplateRequest(item, client, runtime))
                    {
                        delObj.Add(item);
                    }
                }
            }

            this.Context.SaveBizData(this.HtmlForm.Id, e.DataEntitys);

            foreach (var item in delObj)
            {
                allObj.Remove(item);
            }
            e.DataEntitys = allObj?.ToArray();
        }

        /// <summary>
        /// 删除短信模板
        /// </summary>
        /// <param name="item"></param>
        /// <param name="client"></param>
        /// <param name="runtime"></param>
        private bool DeleteSmsTemplateRequest(DynamicObject item,
            AlibabaCloud.SDK.Dysmsapi20170525.Client client,
            AlibabaCloud.TeaUtil.Models.RuntimeOptions runtime)
        {
            var status = false;
            AlibabaCloud.SDK.Dysmsapi20170525.Models.DeleteSmsTemplateRequest deleteSmsTemplateRequest = new AlibabaCloud.SDK.Dysmsapi20170525.Models.DeleteSmsTemplateRequest();
            deleteSmsTemplateRequest.TemplateCode = Convert.ToString(item["fcode"]);
            try
            {
                // 复制代码运行请自行打印 API 的返回值
                var result = client.DeleteSmsTemplateWithOptions(deleteSmsTemplateRequest, runtime);

                if (result.Body.Code == "OK")
                {
                    status=true;
                    this.Result.ComplexMessage.SuccessMessages.Add($"名称为【{item["fname"]}】的模版删除成功！");
                }
                else
                {
                    item["freason"] = result.Body.Message;
                    this.Result.ComplexMessage.ErrorMessages.Add($"名称为【{item["fname"]}】的模版删除失败，请查看【送审备注】结合分析！");
                }
            }
            catch (TeaException error)
            {
                this.Result.ComplexMessage.ErrorMessages.Add($"名称为【{item["fname"]}】的模版删除失败，请查看【送审备注】结合分析！");
                item["freason"] = error.Message;
                //// 错误 message
                //Console.WriteLine(error.Message);
                //// 诊断地址
                //Console.WriteLine(error.Data["Recommend"]);
                //AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            catch (Exception _error)
            {
                this.Result.ComplexMessage.ErrorMessages.Add($"名称为【{item["fname"]}】的模版删除失败，请查看【送审备注】结合分析！");
                TeaException error = new TeaException(new Dictionary<string, object>
                    {
                        { "message", _error.Message }
                    });
                item["freason"] = error.Message;
                //// 错误 message
                //Console.WriteLine(error.Message);
                //// 诊断地址
                //Console.WriteLine(error.Data["Recommend"]);
                //AlibabaCloud.TeaUtil.Common.AssertAsString(error.Message);
            }
            return status;
        }
    }
}