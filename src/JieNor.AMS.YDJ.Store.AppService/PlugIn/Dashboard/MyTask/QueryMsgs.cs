using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Dashboard.MyTask
{
    /// <summary>
    /// 仪表盘：查询我的消息数（已读数/未读数）
    /// </summary>
    [InjectService]
    [FormId("dashboard_mytask")]
    [OperationNo("querymsgs")]
    public class QueryMsgs : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //先对表结构进行构建，以免后面执行sql语句报某个表不存在的错误
            var taskForm = this.MetaModelService.LoadFormModel(this.Context, "bf_task");
            var taskDm = this.GetDataManager();
            taskDm.InitDbContext(this.Context, taskForm.GetDynamicObjectType(this.Context));

            var sqlText = @"
            select 
            (
	            select count(1) from 
	            (
		            select (case tr.freadstatus when '2' then '已读' else '未读' end) as fstatus from t_bf_task tk 
		            left join t_bf_taskreadmsg tr on tk.fid=tr.fid 
                    where tk.fmainorgid=@fmainorgid and tk.fexcuter=@fexcuter
	            ) t where fstatus='已读'
            ) as freads, funreads from 
            (
	            select count(1) as funreads from 
	            (
		            select (case tr.freadstatus when '2' then '已读' else '未读' end) as fstatus from t_bf_task tk 
		            left join t_bf_taskreadmsg tr on tk.fid=tr.fid 
                    where tk.fmainorgid=@fmainorgid and tk.fexcuter=@fexcuter
	            ) t where fstatus='未读'
            ) t";

            var sqlParam = new List<SqlParam>
            {
                new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company),
                new SqlParam("@fexcuter", System.Data.DbType.String, this.Context.UserId)
            };

            int reads = 0, unReads = 0;
            using (var reader = this.DBService.ExecuteReader(this.Context, sqlText, sqlParam))
            {
                if (reader.Read())
                {
                    reads = reader.GetInt("freads");
                    unReads = reader.GetInt("funreads");
                }
            }

            this.Result.SrvData = new { reads = reads, unReads = unReads };
            this.Result.IsSuccess = true;
        }
    }
}