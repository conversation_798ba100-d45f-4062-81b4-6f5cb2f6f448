using JieNor.AMS.YDJ.DataTransferObject;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Service
{
    /// <summary>
    /// 套件辅助属性 数据修复
    /// </summary>
    [InjectService]
    [FormId("sys_mainfw")]
    [OperationNo("RepairSuitSelection")]
    public class RepairSuitSelection : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);
            var metaModelService = this.Context.Container.GetService<IMetaModelService>();
            var productForm = metaModelService.LoadFormModel(this.Context, "ydj_product");
            //查询需要处理合同、采购订单数据
            var NeedRepairDatas = this.LoadSuitSelectionNeedRepair();
            if (NeedRepairDatas == null || !NeedRepairDatas.Any())
            {
                this.Result.SimpleMessage="没有查询到需要修复的套件辅助属性数据，当前无需处理。";
                return;
            }

            foreach (var NeedRepairData in NeedRepairDatas) 
            {
                var RepairId = Convert.ToString(NeedRepairData["fid"]);
                var FormId = Convert.ToString(NeedRepairData["formid"]);
                var product = FormId == "ydj_order" ? "fproductid" : "fmaterialid";

                var htmlForm = this.MetaModelService?.LoadFormModel(this.Context, FormId);
                var dm = this.Container.GetService<IDataManager>();
                dm.InitDbContext(this.Context, htmlForm.GetDynamicObjectType(this.Context));
                string where = $"fid=@fid";
                var sqlParam = new SqlParam[]
                {
                    new SqlParam("fid", System.Data.DbType.String, RepairId)
                };
                var dataReader = this.Context.GetPkIdDataReader(htmlForm, where, sqlParam);
                var RepairObj = dm.SelectBy(dataReader).OfType<DynamicObject>().FirstOrDefault();
                if (RepairObj != null) 
                {
                    //to do 处理辅助属性补全的逻辑
                    //先取明细
                    var fentry = FormId == "ydj_order" ? RepairObj?["fentry"] as DynamicObjectCollection : RepairObj?["fentity"] as DynamicObjectCollection;
                    var refMgr = this.Container.GetService<LoadReferenceObjectManager>();
                    refMgr.Load(this.Context, RepairObj.GetDataEntityType(), RepairObj, false);
                    refMgr.Load(this.Context, productForm.GetDynamicObjectType(this.Context), fentry?.Select(s => s[$"{product}_ref"] as DynamicObject), false);

                    //遍历取到套件商品行
                    foreach (var mx in fentry) 
                    {
                        // refMgr.Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), mx, false);
                        var productId = Convert.ToString(mx[$"{product}"]);
                        var product_ref = mx[$"{product}_ref"] as DynamicObject;

                        if (Convert.ToBoolean(product_ref?["fsuiteflag"])) 
                        {
                            //遍历已有属性，排查出待补全的属性
                            var suite = this.Context.LoadBizDataByACLFilter("sel_suite",
                            "fproductid=@fproductid",
                            true,
                            new[] { new SqlParam("@fproductid", System.Data.DbType.String, productId)
                            }).FirstOrDefault();
                            //获取套件明细
                            var suitEntry = suite["fentity"] as DynamicObjectCollection;

                            //对辅助属性进行重组补全
                            var fattrinfo = Convert.ToString(mx["fattrinfo"]);
                            var attrInfoObj = mx["fattrinfo_ref"] as DynamicObject;

                            var auxPropValSetForm = this.MetaModelService.LoadFormModel(this.Context, "bd_auxpropvalueset");
                            var auxPropValSetObj = new DynamicObject(auxPropValSetForm.GetDynamicObjectType(this.Context));
                            auxPropValSetObj["fmaterialid"] = Convert.ToString(mx[$"{product}"]);
                            var auxPropValSetEntity = auxPropValSetForm.GetEntryEntity("FEntity");
                            var auxPropValSetEntrys = auxPropValSetObj["FEntity"] as DynamicObjectCollection;

                            if (attrInfoObj != null)
                            {
                                //获取原先合同明细套件商品的辅助属性，在原辅助属性基础上补全
                                var attrInfoEntrys = attrInfoObj["FEntity"] as DynamicObjectCollection;
                                //auxPropValSetEntrys = attrInfoEntrys;
                                //先将已有的填充进去
                                foreach (var attrInfoEntry in attrInfoEntrys) 
                                {
                                    var auxPropValSetEntry = new DynamicObject(auxPropValSetEntity.DynamicObjectType);
                                    auxPropValSetEntry["fauxpropid"] = attrInfoEntry?["fauxpropid"];
                                    auxPropValSetEntry["fvalueid"] = attrInfoEntry?["fvalueid"]; ;
                                    auxPropValSetEntry["fvaluename"] = attrInfoEntry?["fvaluename"]; ;
                                    auxPropValSetEntry["fvaluenumber"] = attrInfoEntry?["fvaluenumber"]; ;
                                    auxPropValSetEntrys.Add(auxPropValSetEntry);
                                }
                                //再填充套件明细中的自建属性
                                foreach (var suit in suitEntry)
                                {
                                    var fpartprop_ref = suit["fpartprop_ref"] as DynamicObject;
                                    if (fpartprop_ref == null) continue;
                                    //如果套件明细中没有找到属性则去补全
                                    if (!attrInfoEntrys.Any(o => Convert.ToString(o["fauxpropid"]) == Convert.ToString(fpartprop_ref?["id"])))
                                    {
                                        //如果已经补充过一次，无须再补充。
                                        if (auxPropValSetEntrys.Any(o => Convert.ToString(o["fauxpropid"]) == Convert.ToString(fpartprop_ref?["id"]))) continue;
                                        var auxPropValSetEntry = new DynamicObject(auxPropValSetEntity.DynamicObjectType);
                                        auxPropValSetEntry["fauxpropid"] = fpartprop_ref?["id"];
                                        auxPropValSetEntry["fvalueid"] = 0;
                                        auxPropValSetEntry["fvaluename"] = 0;
                                        auxPropValSetEntry["fvaluenumber"] = 0;
                                        auxPropValSetEntrys.Add(auxPropValSetEntry);
                                    }
                                }
                                mx["fattrinfo_ref"] = auxPropValSetObj;
                            }
                        }
                    }
                    var prepareService = this.Container.GetService<IPrepareSaveDataService>();
                    prepareService?.PrepareDataEntity(this.Context, htmlForm, new DynamicObject[] { RepairObj }, this.OperationContext.Option);
                    dm.Save(RepairObj);
                }
            }
            this.Result.IsSuccess = true;
            this.Result.SimpleMessage = "属性值更新成功！";
        }

        /// <summary>
        /// 查询 套件选配中 套件辅助属性数目少于套件档案明细的子件属性
        /// </summary>
        /// <returns></returns>
        private DynamicObjectCollection LoadSuitSelectionNeedRepair() {
            var sqlText = $@"  select od.fid ,'ydj_order' as 'formid' from t_ydj_order od 
                                inner join T_YDJ_ORDERENTRY odmx on odmx.fid = od.fid
                                inner join T_BD_MATERIAL pd on pd.fid = odmx.fproductid and fsuiteflag =1
                                inner join t_sel_suite tj on odmx.fproductid = tj.fproductid
                                inner join t_sel_suiteentry tjmx on tjmx.fid = tj.fid
                                INNER JOIN t_bd_auxpropvalue attr ON odmx.fattrinfo = attr.fid
                                INNER JOIN t_bd_auxpropvalueentry attrmx ON attrmx.fid = attr.fid
                                where ISNULL(fpartprop ,'') !='' group by od.fid 
                                having count(distinct attrmx.FEntryId)-1 < COUNT(distinct tjmx.fpartprop)
                                union 
                                 select od.fid,'ydj_purchaseorder' as 'formid' from T_YDJ_PURCHASEORDER od 
                                inner join t_ydj_poorderentry odmx on odmx.fid = od.fid
                                inner join T_BD_MATERIAL pd on pd.fid = odmx.fmaterialid and fsuiteflag =1
                                inner join t_sel_suite tj on odmx.fmaterialid = tj.fproductid
                                inner join t_sel_suiteentry tjmx on tjmx.fid = tj.fid
                                INNER JOIN t_bd_auxpropvalue attr ON odmx.fattrinfo = attr.fid
                                INNER JOIN t_bd_auxpropvalueentry attrmx ON attrmx.fid = attr.fid
                                where ISNULL(fpartprop ,'') !='' group by od.fid 
                                having count(distinct attrmx.FEntryId)-1 < COUNT(distinct tjmx.fpartprop)";
            var dynObs = this.DBService.ExecuteDynamicObject(this.Context, sqlText);
            return dynObs;
        }
    }
}
