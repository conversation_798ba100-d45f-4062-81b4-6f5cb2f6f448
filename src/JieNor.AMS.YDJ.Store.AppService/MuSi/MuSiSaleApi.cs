using JieNor.AMS.YDJ.Store.AppService.MuSi.DTO;
using JieNor.Framework;
using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.Interface.SystemIntegration;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Utils;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi
{
    /// <summary>
    /// 慕思招商系统Api
    /// </summary>
    public class MuSiSaleApi
    {
        /// <summary>
        /// 商机接口
        /// </summary>
        public static readonly UrlData CustomerRecordUrl = new UrlData { Name = "商机同步", Url = "/cdapi/opportunity/saveOrUpdateOpportunityDetails" };

        /// <summary>
        /// 商机关闭接口
        /// </summary>
        public static readonly UrlData saveOrUpdate = new UrlData { Name = "商机关闭", Url = "/cdapi/opportunityClose/saveOrUpdate" };

        /// <summary>
        /// 同步跟进记录
        /// </summary>
        public static readonly UrlData AsyncFollow = new UrlData { Name = "同步跟进记录", Url = "/cdapi/trackrecord/save" };

        /// <summary>
        /// 商品列表
        /// </summary>
        public static readonly UrlData goodlist = new UrlData { Name = "商品列表", Url = "consumer-admin/goods/list" };


        /// <summary>
        /// 商机同步
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="dto"></param> 
        public static MuSiResponse<object> SendCustomerRecord(UserContext userCtx, UrlData UrlData, HtmlForm htmlForm, Object dto, Enu_HttpMethod med = Enu_HttpMethod.Post)
        {
            return Invoke<object>(userCtx, htmlForm, UrlData.Url, UrlData.Name, dto, med);
        }

        /// <summary>
        /// 执行
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="url"></param>
        /// <param name="data"></param>
        /// <returns></returns>
        private static MuSiResponse<T> Invoke<T>(UserContext userCtx, HtmlForm bizForm, string url, string opName, object data, Enu_HttpMethod med)
        {
            userCtx.CheckNoTestCompany();

            var extApp = GetExtApp(userCtx);
            if (extApp == null) return MuSiResponse<T>.SUCCESS;

            var systemIntegrationService = userCtx.Container.GetService<ISystemIntegrationService>();

            //操作日志对象
            var opLogForm = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_operationlog");
            var opLogObj = systemIntegrationService.CreateOperationLog(userCtx, opLogForm, url, opName, bizForm.Id);

            opLogObj["fdescription"] = $"当前系统调用{extApp["fname"]}接口";
            opLogObj["fopstatus"] = "2";

            try
            {
                var client = new MuSiMerchantClient(userCtx, extApp);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求Url：{client.TargetServer.Host}{url}");
                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求Header：{client.TargetServer.Headers.ToJson()}");
                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"请求参数：{data.ToJson()}");

                var resp = client.Send<MuSiResponse<T>>(url, data, med);

                opLogObj["fopstatus"] = resp.Code == 0 ? "2" : "3";
                if (resp.Code != 0)
                {
                    opLogObj["ferrorsource"] = "2";
                }

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, $"响应参数：{resp.ToJson()}");
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                return resp;
            }
            catch (Exception ex)
            {
                //日志保存至文件
                userCtx.Container.GetService<ILogServiceEx>().Error(ex);

                systemIntegrationService.WriteOperationLog(userCtx, opLogObj, ex.Message + "\r\n" + ex.StackTrace, true);
                systemIntegrationService.SaveOperationLogAsync(userCtx, opLogForm, new[] { opLogObj });

                throw;
            }
        }

        /// <summary>
        /// 获取外部应用
        /// </summary>
        /// <param name="userCtx"></param>
        /// <returns></returns>
        private static DynamicObject GetExtApp(UserContext userCtx)
        {
            var sysProfileService = userCtx.Container.GetService<ISystemProfile>();

            var topCtx = userCtx.CreateTopOrgDBContext();

            // 获取
            var extAppId = sysProfileService.GetSystemParameter<string>(topCtx, "si_datasyncparam", "fmusiextappid", "");

            if (extAppId.IsNullOrEmptyOrWhiteSpace())
            {
                var formMeta = userCtx.Container.GetService<IMetaModelService>().LoadFormModel(userCtx, "si_datasyncparam");

                throw new BusinessException($"未设置【{formMeta.Caption}】的【{formMeta.GetField("fmusiextappid")?.Caption}】");
            }

            var extApp = topCtx.LoadBizDataById("sys_externalapp", extAppId);
            return extApp;
        }
    }

    public class UrlData
    {
        public string Url { get; set; }
        public string Name { get; set; }
    }
}
