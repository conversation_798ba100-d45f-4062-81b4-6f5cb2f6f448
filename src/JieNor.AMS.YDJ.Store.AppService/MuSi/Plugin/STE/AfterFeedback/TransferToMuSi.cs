using System;
using System.Collections.Generic;
using System.Linq;
using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Plugin.STE.AfterFeedback
{
    /// <summary>
    /// 售后反馈单：同步
    /// </summary>
    [InjectService]
    [FormId("ste_afterfeedback")]
    [OperationNo("synctomusi")]
    [ThirdSystemId("musi")]
    public class TransferToMuSi : AbstractSyncDataToMuSiPlugIn
    {
        /// <summary>
        /// 字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforeFieldMapping(BeforeFieldMappingEventArgs e)
        {
            base.BeforeFieldMapping(e);

            if (e.Entity == null || e.FieldEntry == null) return;

            var dataEntity = e.Entity;
            var bizEntity = e.DataEntity;

            string extFieldId = Convert.ToString(e.FieldEntry["fextfieldid"]).ToLower();
            switch (extFieldId)
            {
                //经销商编码
                case "salesnum":
                    {
                        e.Cancel = true;
                        e.Result = this.UserContext.GetAllCompanys().Values.FirstOrDefault(s => s.CompanyId.EqualsIgnoreCase(UserContext.Company))?.CompanyNumber;
                    }
                    break;
                // 作废状态
                case "delstatus":
                    {
                        e.Cancel = true;
                        e.Result = !dataEntity["fcancelstatus"].IsNullOrEmptyOrWhiteSpace() && Convert.ToBoolean(dataEntity["fcancelstatus"]);
                    }
                    break;
                // 问题图片名称集合
                case "questionimage":
                    {
                        e.Cancel = true;
                        e.Result = new List<PbImgModel>();
                        if (!dataEntity["fquestionimage"].IsNullOrEmptyOrWhiteSpace())
                        {
                            List<PbImgModel> result = new List<PbImgModel>();
                            var imgIds = Convert.ToString(dataEntity["fquestionimage"]).Split(',');
                            var imgTxts = Convert.ToString(dataEntity["fquestionimage_txt"]).Split(',');
                            for (int i = 0; i < imgIds.Length; i++)
                            {
                                if (!imgIds[i].IsNullOrEmptyOrWhiteSpace())
                                {
                                    var name = (i + 1 > imgTxts.Length ? imgIds[i] : imgTxts[i]);
                                    result.Add(new PbImgModel
                                    {
                                        id = imgIds[i],
                                        name = name.IsNullOrEmptyOrWhiteSpace() ? imgIds[i] : name,
                                        url = imgIds[i]?.GetSignedFileUrl()
                                    });
                                }
                            }
                            e.Result = result;
                        }
                    }
                    break;
            }
        }

        /// <summary>
        /// 打包前事件
        /// </summary>
        /// <param name="e"></param>
        public override void BeforePackSourceBill(BeforePackSourceBillEventArgs e)
        {
            base.BeforePackSourceBill(e);

            if (e.DataEntitys == null || e.DataEntitys.Count() == 0) return;
            //为空行删除掉，不传给中台
            foreach (var item in e.DataEntitys)
            {
                var profentry = (item["fproductentry"] as DynamicObjectCollection);
                for (int i = 0; i < profentry.Count; i++)
                {
                    if (profentry[i]["fmaterialid"].IsNullOrEmpty())
                    {
                        profentry.Remove(profentry[i]);
                        i--;
                    }
                }
            }
        }
    }
    /// <summary>
    /// 中台图片公用模型
    /// </summary>
    public class PbImgModel
    {
        public string id { get; set; }
        public string name { get; set; }
        public string url { get; set; }
    }
}
