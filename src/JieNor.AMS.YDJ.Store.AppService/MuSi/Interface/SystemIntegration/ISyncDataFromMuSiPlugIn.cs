using JieNor.AMS.YDJ.Store.AppService.MuSi.FormService.SystemIntegration;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.AMS.YDJ.Store.AppService.MuSi.Interface.SystemIntegration
{
    /// <summary>
    /// 从慕思中台同步数据的插件接口
    /// </summary>
    public interface ISyncDataFromMuSiPlugIn
    {
        /// <summary>
        /// 服务端自定义事件
        /// </summary>
        /// <param name="e"></param>
        void OnCustomServiceEvent(OnCustomServiceEventArgs e);

        ///// <summary>
        ///// 调用向第三方系统发送请求前事件（可干预请求参数）
        ///// </summary>
        ///// <param name="e"></param>
        //void BeforeSend(BeforeSendEventArgs e);

        ///// <summary>
        ///// 调用向第三方系统发送请求后事件（可干预响应数据）
        ///// </summary>
        ///// <param name="e"></param>
        //void AfterSend(AfterSendEventArgs e);

        /// <summary>
        /// 调用字段映射前事件
        /// </summary>
        /// <param name="e"></param>
        void BeforeFieldMapping(BeforeFieldMappingEventArgs e);

        /// <summary>
        /// 调用单据体字段映射事件
        /// </summary>
        /// <param name="e"></param>
        void EntryFieldMapping(EntryFieldMappingEventArgs e);

        /// <summary>
        /// 调用保存前事件
        /// </summary>
        /// <param name="e"></param>
        void BeforeSave(BeforeSaveEventArgs e);

        /// <summary>
        /// 调用保存后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterSave(AfterSaveEventArgs e);

        /// <summary>
        /// 调用执行后事件
        /// </summary>
        /// <param name="e"></param>
        void AfterExecute(AfterExecuteEventArgs e);
    }
}
