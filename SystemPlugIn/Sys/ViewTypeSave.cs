using System;
using System.Linq;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Security.Cryptography;

using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.DataTransferObject.DB;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.Validator;

namespace JieNor.Framework.AppService.SystemPlugIn.Sys
{
    /// <summary>
    /// 视图类型 --查看
    /// </summary>
    [InjectService]
    [FormId("sys_viewtype")]
    [OperationNo("Save")]
    public class ViewTypeSave : AbstractOperationServicePlugIn
    {

        public override void AfterExecuteOperationTransaction(AfterExecuteOperationTransaction e)
        {
            base.AfterExecuteOperationTransaction(e);

            this.CacheClient.RemoveAllByKeyPattern("sysmenu:viewtype", Enu_SearchType.StartWith); 

        }

    }

}