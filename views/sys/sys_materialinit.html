<!-- 商品添加辅助属性弹框 -->
<style>
	.materialinit-button button{
		border: 1px solid #00aaef;
		background-color: #00aaef;
		color: #fff;
		border-radius: 3px !important;
	}
    
</style>
<!--页面内容面板-->
<form action="javascript:;" class="form-horizontal">
	<div class="leftTab">
		<div class="col-md-12 portlet box yellow-casablanca">
	        <div class="portlet-title">
	            <div class="caption">
	               	基本信息
	            </div>
	            <div class="tools">
	                <a href="javascript:;" class="collapse"></a>
	            </div>
	        </div>
	        <div class="portlet-body form">
			    <div class="form-body">
			    	
			        <div class="row">
			        	
			        	<div class="col-md-8">
			        		<div class="form-group">
	                            <label class="col-md-3 control-label">商品数据 </label>
	                            <div class="col-md-8" style="width: auto;">
	                                <div caution="" hasbutton="" limit="1" sizelimit="" allowExt="xls,xlsx,XLS,XLSX" class="uploader-file" name="ffileid">上传附件</div>
	                            </div>
						   </div>
						   
						</div>
						
						<div class="col-md-6 pull-right materialinit-button">
							<button id="tbMaterialin" type="button" opcode="materialin" class="btn">导入</button>
							<button id="tbCreatemodel" type="button" opcode="createmodel" class="btn">生成导入模板</button>
							<button id="tbImporterror" type="button" opcode="importerror" class="btn">导入错误</button>
							<button id="tbImportres" type="button" opcode="importres" class="btn">导入结果</button>
							
							

						</div>
					</div>
					
					
	            </div>
	        </div>
	   	</div>
		
	</div>
	<a id="downLoadFileLink" style="display:none"></a>
</form>
