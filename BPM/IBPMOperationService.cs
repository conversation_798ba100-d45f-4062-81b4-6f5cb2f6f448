using JieNor.Framework.DataTransferObject.BPM;
using JieNor.Framework.DataTransferObject.Poco;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.BPM
{




    /// <summary>
    /// BPM系统 操作服务接口
    /// </summary>
    public interface IBPMOperationService
    {




        /// <summary>
        /// 执行请求
        /// </summary>
        /// <param name="ctx"></param> 
        /// <param name="reqPara"></param>
        /// <returns></returns>
        IOperationResult Execute(UserContext ctx, BPMFlowDTO reqPara);



    }






}
