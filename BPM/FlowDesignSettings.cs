using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.DataTransferObject.BPM
{
    /// <summary>
    /// 流程设计数据/节点描述（节点配置）
    /// </summary>
    public class FlowDesignSettings
    {
        /// <summary>
        /// 流程Id
        /// </summary>
        public static List<string> CheckDesign(BPMDesignDrawingModel model)
        {
            List<string> errText = new List<string>();
            if (model == null || model.sections == null || model.lines == null
                || model.sections.Count < 3 || model.lines.Count < 2)
            {
                errText.Add("流程设计必须包含开始节点/内部节点/结束节点，请检查！");
                return errText;
            }
            //检测节点
            var sections = model.sections;
            if (sections.Count < 3) errText.Add("流程设计节点必须三个以上！");
            var startSection = sections.Where(s => s.stepType == (int)SectionTypes.Start).ToList();
            if (startSection.Count < 1) errText.Add("流程设计必须要有开始节点！");
            if (startSection.Count > 1) errText.Add("流程设计只允许有一个开始节点！");
            var endSection = sections.Where(s => s.stepType == (int)SectionTypes.Finished).ToList();
            if (endSection.Count < 1) errText.Add("流程设计必须要有结束节点！");
            if (endSection.Count > 1) errText.Add("流程设计只允许有一个结束节点！");

            var endPrevNodeKeys = new List<string>();
            //检查各个节点（是否存在线条违规指向）
            foreach (var node in model.sections)
            {
                if (node.stepType == (int)SectionTypes.Start)
                {
                    if (model.lines.Where(l => l.to == node.key).Any())
                        errText.Add("不能有连线指向开始节点！");

                    var endNode = model.sections.Where(s => s.stepType == (int)SectionTypes.Finished).ToList();
                    if (endNode.Count == 1 && model.lines.Where(l => l.from == node.key && l.to == endNode.First().key).Any())
                        errText.Add("开始节点连线不能指向结束节点！");
                }
                else if (node.stepType == (int)SectionTypes.Finished)
                {
                    foreach (var line in model.lines)
                    {
                        if (line.from == node.key)
                        {
                            errText.Add("结束节点不能作为连线起始点！");
                        }
                        if (line.to == node.key)
                        {
                            endPrevNodeKeys.Add(line.from);
                        }
                    }
                }
                if (string.IsNullOrWhiteSpace(node.text))
                {
                    errText.Add("节点名称不能为空！请检查");
                }
                var nodeData = node?.param?.costData;
                if (nodeData == null)
                {
                    errText.Add("节点【" + node.text + "】数据不完整！");
                }
                if (node.stepType != (int)SectionTypes.Finished)
                {
                    if (string.IsNullOrWhiteSpace(nodeData.fbizformid?.id)
                        && string.IsNullOrWhiteSpace(nodeData.fjoinuser?.id)
                        && string.IsNullOrWhiteSpace(nodeData.fjoindept?.id)
                        && string.IsNullOrWhiteSpace(nodeData.fjoinrole?.id)
                        && string.IsNullOrWhiteSpace(nodeData.fjoinvar?.id))
                    {
                        errText.Add("节点【" + node.text + "】未设置参与人！");
                    }
                }
                //if (node.stepType != (int)SectionTypes.Finished && (nodeData.fparamentry == null || !nodeData.fparamentry.Where(p => p.fpermit == true).Any()))
                //{
                //    errText.Add("节点【" + node.text + "】未设置审批权限项！");
                //}
            }

            foreach (var node in model.sections)
            {
                if (node.stepType == (int)SectionTypes.Start || node.stepType == (int)SectionTypes.Finished)
                {
                    continue;
                }
                var nodeData = node?.param?.costData;
                if (endPrevNodeKeys.Contains(node.key))
                {
                    //检查连接结束节点的其他节点是否设置了审批权限项
                    if (nodeData.fparamentry.Count(x => x.fformop.id.Contains("audit")) == 0)
                    {
                        errText.Add("节点【" + node.text + "】未设置审批权限项！");
                    }
                }
                else
                {
                    //中间节点不允许配置表单操作 
                    if (nodeData.fparamentry.Any() && nodeData.fparamentry.Count(x => x.fformop != null && x.fformop.id != string.Empty) > 0)
                    {
                        errText.Add("节点【" + node.text + "】不允许配置表单操作！");
                    }
                }
            }

            //检测线条
            var errLine = model.lines.Where(l => l.from == l.to || l.to == l.from).FirstOrDefault();
            if (errLine != null) errText.Add("连线：" + errLine.text + "指向错误！");
            foreach (var line in model.lines)
            {
                var fromto = model.lines.Where(l => l.from == line.to && l.to == line.from).ToList();
                if (fromto != null && fromto.Count > 0) errText.Add("连线：" + line.text + "存在双向指向错误！");
            }
            return errText;
        }
    }


    #region 枚举定义（节点类型、参与人类型、审核操作类型）
    /// <summary>
    /// 节点类型
    /// </summary>
    public enum SectionTypes
    {
        /// <summary>
        /// 开始
        /// </summary>
        Start = 1,

        /// <summary>
        /// 内部流程节点
        /// </summary>
        InterProcess = 5,

        /// <summary>
        /// 外部流程节点
        /// </summary>
        ExterProcess = 6,

        /// <summary>
        /// 结束（完成）
        /// </summary>
        Finished = 4
    }

    /// <summary>
    /// 参与人(类型)
    /// </summary>
    public enum UserTypes
    {
        /// <summary>
        /// 用户
        /// </summary>
        User = 0,

        /// <summary>
        /// 部门
        /// </summary>
        Dept = 1,

        /// <summary>
        /// 角色
        /// </summary>
        Role = 2,

        /// <summary>
        /// 内部参与人变量
        /// </summary>
        InterUserParam = 5,

        /// <summary>
        /// 外部参与人变量
        /// </summary>
        ExterUserParam = 6
    }

    /// <summary>
    /// 审核操作类型
    /// </summary>
    public enum AuditTypes
    {
        /// <summary>
        /// 通过
        /// </summary>
        Pass = 1,

        /// <summary>
        /// 驳回
        /// </summary>
        Reject = 2,

        /// <summary>
        /// 终止（打回起点）
        /// </summary>
        Rollback = 3
    }

    /// <summary>
    /// 节点参与类型（是否会签模式）
    /// </summary>
    public enum ParticipateTypes
    {
        /// <summary>
        /// 非会签模式
        /// </summary>
        NonMeetingSignMode = 0,

        /// <summary>
        /// 会签模式
        /// </summary>
        MeetingSignMode = 1
    }
    #endregion
}
