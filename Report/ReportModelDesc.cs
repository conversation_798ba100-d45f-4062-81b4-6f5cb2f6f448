using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.DataTransferObject.Report
{
    /// <summary>
    /// 报表模型描述对象
    /// </summary>
    public class ReportModelDesc
    {
        /// <summary>
        /// 报表列模型
        /// </summary>
        public ColumnHeadObject RptGridModel { get; set; }

        /// <summary>
        /// 报表上图表结构
        /// </summary>
        public Dictionary<string, RptChartDesc> Charts { get; set; }

        /// <summary>
        /// 指示报表是否立刻请求数据，允许在报表打开后人为交互提供一些过滤条件后，点某个按钮来触发数据加载
        /// </summary>
        public bool AutoLoadData { get; set; }
    }


}
