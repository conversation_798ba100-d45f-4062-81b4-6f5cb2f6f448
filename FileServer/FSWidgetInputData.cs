using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.DataTransferObject.FileServer
{
    /// <summary>
    /// 文件服务器组件输入数据结构
    /// </summary>
    public class FSWidgetInputData
    {
        /// <summary>
        /// 文件组件调用者身份名称
        /// </summary>
        public string UserName { get; set; }        

        /// <summary>
        /// 文件组件调用者企业代码
        /// </summary>
        public string CompanyId { get; set; }

        /// <summary>
        /// 文件组件调用者产品标识
        /// </summary>
        public string ProductId { get; set; }

        /// <summary>
        /// 文件数据
        /// </summary>
        public string FileData { get; set; }

        /// <summary>
        /// 操作类型：0-读写，1-只读
        /// </summary>
        public int Type { get; set; }

        /// <summary>
        /// 调用方标识
        /// </summary>
        public string Caller { get; set; }

        /// <summary>
        /// 协同表单标识
        /// </summary>
        public string CooFormId { get; set; }

        /// <summary>
        /// 协同表单实体标识
        /// </summary>
        public string CooEntity { get; set; }

        /// <summary>
        /// 协同交易流水号
        /// </summary>
        public string CooTranId { get; set; }
    }
}
