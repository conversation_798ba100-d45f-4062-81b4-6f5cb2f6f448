using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.UserDTO;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.Interface;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.WebController.UserEdit
{
    /// <summary>
    /// 获取注册时可用的角色信息
    /// </summary>
    public class ListRegisterRolesService : ServiceStack.Service
    {
        public object Get(ListRegisterRolesDTO dto)
        {
            var container = this.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            var resp = container.GetService<DynamicDTOResponse>();

            var companyInfo = this.GetDefaultCompany();
            if (companyInfo == null) return resp;

            var userCtx = this.CreateContextByCompanyInfo(companyInfo);
           
            var metaModelService = container.GetService<IMetaModelService>();
            var roleMeta = metaModelService.LoadFormModel(userCtx, "sec_role");
            var dm = container.GetService<IDataManager>();
            dm.Option.SetAutoUpdateScheme(true);
            dm.InitDbContext(userCtx, roleMeta.GetDynamicObjectType(userCtx));

            var pkIdReader = userCtx.GetPkIdDataReader(roleMeta, "fmainorgid='0' and fregvisible='1'", new SqlParam[] { });
            var allRoleObjs = dm.SelectBy(pkIdReader)
                .OfType<DynamicObject>();
            List<BaseDataSummary> lstDataObjs = new List<BaseDataSummary>();
            foreach (var roleObj in allRoleObjs)
            {
                var roleDataObj = new BaseDataSummary()
                {
                    Id = roleObj["id"] as string,
                    Number = roleObj["fnumber"] as string,
                    Name = roleObj["fname"] as string
                };
                lstDataObjs.Add(roleDataObj);
            }
            resp.OperationResult.SrvData = lstDataObjs;
            resp.OperationResult.IsSuccess = true;

            return resp;
        }
    }
}
