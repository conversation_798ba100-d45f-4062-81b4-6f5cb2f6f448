//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using JieNor.Framework.Interface.BizTask;
//using JieNor.Framework.Interface.Log;
//using JieNor.Framework.IoC;
//using JieNor.Framework.Meta.Designer;

//namespace JieNor.Framework.AppService.ExPlugIn.Schedule
//{
//    [InjectService]
//    [TaskSvrId("task.test1")]
//    [Caption("测试任务1")]
//    public class BackJobTest : AbstractScheduleWorker
//    {
//        public static int Flag = 0;

//        private int Flag2 = 0;

//        /// <summary>
//        /// 执行计算逻辑
//        /// </summary>
//        /// <returns></returns>
//        protected override async Task DoExecute()
//        {
//            this.WriteLog("测试任务2正在计算商品价格……");
//            var log = this.UserContext.Container.GetService<ILogServiceEx>();
//            log.Info($"测试任务1正在执行……，{Flag++},{Flag2++}.");
//            await Task.Delay(3000);
//            this.WriteLog("测试任务2商品价格计算完成……");
//        }
//    }

//    //    [InjectService]
//    //    [TaskSvrId("task.test2")]
//    //    [Caption("测试任务2")]
//    //    public class BackJobTest2 : AbstractScheduleWorker
//    //    {
//    //        protected override void DoExecute()
//    //        {
//    //            var log = this.UserContext.Container.GetService<ILogServiceEx>();
//    //            log.Info($"测试任务2正在执行……，");
//    //        }
//    //    }
//}
