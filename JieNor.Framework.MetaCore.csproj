<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{565CAC9A-0FE7-48EE-96ED-AC6AEB9232C4}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>JieNor.Framework.MetaCore</RootNamespace>
    <AssemblyName>JieNor.Framework.MetaCore</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\lib\debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>..\..\lib\debug\JieNor.Framework.MetaCore.XML</DocumentationFile>
    <NoWarn>CS1591;CS1573;CS1587</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\lib\release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>..\..\lib\release\JieNor.Framework.MetaCore.XML</DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=4.6.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\..\src\packages\Autofac.4.6.0\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="FluentValidation, Version=6.4.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\src\packages\FluentValidation.6.4.0\lib\Net45\FluentValidation.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.9.0.1, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\JieNor.Framework.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.DataTransferObject, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\JieNor.Framework.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.SuperOrm, Version=3.1.0.1, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\src\packages\JieNor.Framework.SuperOrm.3.1.0.1\lib\net461\JieNor.Framework.SuperOrm.dll</HintPath>
    </Reference>
    <Reference Include="Mono.Cecil, Version=0.9.6.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\src\packages\Mono.Cecil.0.9.6.4\lib\net45\Mono.Cecil.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Mono.Cecil.Mdb, Version=0.9.6.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\src\packages\Mono.Cecil.0.9.6.4\lib\net45\Mono.Cecil.Mdb.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Mono.Cecil.Pdb, Version=0.9.6.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\src\packages\Mono.Cecil.0.9.6.4\lib\net45\Mono.Cecil.Pdb.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Mono.Cecil.Rocks, Version=0.9.6.0, Culture=neutral, PublicKeyToken=0738eb9f132ed756, processorArchitecture=MSIL">
      <HintPath>..\..\src\packages\Mono.Cecil.0.9.6.4\lib\net45\Mono.Cecil.Rocks.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\src\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack, Version=4.5.6.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Client, Version=4.5.6.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Client.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Common, Version=4.5.6.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Common.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=4.0.0.0, Culture=neutral, PublicKeyToken=e06fbc6124f57c43, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Text, Version=4.5.6.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Text.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\..\InterceptorAttribute.cs">
      <Link>InterceptorAttribute.cs</Link>
    </Compile>
    <Compile Include="BizDynamicDataRow.cs" />
    <Compile Include="BizState\AbstractStateMachineEvent.cs" />
    <Compile Include="BizState\BusinessState.cs" />
    <Compile Include="BizState\BusinessStateMachine.cs" />
    <Compile Include="BizState\BusinessTransition.cs" />
    <Compile Include="BizState\Exceptions\FinalStateTransitionException.cs" />
    <Compile Include="BizState\Exceptions\InvalidStateTransitionException.cs" />
    <Compile Include="BizState\Exceptions\ParentStateNotAvailableException.cs" />
    <Compile Include="BizState\Exceptions\SelfTransitionException.cs" />
    <Compile Include="BizState\Exceptions\StringTable.Designer.cs" />
    <Compile Include="BizState\Exceptions\TransitionActionException.cs" />
    <Compile Include="BLA\BLAEngine.cs" />
    <Compile Include="BLA\InputPropertyAttribute.cs" />
    <Compile Include="BLA\OutputPropertyAttribute.cs" />
    <Compile Include="ExtendedDataEntity.cs" />
    <Compile Include="ExtendedDataEntitySet.cs" />
    <Compile Include="FormMetaIoCModule.cs" />
    <Compile Include="FormMeta\ChartElement\HtmlBarChartElement.cs" />
    <Compile Include="FormMeta\ChartElement\HtmlChartElement.cs" />
    <Compile Include="FormMeta\ChartElement\HtmlLineChartElement.cs" />
    <Compile Include="FormMeta\ConvertElement\BillGroupObject.cs" />
    <Compile Include="FormMeta\ConvertElement\BillTypeMapObject.cs" />
    <Compile Include="FormMeta\ConvertElement\ConvertRule.cs" />
    <Compile Include="FormMeta\ConvertElement\FieldMapObject.cs" />
    <Compile Include="FormMeta\ConvertElement\FieldGroupObject.cs" />
    <Compile Include="FormMeta\HtmlBillNodeEntryEntity.cs" />
    <Compile Include="FormMeta\HtmlBizService.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlAuxPropertyField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBarCodeField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBaseDataEntryField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBasePropertyField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBizFieldValueField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBizFieldTypeField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlCloseStatusField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlColorField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlCompanyField.cs" />
    <Compile Include="FormMeta\HtmlBackgroundTask.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlDateRangeField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlFilterPanelField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlFlowInstanceField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlHyperLinkField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMaterialImageField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlPersonField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlSourceIdField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlTagField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlTimeField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlUserSignField.cs" />
    <Compile Include="FormMeta\HtmlElement\IFixedDropdownField.cs" />
    <Compile Include="FormMeta\HtmlElement\IRefFormField.cs" />
    <Compile Include="FormMeta\HtmlGroupField.cs" />
    <Compile Include="FormMeta\HtmlGroupView.cs" />
    <Compile Include="FormMeta\HtmlListRowBGColor.cs" />
    <Compile Include="FormMeta\HtmlListCellColor.cs" />
    <Compile Include="FormMeta\HtmlStatusSeal.cs" />
    <Compile Include="FormMeta\HtmlListFuzzyFld.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlDynBaseDataField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlExpressField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlExprPanelField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlJsonConfigField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlJsonImageField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMulClassTypeDataField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMulClassTypeField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMulBaseTableDataField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMulFieldModelField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlFieldModelField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMulBaseDataField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMulFileField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMulImageField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlNotesCountField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlPrintCountField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlAttacthCountField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlJsonField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlRichTextField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlSimpleSelectField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlSizeField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlSourceBillNoField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlSourceFormField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlButtonField.cs" />
    <Compile Include="FormMeta\HtmlElement\IFieldMetaSqlProxy.cs" />
    <Compile Include="FormMeta\HtmlElement\IMultTextField.cs" />
    <Compile Include="FormMeta\HtmlElement\ISupportCurrencySymbol.cs" />
    <Compile Include="FormMeta\HtmlElement\ISupportCustomFormat.cs" />
    <Compile Include="FormMeta\HtmlLinkEntryEntity.cs" />
    <Compile Include="FormMeta\HtmlOperation.cs" />
    <Compile Include="FormMeta\HtmlPermItem.cs" />
    <Compile Include="FormMeta\HtmlTreeEntryEntity.cs" />
    <Compile Include="FormMeta\HtmlItemState.cs" />
    <Compile Include="FormMeta\HtmlValidation.cs" />
    <Compile Include="FormMeta\IBaseDataFilterController.cs" />
    <Compile Include="FormMeta\IDomainMetaTypeLoader.cs" />
    <Compile Include="FormMeta\MenuElement\HtmlMenuBar.cs" />
    <Compile Include="FormMeta\MenuElement\HtmlMenuItem.cs" />
    <Compile Include="FormMeta\MenuElement\MenuBarParser.cs" />
    <Compile Include="FormModel\FormMetaExtend.cs" />
    <Compile Include="FormModel\ListTreeNode.cs" />
    <Compile Include="FormModel\List\ListFuzzySetting.cs" />
    <Compile Include="FormModel\List\ListColorSetting.cs" />
    <Compile Include="FormModel\List\QueryConstField.cs" />
    <Compile Include="FormModel\List\QueryExprField.cs" />
    <Compile Include="FormModel\List\QueryField.cs" />
    <Compile Include="FormModel\List\QueryMetaInfo.cs" />
    <Compile Include="FormModel\List\QueryRefField.cs" />
    <Compile Include="FormModel\List\SqlBuilderParameter.cs" />
    <Compile Include="FormModel\MdlSchemaSql.cs" />
    <Compile Include="FormModel\Office\OfficeContext.cs" />
    <Compile Include="FormModel\Office\PrintContext.cs" />
    <Compile Include="FormModel\Office\PrintDataInfor.cs" />
    <Compile Include="FormModel\QueryObject.cs" />
    <Compile Include="FormModel\WebView\PageInfrastructure.cs" />
    <Compile Include="FormOp\ActionParam\InsertRowAction.cs" />
    <Compile Include="FormOp\ActionParam\DeleteRowAction.cs" />
    <Compile Include="FormOp\ActionParam\SetValueAction.cs" />
    <Compile Include="FormOp\ActionParam\ShowProgressFormAction.cs" />
    <Compile Include="FormOp\BillShowParameter.cs" />
    <Compile Include="FormOp\BizWarn\PlugIn\AfterSendMessageEventArgs.cs" />
    <Compile Include="FormOp\BizWarn\PlugIn\BeforeSendMessageEventArgs.cs" />
    <Compile Include="FormOp\BizWarn\PlugIn\PrepareQueryObjectEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\BillSnapshotEntry.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\BeforeMapFieldValueEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\OnCalcWillLinkQtyEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\OnCreateLinkDataEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\OnPrepareQueryObjectEventArgs.cs" />
    <Compile Include="FormOp\FileServer\FileSummary.cs" />
    <Compile Include="FormOp\FlowControlContext.cs" />
    <Compile Include="FormOp\ConvertService\BillConvertContext.cs" />
    <Compile Include="BLA\IBizLogicUnit.cs" />
    <Compile Include="FormOp\ConvertService\ConvertLinkRelation.cs" />
    <Compile Include="FormOp\ConvertService\ConvertLinkResult.cs" />
    <Compile Include="FormOp\ConvertService\ConvertResult.cs" />
    <Compile Include="FormOp\ConvertService\ConvertSourceQueryObject.cs" />
    <Compile Include="FormOp\ConvertService\IConvertLogicUnit.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\BeforeCreateTargetBillDataEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\AfterGetSourceBillDataEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\AfterGroupSourceDataEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\BeforeGetSourceBillDataEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\OnGroupBillEntryDataEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\OnSplitBillDataEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\ConvertActionEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\InitializeServiceEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\OnConvertCompleteEventArgs.cs" />
    <Compile Include="FormOp\ConvertService\PlugIn\OnPrepareQueryBuilderParameterEventArgs.cs" />
    <Compile Include="FormOp\DynamicBillView.cs" />
    <Compile Include="FormOp\FormParameter.cs" />
    <Compile Include="FormOp\FormService\CustomEventData\BeforeGetConvertRuleData.cs" />
    <Compile Include="FormOp\FormService\DesignMeta\WritebackRuleDesc.cs" />
    <Compile Include="FormOp\FormService\FormServiceDesc.cs" />
    <Compile Include="FormOp\FormService\PlugIn\AfterWritebackEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\InitDistributedLocksEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\CreateObjectIdemotencyEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\BeforeWritebackEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\CustomEventArgs\BuildQueryListFilterStringEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\InitializeDataEntityEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnCustomServiceEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnBuildWritebackSqlEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnCheckExceedanceEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnDynamicRemindLoadLogArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnDynamicRemindProcessArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnLocateServiceTargetEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnPrepareOperationOptionEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\PrepareBusinessServiceEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\PrepareValidationRulesEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\SystemIntegration\AfterBuildBillMappingEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\SystemIntegration\AfterFieldMappingEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\SystemIntegration\AfterPackTargetBillEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\SystemIntegration\AfterSendDataToTargetEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\SystemIntegration\BeforeCreateEntityEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\SystemIntegration\BeforePackTargetBillEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\SystemIntegration\BeforeSendDataToTargetEventArgs.cs" />
    <Compile Include="FormOp\FormShowParameter.cs" />
    <Compile Include="FormOp\ListShowParameter.cs" />
    <Compile Include="FormOp\OpData\BillAuditDetailObject.cs" />
    <Compile Include="FormOp\OpData\DownloadConfig.cs" />
    <Compile Include="FormOp\OpData\ExcelExportParam.cs" />
    <Compile Include="FormOp\OpData\ExcelImportParam.cs" />
    <Compile Include="FormOp\OpData\OperationConfig.cs" />
    <Compile Include="FormOp\OpData\OperationControlParam.cs" />
    <Compile Include="FormOp\OpData\ShareBillParam.cs" />
    <Compile Include="FormOp\OpData\UserRegistry.cs" />
    <Compile Include="FormOp\ReportFieldModel.cs" />
    <Compile Include="FormOp\ReportShowParameter.cs" />
    <Compile Include="FormOp\ServiceControlOption.cs" />
    <Compile Include="FormOp\TaskProgress\TaskProgressItem.cs" />
    <Compile Include="FormOp\WritebackContext.cs" />
    <Compile Include="IExprParser.cs" />
    <Compile Include="MetaAttribute\HtmlAttributeKey.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlCheckBoxField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBillTypeField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBillStatusField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlComboField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlFieldExtension.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlModifyDateField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlDateField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlDateTimeField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlCreateDateField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlModifierField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlCreaterField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMultiLangTextField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlMulComboField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlUserField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlUnitField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlQtyField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlPriceField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlIntegerField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlImageField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlFileField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlDecimalField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBillNoField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlBaseDataField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlAmountField.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlTextField.cs" />
    <Compile Include="FormMeta\HtmlElement.cs" />
    <Compile Include="FormMeta\HtmlElementExtension.cs" />
    <Compile Include="FormMeta\HtmlEntity.cs" />
    <Compile Include="FormMeta\HtmlHeadEntity.cs" />
    <Compile Include="FormMeta\HtmlEntryEntity.cs" />
    <Compile Include="FormMeta\HtmlElement\HtmlField.cs" />
    <Compile Include="FormMeta\HtmlForm.cs" />
    <Compile Include="FormMeta\HtmlParser.cs" />
    <Compile Include="FormMeta\HtmlParserExtension.cs" />
    <Compile Include="FormMeta\HtmlSubEntryEntity.cs" />
    <Compile Include="FormMeta\ValueConverter\DateTimeValueConverter.cs" />
    <Compile Include="FormMeta\ValueConverter\ValueTypeConverter.cs" />
    <Compile Include="FormModel\List\EntityTable.cs" />
    <Compile Include="FormModel\FormUserProfile.cs" />
    <Compile Include="FormModel\List\SelectField.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnCheckPermssionArgs.cs" />
    <Compile Include="FormOp\OperationResultExtensions.cs" />
    <Compile Include="FormOp\HtmlViewAction.cs" />
    <Compile Include="FormOp\OperateOptionExtentions.cs" />
    <Compile Include="FormOp\OperationContext.cs" />
    <Compile Include="FormOp\FormService\PlugIn\AfterExecuteOperationTransaction.cs" />
    <Compile Include="FormOp\FormService\PlugIn\BeforeExecuteOperationTransaction.cs" />
    <Compile Include="FormOp\FormService\PlugIn\BeginOperationTransactionArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\BeginSetStausTransactionArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\EndOperationTransactionArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\EndSetStatusTransactionArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\InitializeServiceEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OnPrepareOperationServiceEventArgs.cs" />
    <Compile Include="FormOp\FormService\PlugIn\OperationTransactionArgs.cs" />
    <Compile Include="MetaAttribute\HtmlAttributeAttribute.cs" />
    <Compile Include="PageConst.cs" />
    <Compile Include="PermData\PermAuth.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Validator\IBusinessValidRule.cs" />
    <Compile Include="Validator\IDataValidRule.cs" />
    <Compile Include="Validator\IFluentRule.cs" />
    <Compile Include="Validator\ValidationManager.cs" />
    <Compile Include="Validator\ValidationResult.cs" />
    <Compile Include="Validator\ValidationRule.cs" />
    <Compile Include="Validator\ValidationServiceExtendsion.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="FormMeta\HtmlForm.DTA.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="FormModel\DataWidget\" />
    <Folder Include="FormOp\WriteBack\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="BizState\Exceptions\StringTable.resx" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\..\build\version.targets" />
</Project>