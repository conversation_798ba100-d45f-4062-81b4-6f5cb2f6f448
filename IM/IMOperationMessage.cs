using ServiceStack.Text;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.DataTransferObject.IM
{
    /// <summary>
    /// 定义一个业务操作相关的消息对象，更贴近业务应用
    /// </summary>
    public class IMOperationMessage : IMessageConverter
    {
        /// <summary>
        /// 初始化业务操作消息
        /// </summary>
        /// <param name="formId"></param>
        /// <param name="opCode"></param>
        /// <param name="opName"></param>
        public IMOperationMessage(string formId, string opCode, string opName)
        {
            this.FormId = formId;
            this.OpCode = opCode;
            this.OpName = opName;
        }

        /// <summary>
        /// 业务表单对象
        /// </summary>
        public string FormId { get; private set; }

        /// <summary>
        /// 操作代码
        /// </summary>
        public string OpCode { get; private set; }

        /// <summary>
        /// 操作名称
        /// </summary>
        public string OpName { get; private set; }

        /// <summary>
        /// 业务对象编号
        /// </summary>
        public string BillNo { get; set; }

        /// <summary>
        /// 自定义数据
        /// </summary>
        public Dictionary<string, object> CustomParameter { get; private set; } = new Dictionary<string, object>(StringComparer.OrdinalIgnoreCase);

        /// <summary>
        /// 消息接收者
        /// </summary>
        public List<string> Receivers { get; private set; } = new List<string>();

        /// <summary>
        /// 消息摘要
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public object Content { get; set; }

        /// <summary>
        /// 转换成标准通讯消息
        /// </summary>
        /// <returns></returns>
        public IMMessage ToMessage()
        {
            if (string.IsNullOrWhiteSpace(this.Title))
            {
                this.Title = $"单据{this.BillNo}{this.OpName}成功！";
            }
            IMMessage message = this.Title.CreateBusinessMessage(this.Receivers);
            message.Head.Qid = $"{this.FormId}";
            message.Head.Tags["opcode"] = this.OpCode;
            message.Head.Tags["opname"] = this.OpName;

            foreach (var kvpItem in this.CustomParameter)
            {
                message.Head.Tags[kvpItem.Key] = JsonSerializer.SerializeToString(kvpItem.Value);
            }

            message.AddMessage(new IMJsonMessage() { Content = JsonSerializer.SerializeToString(this.Content) });

            return message;
        }
    }
}
