using JieNor.Framework.CustomException;
using JieNor.Framework.DataTransferObject.SSO;
using JieNor.Framework.Interface;
using JieNor.Framework.Interface.Cache;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace JieNor.Framework.AppService.SSOTicket
{
    /// <summary>
    /// 蓝凌BPM凭据创建者
    /// </summary>
    [InjectService("sso_llbpm")]
    public class LLBPMTicketProcessor : ISSOTicketProcessor
    {
       

        [InjectProperty]
        IServiceContainer Container { get; set; }

        /// <summary>
        /// 凭据处理器标识
        /// </summary>
        public string ProcessorName
        {
            get { return "sso_llbpm"; }
        }

        /// <summary>
        /// 根据凭据信息创建本系统上下文
        /// </summary>
        /// <param name="serverId"></param>
        /// <param name="ticketInfo">单点登录凭据</param>
        /// <param name="dctParam"></param>
        /// <param name="userAuthTicket"></param>
        /// <returns></returns>
        public void CreateSSOTicket(string serverId, string ticketInfo, Dictionary<string, string> dctParam, out UserAuthTicket userAuthTicket)
        {
            userAuthTicket = new UserAuthTicket();
            userAuthTicket.IsAuthenticated = false;

            var ssoInfo = this.GetSSOServerInfo(serverId);

            var companyId = ssoInfo.DefCompanyId;
            if (companyId.IsNullOrEmptyOrWhiteSpace())
            {
                dctParam.TryGetValue("companyId", out companyId);
            }

            var companyInfo = companyId.GetCompany();;
            if (companyInfo == null)
            {
                throw new BusinessException($"当前站点中不存在要登录的企业主体：{companyId}");
            }

            var userCtx = this.CreateContextByCompanyInfo(companyInfo);

            if (ticketInfo.IsNullOrEmptyOrWhiteSpace())
            {
                throw new BusinessException($"用户认证信息错误");
            }

            StringReader sr = new StringReader(ticketInfo);
            var isOK = sr.ReadLine().EqualsIgnoreCase("yes");
            if (isOK)
            {
                var userName = sr.ReadLine();
                if (!userName.IsNullOrEmptyOrWhiteSpace())
                {
                    var metaModelService = this.Container.GetService<IMetaModelService>();

                    var userMeta = metaModelService.LoadFormModel(userCtx, "sec_user");
                    var dm = this.Container.GetService<IDataManager>();
                    dm.InitDbContext(userCtx, userMeta.GetDynamicObjectType(userCtx));
                    userCtx.UpdateMdlSchema(userMeta.Id);

                    var pkIdReader = userCtx.GetPkIdDataReader(userMeta, "(fname=@userName or fnumber=@userNumber) and fmainorgid=@companyId",
                        new SqlParam[]
                        {
                            new SqlParam("userName", System.Data.DbType.String,userName),
                            new SqlParam("userNumber", System.Data.DbType.String, userName),
                            new SqlParam("companyId", System.Data.DbType.String, companyInfo.CompanyId)
                        });

                    var allUser = dm.SelectBy(pkIdReader)
                        .OfType<DynamicObject>();
                    var linkUserObj = allUser.FirstOrDefault(f => f["fnumber"].ToString().EqualsIgnoreCase(userName));
                    if (linkUserObj == null)
                    {
                        linkUserObj = allUser.FirstOrDefault();
                    }

                    if (linkUserObj != null)
                    {
                        //ticket.Id = ticketInfo.Ticket;
                        userAuthTicket.UserAuthId = linkUserObj["id"] as string;
                        userAuthTicket.UserId = linkUserObj["id"] as string;
                        userAuthTicket.UserName = linkUserObj["fnumber"] as string;
                        userAuthTicket.DisplayName = linkUserObj["fname"] as string;
                        userAuthTicket.Company = companyInfo.CompanyId;
                        userAuthTicket.Companys = new List<DataTransferObject.DB.CompanyDCInfo> { companyInfo };
                        userAuthTicket.CreatedAt = DateTime.Now;
                        userAuthTicket.RefUserId = linkUserObj["frefuserid"] as string;
                        userAuthTicket.IsAuthenticated = true;
                        userAuthTicket.FromToken = true;

                        //this.Cache.Set(userCtx, cacheKey, ticket, TimeSpan.FromHours(1));
                    }
                }
            }

        }


        /// <summary>
        /// 处理SSO单点登录的票据
        /// </summary>
        /// <param name="serverId"></param>
        /// <param name="ticketInfo"></param>
        /// <param name="dctParam"></param>
        public void LogOut(string serverId, object ticketInfo, Dictionary<string, string> dctParam = null)
        {

        }

    }
}
