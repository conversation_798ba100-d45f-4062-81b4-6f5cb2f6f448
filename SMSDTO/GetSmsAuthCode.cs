using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.DataTransferObject.SMSDTO
{
    /// <summary>
    /// 调用验证码处理
    /// </summary>
    [Route("/sms/code", "GET")]
    public class GetSmsAuthCode : IReturn<DynamicDTOResponse>
    {
        /// <summary>
        /// 手机号
        /// </summary>
        public string MobilePhone { get; set; }

        /// <summary>
        /// 短信模板
        /// </summary>
        public string TemplateCode { get; set; }
    }
}
