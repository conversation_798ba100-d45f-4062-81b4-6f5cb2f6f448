using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.CustomException
{
    /// <summary>
    /// 微服务调用异常
    /// </summary>
    public class MicroServiceInvokeException : MicroServiceException
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message"></param>
        public MicroServiceInvokeException(string message)
            : base(message)
        {

        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="message"></param>
        /// <param name="ex"></param>
        public MicroServiceInvokeException(string message, Exception ex)
            : base(message,ex)
        {

        }

    }
}
