using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.BpmImpl.MyImpl.RequestPlugIn
{
    /// <summary>
    /// 获取系统可用的用户集合
    /// </summary>
    [InjectService]
    [FormId("bpm_consoleview")]
    [OperationNo("listusers")]
    public class GetModelUserList : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// 调用操作事物后触发
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            //BPM设计时取数接口
            var bpmDesigntimeService = this.Container.GetService<IBPMDesigntimeService>();
            if (bpmDesigntimeService != null)
            {
                this.Result.SrvData = bpmDesigntimeService.GetModelUserList(this.Context);
            }
        }
    }
}