using System;
using System.Collections.Concurrent;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using ServiceStack;
using ServiceStack.VirtualPath;

namespace JieNor.Framework
{

    /// <summary>
    /// 线程处理器,使用示例如下：
    /// <![CDATA[
    ///     UserContext userCtx = null;
    ///     object otherBizPara = new object();
    ///     IOperationResult result = new OperationResult();
    ///
    ///     ThreadWorker.QuequeTask(new Action<object>((p) =>
    ///     {
    ///         var px = p as TaskAsyncState;
    ///         var userCtx = px.UserContext;
    ///         var bizPara = px.BizParameter;
    ///         var ret = px.ReturnResult;
    ///
    ///         //todo....
    ///
    ///     }),
    ///     new TaskAsyncState()
    ///     {
    ///         UserContext = userCtx,
    ///         BizParameter = otherBizPara,
    ///         ReturnResult = result,
    ///     },
    ///     (asyncState) =>
    ///         {
    ///             var ret = asyncState.ReturnResult;
    ///             //todo：根据结果再回调处理逻辑
    ///         });
    /// ]]>
    /// </summary>
    public class ThreadWorker
    {
        private static ConcurrentQueue<Task> allTasks = new ConcurrentQueue<Task>();
        private static Task curTask;
        private static Task mtask = new Task(new Action(ThreadWorker.Start));
        private static AutoResetEvent sync = new AutoResetEvent(true);

        static ThreadWorker()
        {
            mtask.Start();



        }

        /// <summary>
        /// 构建线程异常信息
        /// </summary>
        /// <param name="ex"></param>
        /// <param name="strBuilder"></param>
        internal static void BuildExceptionMessage(Exception ex, StringBuilder strBuilder)
        {
            if (ex != null)
            {
                if (!ex.Message.IsEmpty())
                {
                    strBuilder.AppendLine(ex.Message);
                }
                BuildExceptionMessage(ex.InnerException, strBuilder);
            }
        }

        /// <summary>
        /// 推送一条异步任务
        /// </summary>
        /// <param name="action"></param>
        /// <param name="callback"></param>
        public static void QuequeTask(Action action, Action<AsynResult> callback)
        {
            Task item = new Task(action);
            item.ContinueWith(t => callback?.Invoke(AsynResult.CreateUnsuccess(t.Exception)), TaskContinuationOptions.NotOnRanToCompletion);
            item.ContinueWith(t => callback?.Invoke(AsynResult.CreateSuccess("")), TaskContinuationOptions.OnlyOnRanToCompletion);
            allTasks.Enqueue(item);
            sync.Set();
        }

        /// <summary>
        /// 推送一个自定义的异步任务
        /// </summary>
        /// <param name="task"></param>
        /// <param name="callback"></param>
        public static void QuequeTask(Task task, Action<AsynResult> callback)
        {
            if (task == null) return;
            task.ContinueWith(t => callback?.Invoke(AsynResult.CreateUnsuccess(t.Exception, (task.AsyncState as TaskAsyncState)?.ReturnResult)), TaskContinuationOptions.NotOnRanToCompletion);
            task.ContinueWith(t => callback?.Invoke(AsynResult.CreateSuccess("", (task.AsyncState as TaskAsyncState)?.ReturnResult)), TaskContinuationOptions.OnlyOnRanToCompletion);
            allTasks.Enqueue(task);
            sync.Set();
        }

        /// <summary>
        /// 推送一个异步任务，回调函数可以接受到一个上下文参数数组
        /// </summary>
        /// <param name="action"></param>
        /// <param name="asyncState"></param>
        /// <param name="callback"></param>
        public static void QuequeTask(Action<object> action, TaskAsyncState asyncState, Action<AsynResult> callback)
        {
            Task task = new Task(action, asyncState, TaskCreationOptions.LongRunning | TaskCreationOptions.PreferFairness);
            QuequeTask(task, callback);
        }

        private static void Start()
        {
            while (true)
            {
                while (allTasks.Count == 0)
                {
                    sync.WaitOne();
                }
                if (allTasks.TryDequeue(out curTask))
                {
                    // WriteLogToFile($"正在执行：{curTask?.ToJson()}");

                    curTask.Start();
                    //curTask = null;

                    WriteLogToFile($"剩余队列：{allTasks.Count}");
                }
            }
        }


        public static void WriteLogToFile(string message)
        {
            try
            {
                string fileName = "ThreadWorker";

                var files = new FileSystemVirtualPathProvider(HostContext.AppHost, HostContext.Config.WebHostPhysicalPath);
                string path = "app_data/debuglog/" + DateTime.Now.ToString("yyyy-MM-dd") + "/" + fileName + ".txt";

                StringBuilder content = new StringBuilder();
                content.AppendLine("时间:" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"));
                content.AppendLine("内容:" + message);
                content.AppendLine();

                if (!files.FileExists(path))
                {
                    files.WriteFile(path, content.ToString());
                }
                else
                {
                    files.AppendFile(path, content.ToString());
                }
            }
            catch (Exception ex)
            {
                WinEventUtil.WriteError(ex.Message ?? "线程执行错误", ex, 6003);
            }
        }
    }

    /// <summary>
    /// 线程数据结构
    /// </summary>
    public class TaskAsyncState
    {
        /// <summary>
        /// 连接上下文
        /// </summary>
        public UserContext UserContext { get; set; }
        /// <summary>
        /// 业务参数
        /// </summary>
        public object BizParameter { get; set; }

        /// <summary>
        /// 服务返回值
        /// </summary>
        public object ReturnResult { get; set; }
    }

    /// <summary>
    /// 线程执行结果
    /// </summary>
    public class AsynResult
    {
        internal static AsynResult CreateSuccess(string msg = "", object returnResult = null) =>
            new AsynResult
            {
                Message = msg,
                Success = true,
                ReturnResult = returnResult
            };

        internal static AsynResult CreateUnsuccess(System.Exception ex, object returnResult = null) =>
            new AsynResult
            {
                Exception = ex,
                Message = ex.Message,
                Success = false,
                ReturnResult = returnResult
            };

        /// <summary>
        /// 内部异常
        /// </summary>
        public System.Exception Exception { get; set; }

        /// <summary>
        /// 处理异常
        /// </summary>
        public void HandleError()
        {
            if (this.Success == false)
            {
                WinEventUtil.WriteError(this.Message ?? "线程执行错误", this.Exception, 6003);
            }
        }

        /// <summary>
        /// 消息
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 是否成功
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// 执行结束的方法输出
        /// </summary>
        public object ReturnResult { get; set; }
    }



}
