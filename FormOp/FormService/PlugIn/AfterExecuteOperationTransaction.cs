using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.Framework.MetaCore.FormOp.FormService.PlugIn
{
    public class AfterExecuteOperationTransaction:OperationTransactionArgs
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="dataEntities"></param>
        public AfterExecuteOperationTransaction(DynamicObject[] dataEntities)
            :base(dataEntities)
        {
        }
    }
}
