
> short description of the bug / issue, provide more detail below.

====================================================================


###### `[  paste a link to an example (demo or active link) of the issue here ]`  

Your issue may be closed without active code to debug. Options include jsfiddle, jsbin, codepen, dropbox, etc. with code highlighting the specific issue you are having


====================================================================


#### Steps to reproduce the problem

1. ...  
2. ...  


====================================================================


#### What is the expected behaviour?

...  


====================================================================


#### What is the observed behaviour?

...  


====================================================================

#### When you set `debug:true` and inspect the iframe, what do you see?

...  


====================================================================

#### More Details

- Which browsers/versions does it happen on?
- Which printThis version are you using?
- Did this work before?




