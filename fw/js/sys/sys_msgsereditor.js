///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/sys/sys_msgsereditor.js
*/
; (function () {
    var sys_msgsereditor = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        //继承 BasePlugIn
        __extends(_child, _super);

        //控件初始化之后
        _child.prototype.onInitialized = function (e) {
            var that = this;

            //当前页面上所有字段的可用性 = 父页面字段的可用性
            var cp = that.formContext.cp;
            if (cp && cp.enable === false) {
                var fields = that.Model.uiForm.getAllFields();
                if (fields) {
                    for (var i = 0; i < fields.length; i++) {
                        that.Model.setEnable({ id: fields[i], value: cp.enable });
                    }
                }
            }
            that.initShowLinkUrl(true);
        };

        //字段值改变事件
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fbizobject':
                    that.initShowLinkUrl(false);
                    break;
                case 'fmsgtype':
                    that.initShowLinkUrl(false);
                    break;
            }
        };

        //处理表单上包含 opCode 属性元素的点击事件
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                //确定
                case 'confirm':
                    e.result = true;
                    if (!that.Model.validForm()) return;
                    var cp = that.formContext.cp;
                    var parentVM = Index.getPage(cp.parentPageId);
                    if (parentVM) {

                        // 如果没有选择“站内消息”，要默认选择
                        var msgType = that.Model.getValue({ id: 'fmsgtype' });
                        if (msgType.id.indexOf("ew_message001") === -1) {
                            var isEmpty = msgType.id === "";
                            var comboData = that.Model.viewModel.uiComboData.fmsgtype;
                            var newValue = msgType;
                            for (var i = 0; i < comboData.length; i++) {
                                if (comboData[i].id === "ew_message001") {
                                    newValue.id = comboData[i].id + (isEmpty ? '' : ',') + newValue.id;
                                    newValue.name = comboData[i].name + (isEmpty ? '' : ',') + newValue.name;
                                    break;
                                }
                            }

                            that.Model.setValue({ id: 'fmsgtype', value: newValue.id });
                        }

                        var uiData = that.Model.clone();
                        if (!$.trim(uiData.fsendobjyg.id) && !$.trim(uiData.fsendobjjs.id) && !$.trim(uiData.fsendobjbl.id)) {
                            yiDialog.error('发送对象(员工)、发送对象(角色)、发送对象(变量)不能同时为空！');
                            return;
                        }
                        //删除非存储字段
                        delete uiData.id;
                        delete uiData.parentPageId;
                        delete uiData.paramFormId;
                        delete uiData.rowId;
                        delete uiData.fieldKey;
                        //回填服务配置信息
                        parentVM.Model.setValue({
                            id: cp.fieldKey,
                            row: cp.rowId,
                            value: {
                                id: JSON.stringify(uiData),
                                name: '消息服务配置'
                            }
                        });
                    }
                    //关闭对话框
                    that.Model.close();
                    break;
            }
        };

        _child.prototype.initShowLinkUrl = function (isFirstInit) {
            var that = this;
            var msgtype = that.Model.getSimpleValue({ id: "fmsgtype" });
            var bizformid = that.Model.getSimpleValue({ id: "fbizobject" });
            if (msgtype.split(',').indexOf('ew_message003') > -1) {
                that.Model.setVisible({ id: '.info-flinkurl', value: true });
                if (!isFirstInit) {
                    var url = '';
                    switch (bizformid) {
                        case 'ydj_customerrecord':
                            url = 'page/customerManage/pages/business/detail/detail?id={fid}';//商机详情
                            break;
                        case 'ydj_customer':
                            url = 'page/customerManage/pages/customer/detail/detail?id={fid}';//客户详情
                            break;
                        case 'coo_incomedisburse':
                            url = 'page/saleManage/pages/workbill/detail/detail?id={fid}';//收款单详情
                            break;
                        case 'ydj_order':
                            url = 'page/productManage/pages/contract/detail/detail?id={fid}';//销售合同详情
                            break;
                        case 'ydj_service':
                            url = 'page/serviceManage/pages/service/detail/detail?id={fid}';//服务单详情
                            break;
                        case 'ste_afterfeedback':
                            url = 'page/serviceManage/pages/afterSale/detail/detail?id={fid}';//售后反馈单单详情
                            break;
                        default:
                    }
                    that.Model.setValue({ id: 'flinkurl', value: url });
                }
            }
            else {
                that.Model.setValue({ id: 'flinkurl', value: '' });
                that.Model.setVisible({ id: '.info-flinkurl', value: false });
            }
        }
        return _child;
    })(BasePlugIn);
    window.sys_msgsereditor = window.sys_msgsereditor || sys_msgsereditor;
})();