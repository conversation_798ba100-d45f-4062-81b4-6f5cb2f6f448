
 /// @ sourceURL=/fw/js/sys/sys_linkformsearch.js
; (function () {
    var sys_linkformsearch = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);
        };
        
        //继承 BasePlugIn
        __extends(_child, _super);
        
        _child.prototype.onInitialized=function(){
            var that = this;
            //请求左侧菜单的数据
            var parentViewModel = Index.getPage(that.Model.viewModel.parentPageId);
        	var url = '/bill/{0}?operationno=Linkformsearch&pageid={1}'.format( parentViewModel.formId, parentViewModel.pageId);
            var params = { selectedRows: [{PKValue:that.Model.uiData.LinkformsearchPkid}]};
            //在页面层面显示一个遮罩层
            that.Model.blockUI({ id: '#page#' });
            yiAjax.p(url, params, function (r) {
                var isSucess = r.operationResult.isSuccess,
                    srvData = r.operationResult.srvData;
                if(isSucess)
                    //隐藏遮罩层
                    that.Model.unblockUI({ id: '#page#' });
                    if(srvData.length > 0)
                        var nextFormData = [],
                            preFormData = [];
                        for(var i=0,j=srvData.length;i<j;i++){
                            switch(srvData[i].flag){
                                case 'nextForm':
                                    nextFormData.push({'formId':srvData[i].formId,'formCaption':srvData[i].formCaption,'filterString':srvData[i].filterString,'count':srvData[i].count});
                                    break;
                                case 'preForm':
                                    preFormData.push({'formId':srvData[i].formId,'formCaption':srvData[i].formCaption,'filterString':srvData[i].filterString,'count':srvData[i].count});
                                    break;
                            }
                        }
                        var nextForm = '';
                        for(var i=0,j=nextFormData.length;i<j;i++){
                            nextForm += '<li class="link-item" formid="{0}" filter="{2}">{1}({3})</li>'.format(nextFormData[i].formId,nextFormData[i].formCaption,nextFormData[i].filterString,nextFormData[i].count)
                        }
                        that.Model.setHtml({id:'.link-next-nav',value:nextForm == '' ? '<li class="notfound">无下游单据</li>' : nextForm});
                        var preForm = '';
                        for(var i=0,j=preFormData.length;i<j;i++){
                            preForm += '<li class="link-item" formid="{0}" filter="{2}">{1}({3})</li>'.format(preFormData[i].formId,preFormData[i].formCaption,preFormData[i].filterString,preFormData[i].count)
                        }
                        that.Model.setHtml({id:'.link-pre-nav',value:preForm == '' ? '<li class="notfound">无上游单据</li>' : preForm});
            }, null, null);
        	that.initEventClick();
        	
        }

        //表单元素被单击后
        _child.prototype.initEventClick = function (e) {
            var that = this;
            $("#"+that.formContext.pageId+" .link-left").on('click','li',function(){
                if($(this).hasClass("notfound")){
                    return;
                }
                var formId = $(this).attr('formid'),
                    filter = $(this).attr('filter');
                $("#"+that.formContext.pageId+" .link-left").find("li").removeClass("active");
                $(this).addClass("active");
                that.Model.showList({
                    formId: formId,
                    domainType: "list",
                    openStyle: Consts.openStyle.inContainer,
                    param: {
                        filterString: filter,
                        containerId: 'link-right' 
                    }
                });
            });
        }

        return _child;
    })(BasePlugIn);
    window.sys_linkformsearch = window.sys_linkformsearch || sys_linkformsearch;
})();
