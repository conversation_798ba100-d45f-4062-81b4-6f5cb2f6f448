/*
表达式函数库
@ sourceURL=/fw/js/platform/mvvm/expr/exprFuncLib.js
*/
; (function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ?
        module.exports = factory() : typeof define === 'function' && define.amd ?
            define(factory) : (global.exprFuncLib = factory());
}(this, (function () {
    'use strict';

    //构造函数
    function ListFunc() {

    }

    //静态方法：汇总指定数组中的数值
    ListFunc.sum = function (items) {
        var sum = 0;
        if (Array.isArray(items)) {
            for (var i = 0; i < items.length; i++) {
                sum += items[i];
            }
        }
        return sum;
    };

    //静态方法：根据指定的表达式汇总指定数组中的数值
    ListFunc.sumexpr = function (items, expr) {
        if (!exprEval) {
            throw new Error("此方法依赖 expr-eval 计算组件！");
        }
        var sum = 0;
        if (Array.isArray(items)) {
            for (var i = 0; i < items.length; i++) {
                sum += exprEval.Parser.evaluate(expr, items[i]);
            }
        }
        return sum;
    };

    //静态方法：获取单据类型参数
    ListFunc.getBillTypeParam = function (billTypeId, propName) {
        var value = null;
        billTypeId = $.trim(billTypeId);
        propName = $.trim(propName);
        if (billTypeId && propName) {
            var url = '/bill/bd_billtype?operationno=getparamset&billtypeid=' + billTypeId;
            yiAjax.g(url, '', function (r) {
                var result = r && r.operationResult;
                if (result && result.isSuccess) {
                    var srvData = result.srvData;
                    if (srvData && srvData[propName] !== undefined) {
                        value = srvData[propName];
                    }
                }
            }, null, null, null, { async: false });
        }
        return value;
    };

    //静态方法：获取商品默认辅助属性值
    ListFunc.getAuxPropValue = function (productId) {
        var value = { id: '', name: '', fentity: [] };
        productId = $.trim(productId);
        if (!productId) return value;

        //先从临时缓存区获取（商品基础资料批量填充时存储的数据）
        var tempMtls = window._tempMaterials;
        if (tempMtls && tempMtls.length > 0) {
            for (var i = 0; i < tempMtls.length; i++) {
                var tempMtl = tempMtls[i];
                if (tempMtl && tempMtl.id === productId) {
                    value.fentity = $.extend(true, [], tempMtl.defAuxProp);
                    //匹配后移除掉，以减少下次的匹配次数
                    tempMtls.splice(i, 1);
                    return value;
                }
            }
        }

        //请求商品的辅助属性
        var url = '/dynamic/bd_auxpropvaluemap?operationno=getauxpropvalues&materialId=' + productId;
        yiAjax.g(url, '', function (r) {
            var result = r && r.operationResult;
            var srvData = result && result.srvData;
            if (!srvData || srvData.length < 1 || !result.isSuccess) return;

            //此处只传了一个商品Id到服务端，所以服务端只会返回一个指定商品对应的辅助属性值列表
            var auxProps = srvData[0].auxPropInfo;
            if (!auxProps) return;

            for (var i = 0; i < auxProps.length; i++) {
                var auxProp = auxProps[i];
                var valueList = auxProp && auxProp.valueList;
                if (!valueList) continue;

                for (var j = 0; j < valueList.length; j++) {

                    //封装默认的辅助属性值集
                    var auxVal = valueList[j];
                    if (!auxVal || !auxVal.isDefVal) continue;

                    value.fentity.push({
                        fvalueid: auxVal.valueId,
                        fvaluename: auxVal.valueName,
                        fvaluenumber: auxVal.valueNumber,
                        fauxpropid: {
                            id: auxProp.auxPropId,
                            fname: auxProp.auxPropName,
                            fnumber: auxProp.auxPropNumber
                        }
                    });
                }
            }
        }, null, null, null, { async: false });

        return value;
    };

    //静态方法：获取商品图片
    ListFunc.getImages = function (productId, attrInfo, customDesc) {
        var value = '';
        productId = $.trim(productId);
        if (!productId) return value;

        var entities = [];
        var entrys = attrInfo && attrInfo.fentity;
        if (entrys) {
            for (var i = 0; i < entrys.length; i++) {
                entities.push({
                    auxPropId: entrys[i].fauxpropid.id,
                    valueId: entrys[i].fvalueid
                });
            }
        }

        var url = '/dynamic/ydj_product?operationno=getimages';
        var param = {
            selectedrows: [{ PKValue: productId }],
            simpleData: {
                attrInfo: JSON.stringify({ entities: entities }),
                customDesc: $.trim(customDesc)
            }
        };

        yiAjax.p(url, param, function (r) {
            var result = r && r.operationResult;
            var simpleData = result && result.simpleData;
            var imageIds = simpleData && simpleData.imageIds;
            if (imageIds && result.isSuccess) {
                value = { id: imageIds };
            }
        }, null, null, null, { async: false });

        return value;
    };

    return { ListFunc: ListFunc };

})));