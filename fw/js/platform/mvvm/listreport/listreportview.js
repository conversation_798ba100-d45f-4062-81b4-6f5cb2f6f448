///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/platform/mvvm/listreport/listreportview.js
*/
; (function () {
    var ListReportView = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);

            //构造函数中定义实例自身的属性，比如每个实例不一样的属性，应该在此处定义

            //获取列表数据的 url
            that.queryDataUrl = '/listreport/{0}?operationno=querydata&pageid={1}'.format(that.formId, that.pageId);
        };
        //继承父类
        __extends(_child, _super);

        /*
            以下定义当前类自身特有的成员：
            在原型中定义所有实例共享的成员（属性或方法），原则上所有方法都应该定义在原型上（因为每个实例的方法都是一样的），以便复用
        */

        //重建与视图匹配的模型实例
        _child.prototype.createModel = function () {
            return new ListReportModel({ viewModel: this });
        };

        /*
            处理列表行双击事件
            参数(args)结构：
                |--id:list 固定值
                |--row: 行号
                |--fieldId:双击的单元格列标识
                |--data:双击的行数据对象
                |--listMode:列表模式
                |--e:点击事件参数对象
                |--result:布尔，=true时表示由派生类接管，父类不再处理。
            返回值：
                |--无
        */
        _child.prototype.processListDblClickAction = function (args) {
            var that = this;
            that.plugInProxy.invoke(eventConst.onEntryRowDblClick, args);

            //始终屏蔽基类处理双击事件，但允许插件得到事件通知
            //不是“查找返回模式”才屏蔽
            if (that.listMode.toLowerCase() !== 'lookup') {
                args.result = true;
            }
        };

        return _child;
    })(ListView);
    window.ListReportView = window.ListReportView || ListReportView;
})();