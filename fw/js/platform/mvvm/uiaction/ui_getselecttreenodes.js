/**
 * @file ui_getselecttreenodes.js
 * @desc 实现模型与视图的双向绑定指令，由宿主viewModel调用
 *
 * *************************
 * 约束：必须实现一个updateView与updateModel的方法
 * *************************
 */
; var ui_getselecttreenodes = {

    // 根据传入的模型数据更新视图
    updateView: function (args) {
        if (!args || !args.id) { return; }
        var treeId = args.viewModel.getTreeId(args.id),
            treeObj = $.fn.zTree.getZTreeObj(treeId),
            nodes = treeObj.getSelectedNodes();
        return nodes;
    },

    // 根据视图的变化更新模型
    updateModel: function (args) {

    }
};