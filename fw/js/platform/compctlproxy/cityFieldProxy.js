///<reference path="/fw/js/platform/platformconst.js" />
/*
省市区字段代理，继承至字段代理
@ sourceURL=/fw/js/platform/compctlproxy/CityFieldProxy.js
*/
; (function () {
    var _proxy = function (_super) {
        var _child = function _child(args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        /**
         * @description 初始化视图控件
         * @param {object} args
         */
        _child.prototype.initView = function (args) {
            var that = this;

            //字段是否在表格中
            if (!that.isInGrid) {

                //查找控件元素（后续改成根据字段模型自动创建元素）
                var fieldId = '';
                var cityKeys = ['province', 'city', 'region'];
                for (var i = 0, l = cityKeys.length; i < l; i++) {
                    var field = that.uiField[cityKeys[i]];
                    if (field) {
                        fieldId = field.id;
                        var fieldEl = $('#' + that.pageId).find('[name=' + fieldId + ']');
                        if (fieldEl.length > 0) {
                            that.uiElement = fieldEl;
                            break;
                        }
                    }
                }
                if (!that.uiElement || that.uiElement.length <= 0 || that.uiElement.attr('type') !== 'city') return;

                that.uiElement.attr({
                    id: '{0}_{1}'.format(that.pageId, fieldId),
                    name: fieldId
                });

                //因为citypicker省市区控件的赋值，需要在初始化控件的时候就赋值；
                var _province = (that.uiValue.province && that.uiValue.province.fname) ? that.uiValue.province.fname : '';
                var _city = (that.uiValue.city && that.uiValue.city.fname) ? that.uiValue.city.fname : '';
                var _region = (that.uiValue.region && that.uiValue.region.fname) ? that.uiValue.region.fname : '';

                that.uiElement.city({
                    province: _province,
                    city: _city,
                    district: _region,
                    change: function (e) {
                        var pField = that.uiElement.attr('province'),
                            cField = that.uiElement.attr('city'),
                            rField = that.uiElement.attr('region'),
                            province = { id: '', fname: '', fnumber: '' },
                            city = { id: '', fname: '', fnumber: '' },
                            region = { id: '', fname: '', fnumber: '' },
                            _vals, _txts;
                        if (e.vals) {
                            _vals = e.vals.split('/');
                            _txts = e.txts.split('/');
                            if (_vals.length > 0) {
                                province.id = _vals[0];
                                province.fname = _txts[0];
                                province.fnumber = _txts[0];
                                province.fenumitem = _txts[0];
                            }
                            if (_vals.length > 1) {
                                city.id = _vals[1];
                                city.fname = _txts[1];
                                city.fnumber = _txts[1];
                                city.fenumitem = _txts[1];
                            }
                            if (_vals.length > 2) {
                                region.id = _vals[2];
                                region.fname = _txts[2];
                                region.fnumber = _txts[2];
                                region.fenumitem = _txts[2];
                            }
                        }
                        //选择类型（省，市，区）
                        switch (e.stype) {
                            case 'city':
                                //选择市，需要更新 市，区
                                that.onFieldValueChanged({ id: cField, row: -1, value: city, oldvalue: {} });
                                that.onFieldValueChanged({ id: rField, row: -1, value: region, oldvalue: {} });
                                break;
                            case 'district':
                                //选择区，则只需要更新区
                                that.onFieldValueChanged({ id: rField, row: -1, value: region, oldvalue: {} });
                                break;
                            default:
                                //否则，更新 省，市，区
                                that.onFieldValueChanged({ id: pField, row: -1, value: province, oldvalue: {} });
                                that.onFieldValueChanged({ id: cField, row: -1, value: city, oldvalue: {} });
                                that.onFieldValueChanged({ id: rField, row: -1, value: region, oldvalue: {} });
                                break;
                        }
                    }
                });
            }
        };

        /**
         * @description 获取当前控件的值：从 UI 元素上获取控件值
         * @param {object} args
         * @returns {object} 对象
         */
        _child.prototype.getValue = function (args) {
            var that = this;
            var value = {
                province: { id: '', name: '' },
                city: { id: '', name: '' },
                region: { id: '', name: '' }
            };
            //字段是否在表格中
            if (that.isInGrid) {
                //如果单元格处于编辑状态，则更新控件值
                if (that.uiElement && that.uiElement.length > 0) {
                    value = that.uiElement.city('getValue');
                } else {
                    //获取单元格值
                    value = that.getCellValue(args);
                }
            } else {
                value = that.uiElement.city('getValue');
            }
            return value;
        };

        /**
         * @description 设置当前控件的值：将实际字段值更新至 UI 元素上
         * @param {object} args
         */
        _child.prototype.setValue = function (args) {
            var that = this;
            //字段是否在表格中
            if (that.isInGrid) {
                //如果单元格处于编辑状态，则更新控件值
                if (that.uiElement && that.uiElement.length > 0) {
                    that.uiElement.city('setValue', args);
                } else {
                    //更新单元格值
                    that.setCellValue(args);
                }
            } else {
                that.uiElement.city('setValue', args);
            }
        };

        /**
         * @description 设置当前控件的可用性
         * @param {boolean} args
         */
        _child.prototype.setEnable = function (args) {
            this.uiElement && this.uiElement.city('setEnable', args);
        };

        /**
         * @description 设置当前控件的可用性
         */
        _child.prototype.getEnable = function () {
            if (this.uiElement) {
                return this.uiElement.city('getEnable');
            }
            return false;
        };

        return _child;
    }(window.ydj.ctlProxy.FieldCtlProxy);

    window.ydj.ctlProxy.CityFieldProxy = _proxy;
})();