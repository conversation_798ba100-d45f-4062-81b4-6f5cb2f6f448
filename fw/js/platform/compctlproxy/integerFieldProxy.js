///<reference path="/fw/js/platform/platformconst.js" />
/*
整数字段代理，继承至小数字段代理
@ sourceURL=/fw/js/platform/compctlproxy/integerFieldProxy.js
*/
; (function () {
    var _proxy = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        /**
         * @description 表单字段值变化时触发：通常由控件值变化来触发此事件，完成从 UI 向 Model 的数据传递过程
         * @param {object} args 
         */
        _child.prototype.onFieldValueChanged = function (args) {
            var that = this;
            args.value = yiMath.toInt(yiCommon.parser(args.value, true));

            //此处直接通过事件总线外业务层抛
            that.uiEventRouter.dispatchEvent(eventConst.onFieldValueChanged, args);
            that.uiValue = args.value;

            //将合法的值重新设值给控件
            that.setValue({ value: args.value });
        };

        /**
         * @description 格式化字段显示值
         * @param {object} args
         */
        _child.prototype.formatDisplayValue = function (args) {
            var that = this;
            var value = $.trim(args.value) || 0;
            return value;
        };

        return _child;
    })(window.ydj.ctlProxy.FieldProxy_102);

    window.ydj.ctlProxy.FieldProxy_101 = _proxy;
})();