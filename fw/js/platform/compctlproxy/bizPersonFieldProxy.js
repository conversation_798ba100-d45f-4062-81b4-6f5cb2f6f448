/*
业务对象字段代理，继承至字段代理
@ sourceURL=/fw/js/platform/compctlproxy/bizPersonFieldProxy.js
*/
;(function () {
    var _proxy = function (_super) {
        var _child = function _child(args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);

        /*字段代理方法开始****************************************************************************************************************************************************************************************************/

        /**
         * @description 初始化视图控件
         * @param {object} args
         */
        _child.prototype.initView = function (args) {
            var that = this;

            //控件选项定义
            var ctlField = that.uiField.controlFieldKey;
            var bizFormId = that.uiFormProxy.viewModel.Model.getValue({id:ctlField,row:that.row}).id;
            var options = {
                domainType: that.domainType,
                formId: that.formId,
                pageId: that.pageId,
                fieldKey: that.uiField.id,
                flexRow: that.row,
                enable: true,
                isBillHeader: false,
                bizFormId: bizFormId ? bizFormId : '',
                onChange: function (e) { 
                    //表单字段值变化事件
                    that.onFieldValueChanged({
                        id: e.name,
                        value: e.newValue,
                        oldvalue: e.oldValue,
                        row: e.row,
                        prow: that.getParentRow()
                    });
                }
            };

            //字段是否在表格中渲染
            if (that.isInGrid) {

                //创建控件元素
                that.uiElement = $('<input type="bizperson" class="y-editor-text" />');

                var $elwrap = $('<div style="position: relative;"></div>');

                that.uiElement.appendTo($elwrap);

                $elwrap.appendTo(args.container);

                options.isBillHeader = false;

            } else {

                //查找控件元素
                that.uiElement = $('#' + that.pageId).find('[name=' + that.uiField.id + ']');
                if (that.uiElement.length <= 0 && that.uiField.visible) {
                    //console.error('表头字段 ' + that.uiField.id + ' 在视图中未定义！');
                    return;
                }

                that.uiElement.attr({
                    id: '{0}_{1}'.format(that.pageId, that.uiField.id)
                });
                options.isBillHeader = true;
            }

            that.uiElement.attr({
                name: that.uiField.id
            });

            //初始化控件
            that.uiElement.bizperson(options);

            //设置控件值
            if (!that.isInGrid) {
                that.setValue({ value: that.uiValue });
            }
        };

        /**
         * @description 获取当前控件的值：从 UI 元素上获取控件值
         * @returns {object} 对象
         */
        _child.prototype.getValue = function (args) {
            var that = this;
            var value = {};
            //字段是否在表格中
            if (that.isInGrid) {
                //如果单元格处于编辑状态，则更新控件值
                if (that.uiElement && that.uiElement.length > 0) {
                    value = that.uiElement.bizperson('getValue');
                } else {
                    //获取单元格值
                    value = that.getCellValue(args);
                }
            } else {
                value = that.uiElement.bizperson('getValue');
            }
            return value;
        };

        /**
         * @description 设置当前控件的值：将实际字段值更新至 UI 元素上
         * @param {object} args
         */
        _child.prototype.setValue = function (args) {
            var that = this;
            that.uiValue = args.value;
            //字段是否在表格中
            if (that.isInGrid) {
                //如果单元格处于编辑状态，则更新控件值
                if (that.uiElement && that.uiElement.length > 0) {
                    that.uiElement.bizperson('setValue', args.value);
                } else {
                    //更新单元格值
                    that.setCellValue(args);
                }
            } else {
                that.uiElement.bizperson('setValue', args.value);
            }
        };

        /**
         * @description 格式化字段显示值
         * @param {object} args
         */
        _child.prototype.formatDisplayValue = function (args) {
            var that = this;
            if (that.isReadOnly) {
                return args.value;
            } else {
                return args.value.name;
            }
        };

        /**
         * @description 获取当前控件的可用性
         * @returns {boolean} 返回 true 或 false
         */
        _child.prototype.getEnable = function () {
            return this.uiElement.bizperson('getEnable');
        };

        /**
         * @description 设置当前控件的可用性
         * @param {boolean} args
         */
        _child.prototype.setEnable = function (args) {
            if(this.uiElement){
                this.uiElement.bizperson('setEnable', args);
            }
        };

        /**
         * @description 销毁当前控件
         */
        _child.prototype.destroy = function () {
            this.uiElement.bizperson('destroy');
            this.row = '';
        };
        
        return _child;
    }(window.ydj.ctlProxy.FieldCtlProxy);

    window.ydj.ctlProxy.FieldProxy_157 = _proxy;
})();