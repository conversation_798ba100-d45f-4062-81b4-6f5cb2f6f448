; (function () {
    var Lookauditlog = (function () {
        return function (args) {

            if (!args.pkids || args.pkids.length <= 0) {
                yiDialog.warn('请选择一行数据！');
                return;
            }

            if (args.pkids.length > 1) {
                yiDialog.warn('只能选择一行数据进行查看！');
                return;
            }

            if (args.domainType.toLowerCase() === Consts.domainType.bill && !$.trim(args.pkids[0])) {
                yiDialog.warn('请先保存后再执行该操作！');
                return;
            }

            //地址
            var url = '/{0}/{1}?operationno=lookauditlog&pageid={2}'.format(args.domainType, args.formId, args.pageId);

            //传递到服务端的主键ID数组
            var params = { selectedRows: [], simpleData: args.param };
            for (var i = 0; i < args.pkids.length; i++) {
                params.selectedRows.push({ PKValue: args.pkids[i] });
            }

            //请求查看审批日志
            yiAjax.p(url, params, function (r) {

                //成功后回调函数
                if (args.extra && $.isFunction(args.extra.success)) {
                    args.extra.success(r);
                }

            }, null, null, $(args.pageSelector));
        };
    })();
    window.Lookauditlog = window.Lookauditlog || Lookauditlog;
})();