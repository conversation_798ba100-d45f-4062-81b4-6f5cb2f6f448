/// <reference path="../../consts.js" />
/*
@ sourceURL=/fw/js/platform/serveraction/showform.js
*/
; (function () {
    var Showform = (function () {

        //参数：response, actionParam, extra
        return function (r, ap, e) {
            var that = this;
            e = e || {};

            var fsp = ap.fsp;
            fsp.pi = ap.pi;

            //模态对话框打开
            that.modalShowForm = function (r, fsp, e) {
                dialogShowForm(r, fsp, e, true);
            };

            //模态对话框打开
            that.nonmodalShowForm = function (r, fsp, e) {
                dialogShowForm(r, fsp, e, false);
            };

            //默认页签打开
            that.defaultShowForm = function (r, fsp, e) {

                TabMgr.getInstance().open({
                    title: fsp.formCaption,
                    pageId: fsp.pageId,
                    cp: e.cp,
                    callback: function (panel) {

                        //创建页面实例所需的参数
                        var opts = $.extend(true, {}, getCreatePageInstanceOpts(r, fsp), { panel: panel, cp: e && e.cp ? e.cp : null });

                        //加载页面 js 文件
                        loadPageJs(opts, function (plugIns) {

                            //创建页面实例
                            var pageObj = createPageInstance(opts);

                            //获取页面 html 内容
                            getPageHtml(opts, pageObj, function (html) {

                                //将页面中的占位符 {0} 替换成 pageId
                                html = html.format(pageObj.pageId);

                                //获取 bill.html 文件内容
                                getBillHtml(opts, pageObj.pageId, html, function (billHtml) {
                                    try {
                                        //加载 tab 页签 html
                                        opts.panel.html(billHtml);

                                        //初始化页面插件代理
                                        var plugProxy = new PlugInProxy({ plugIns: plugIns });

                                        //初始化页面
                                        pageObj.init({ "plugInProxy": plugProxy });
                                    }
                                    catch (e) {
                                        yiDialog.m({ msg: "页面初始化错误：{0},{1};错误位置：".format(e.name, e.message, e.stack) });
                                    }
                                    finally {
                                        //管理 tab 页签容器
                                        TabMgr.getInstance().manage(pageObj);
                                    }
                                });
                            })
                        });
                    }
                });
            };

            //在指定的容器中打开
            that.incontainerShowForm = function (r, fsp, e) {
                if (!fsp.containerId) {
                    yiDialog.error('指定的 containerId 为空！');
                    return;
                }
                var creaMes = getCreatePageInstanceOpts(r, fsp);
                var container = null;
                if (fsp.containerId.substr(0, 1) === '#') {
                    container = $(fsp.containerId, '#' + creaMes.parentPageId);
                } else {
                    container = $('[y-sumcontent=' + fsp.containerId + ']', '#' + creaMes.parentPageId);
                }
                if (!container) {
                    yiDialog.error('指定的页面容器不存在！');
                    return;
                }

                //创建页面实例所需的参数
                var opts = $.extend(true, {}, getCreatePageInstanceOpts(r, fsp), { panel: container, cp: e && e.cp ? e.cp : null });

                //创建页面实例
                var pageObj = createPageInstance(opts);

                //获取页面 html 内容
                getPageHtml(opts, pageObj, function (r) {

                    //将页面中的占位符 {0} 替换成 pageId
                    r = r.format(pageObj.pageId);

                    //获取 bill.html 文件内容
                    getBillHtml(opts, pageObj.pageId, r, function (billHtml) {

                        //加载页面 js 文件
                        loadPageJs(opts, function (plugIns) {

                            try {

                                //将页面中的占位符 {0} 替换成 pageId
                                r = r.format(pageObj.pageId);

                                //将页面 html 内容加载到指定容器中
                                opts.panel.html('<div id="{0}">{1}</div>'.format(opts.pageId, billHtml));

                                //初始化页面插件代理
                                var plugProxy = new PlugInProxy({ plugIns: plugIns });

                                //初始化页面
                                pageObj.init({ "plugInProxy": plugProxy });

                                //如果当前页面是自定义过滤页面，则将当前页面pageId记录在父页面(列表页面)中
                                if (opts.parentPageId) {
                                    var parentPage = Index.getPage(opts.parentPageId);
                                    if (parentPage && $.isFunction(parentPage.initCustomFilterPageId)) {
                                        parentPage.initCustomFilterPageId({
                                            pageId: opts.pageId,
                                            formId: opts.formId
                                        });
                                    }
                                }

                                //如果是首页小组件，有滚动条的时候优化滚动条，使其变细小
                                //暂时先去掉,这个功能与小组件拖动互相影响，后续再看下其他插件或方法再补上
                                //if (opts.panel.hasClass('board-part-content')) {
                                //    Index.beautyScrollBar(opts.panel);
                                //}
                            }
                            catch (e) {
                                yiDialog.m({ msg: "页面初始化错误：{0},{1};错误位置：".format(e.name, e.message, e.stack) });
                            }
                            finally {
                                //记录页面对象
                                Index.addPage(pageObj);
                            }
                        });
                    })
                });
            };

            //侧边滑动弹出
            that.slidefromrightShowForm = function (r, fsp, e) {
                slideShowForm(r, fsp, e);
            }

            //如果 actionParam 不存在，则无需处理
            if (!fsp) { return; }

            //领域类型统一转成小写
            fsp.domainType = fsp.domainType.toLowerCase();
            fsp.openStyle = fsp.openStyle.toLowerCase();

            //根据 openStyle 来不同方式打开页面
            that[fsp.openStyle + 'ShowForm'](r, fsp, e);
        };

    })();

    //侧边滑动弹出
    function slideShowForm(r, fsp, e, isModal) {

        //创建页面实例所需的参数
        var opts = getCreatePageInstanceOpts(r, fsp);
        opts.cp = e && e.cp ? e.cp : null;

        //加载页面 js 文件
        loadPageJs(opts, function (plugIns) {
            opts.refId = e.refId;

            //创建页面实例
            var pageObj = createPageInstance(opts);
            pageObj.dbClickCallback = e.callback;
            pageObj.bdSelectMult = e.bdSelectMult;
            pageObj.bdSelectSubMult = e.bdSelectSubMult;

            //获取浏览器的高度，和宽度，仅仅一次，因为一次就够了，
            var docWid = $(window).width();
            var docHei = $(window).height();

            //获取页面 html 内容
            yiAjax.gf(opts.pi.pageViewFile, {}, function (html) {

                //将页面中的占位符 {0} 替换成 pageId
                html = html.format(pageObj.pageId);

                //获取 bill.html 文件内容
                getBillHtml(opts, pageObj.pageId, html, function (billHtml) {
                    var _width = '90%', _height = '80%';
                    var list_h = 0;
                    var list_w = $('#tab-content').outerWidth();
                    if (opts.uiMeta) {
                        if ($.trim(opts.uiMeta.width)) {
                            _width = opts.uiMeta.width;
                        }
                        if ($.trim(opts.uiMeta.height)) {
                            _height = opts.uiMeta.height;
                        }
                    }
                    if (fsp && fsp.formUserProfile && fsp.formUserProfile.formWidth) {
                        _width = fsp.formUserProfile.formWidth + '%';
                    }
                    if (fsp && fsp.formUserProfile && fsp.formUserProfile.formHeight) {
                        _height = fsp.formUserProfile.formHeight + '%';
                    }
                    _height = yiMath.toMoneyFormat((list_h / docHei), 2, 0) * 100 + 1 + '%';
                    _width = yiMath.toMoneyFormat((list_w * (2 / 3) / docWid), 2, 0) * 100 + 2 + '%';

                    var content = billHtml;
                    if (pageObj.domainType === Consts.domainType.bill) {
                        content = '<div class="dialog-page-content">' + billHtml + '</div>';
                    }

                    //显示对话框
                    yiDialog.d({
                        id: fsp.pageId,
                        type: 1,
                        shade: isModal ? 0.3 : 0,
                        content: content,
                        offset: 'rb',//为了从右边滑出  rb是从右下角滑出
                        title: fsp.formCaption,
                        scrollbar: false,
                        maxmin: true,
                        resize: false,
                        area: [_width, _height],
                        resizing: function (layero) {

                            //以百分比的数值保存在个性化信息里面。
                            var pageObj = Index.getPage(fsp.pageId);

                            //弹出框的高度和宽度
                            var wid = $(layero).width();
                            var hei = $(layero).height();

                            pageObj.formWidth = yiMath.toMoneyFormat(wid / docWid, 2, 0) * 100;
                            pageObj.formHeight = yiMath.toMoneyFormat(hei / docHei, 2, 0) * 100;
                            pageObj.resizeForm({ dialogResize: true, source: 'resizing' });

                        },
                        full: function (layero) {
                            var pageObj = Index.getPage(fsp.pageId);
                            pageObj.resizeForm({ dialogResize: true, source: 'full' });
                        },
                        min: function (layero) {

                        },
                        restore: function (layero) {
                            var pageObj = Index.getPage(fsp.pageId);
                            pageObj.resizeForm({ dialogResize: true, source: 'restore' });
                        },
                        success: function (layero, index) {

                            //为了从右边滑出
                            $(layero).siblings('.layui-layer-shade').remove();

                            //得到容器的宽度以及position的位置
                            var acLeft = $(layero).offset().left;
                            var releft = $(layero).offset().left + $(layero).width();

                            //将容器平移至最右边
                            $(layero).css({ left: releft });
                            $(layero).addClass('silde');

                            setTimeout(function () {
                                //平滑出来。
                                $(layero).css({ left: acLeft });
                            }, 400);

                            setTimeout(function () {
                                //去除滑动的样式，以免影响其他的效果
                                $(layero).removeClass('silde');
                            }, 2000);

                            try {

                                //初始化页面插件代理
                                var plugProxy = new PlugInProxy({ plugIns: plugIns });

                                //初始化页面
                                pageObj.init({ "plugInProxy": plugProxy });

                                //在页面对象中存放对话框标识，以便在模型的 close 方法中关闭对话框
                                pageObj.dialogIndex = index;
                            }
                            catch (e) {
                                yiDialog.m({ msg: "页面初始化错误：{0},{1};错误位置：".format(e.name, e.message, e.stack) });
                            }
                            finally {

                                //记录页面对象
                                Index.addPage(pageObj);
                            }
                        },
                        end: function () {

                            //个性化设置信息
                            var pageObj = Index.getPage(fsp.pageId);
                            var simpleData = pageObj && pageObj.getUserProfile();

                            //销毁页面对象
                            Index.disposePage(fsp.pageId);

                            //销毁对话框的同时也释放 pageId
                            yiAjax.p('/dynamic/{0}?operationno=close&pageId={1}'.format(fsp.formId, fsp.pageId), { simpleData: simpleData });
                        }
                    });
                });
            });
        });
    }

    //对话框打开
    function dialogShowForm(r, fsp, e, isModal) {

        //创建页面实例所需的参数
        var opts = getCreatePageInstanceOpts(r, fsp);
        opts.cp = e && e.cp ? e.cp : {};

        //加载页面 js 文件
        loadPageJs(opts, function (plugIns) {
            opts.refId = e.refId;

            //创建页面实例
            var pageObj = createPageInstance(opts);
            pageObj.dbClickCallback = e.callback;
            pageObj.maxReturnRows = 50;
            //if (e.maxReturnRows) {
            //    pageObj.maxReturnRows = e.maxReturnRows;
            //}
            pageObj.bdSelectMult = e.bdSelectMult;
            pageObj.bdSelectSubMult = e.bdSelectSubMult;

            //获取浏览器的高度，和宽度，仅仅一次，因为一次就够了，
            var docWid = $(window).width();
            var docHei = $(window).height();

            //如果启用了备选区，则请求对应的备选区列表页面文件
            var pageViewFile = opts.pi.pageViewFile;
            if (pageObj.enableBft) {
                if (pageObj.domainType === 'listtree') {
                    pageViewFile = opts.pi.pageViewFile.replace('listtree.html', 'listtree_bft.html');
                } else {
                    pageViewFile = opts.pi.pageViewFile.replace('list.html', 'list_bft.html');
                }
            }

            //获取页面 html 内容
            yiAjax.gf(pageViewFile, {}, function (html) {

                //将页面中的占位符 {0} 替换成 pageId
                html = html.format(pageObj.pageId);

                //获取 bill.html 文件内容
                getBillHtml(opts, pageObj.pageId, html, function (billHtml) {
                    var _width = '90%', _height = '80%', resize = true, maxmin = true;
                    if (opts.uiMeta) {
                        if ($.trim(opts.uiMeta.width)) {
                            _width = opts.uiMeta.width;
                        }
                        if ($.trim(opts.uiMeta.height)) {
                            _height = opts.uiMeta.height;
                        }
                        resize = opts.uiMeta.resize;
                        maxmin = opts.uiMeta.maxmin;
                    }
                    if (fsp && fsp.formUserProfile && fsp.formUserProfile.formWidth) {
                        _width = fsp.formUserProfile.formWidth + '%';
                    }
                    if (fsp && fsp.formUserProfile && fsp.formUserProfile.formHeight) {
                        _height = fsp.formUserProfile.formHeight + '%';
                    }

                    var content = billHtml;
                    if (pageObj.domainType === Consts.domainType.bill) {
                        content = '<div class="dialog-page-content">' + billHtml + '</div>';
                    }

                    //显示对话框
                    yiDialog.d({
                        id: fsp.pageId,
                        type: 1,
                        shade: isModal ? 0.3 : 0,
                        content: content,
                        title: fsp.formCaption,
                        scrollbar: true,
                        resize: resize,
                        maxmin: maxmin,
                        area: [_width, _height],
                        resizing: function (layero) {

                            //以百分比的数值保存在个性化信息里面。
                            var pageObj = Index.getPage(fsp.pageId);

                            //弹出框的高度和宽度
                            var wid = $(layero).width();
                            var hei = $(layero).height();

                            pageObj.formWidth = yiMath.toMoneyFormat(wid / docWid, 2, 0) * 100;
                            pageObj.formHeight = yiMath.toMoneyFormat(hei / docHei, 2, 0) * 100;
                            pageObj.resizeForm({ dialogResize: true, source: 'resizing' });
                        },
                        full: function (layero) {
                            var pageObj = Index.getPage(fsp.pageId);
                            pageObj.resizeForm({ dialogResize: true, source: 'full' });

                            if (pageObj.domainType === 'listtree' && pageObj.enableBft) {
                                //延时重新调用一次表单缩放逻辑，解决第一次调用后没有完全缩放的问题
                                var timer = setTimeout(function () {
                                    pageObj.resizeForm({ dialogResize: true, source: 'full' });
                                    clearTimeout(timer);
                                }, 5);
                            }
                        },
                        min: function (layero) {

                        },
                        restore: function (layero) {
                            var pageObj = Index.getPage(fsp.pageId);
                            pageObj.resizeForm({ dialogResize: true, source: 'restore' });

                            if (pageObj.domainType === 'listtree' && pageObj.enableBft) {
                                //延时重新调用一次表单缩放逻辑，解决第一次调用后没有完全缩放的问题
                                var timer = setTimeout(function () {
                                    pageObj.resizeForm({ dialogResize: true, source: 'restore' });
                                    clearTimeout(timer);
                                }, 5);
                            }
                        },
                        success: function (layero, index) {
                            try {

                                //初始化页面插件代理
                                var plugProxy = new PlugInProxy({ plugIns: plugIns });

                                //初始化页面
                                pageObj.init({ "plugInProxy": plugProxy });
                                pageObj.resizeForm();

                                //在页面对象中存放对话框标识，以便在模型的 close 方法中关闭对话框
                                pageObj.dialogIndex = index;
                            }
                            catch (e) {
                                yiDialog.m({ msg: "页面初始化错误：{0},{1};错误位置：".format(e.name, e.message, e.stack) });
                            }
                            finally {
                                //记录页面对象
                                Index.addPage(pageObj);
                            }
                        },
                        cancel: function (index, layero) {
                            var pageObj = Index.getPage(fsp.pageId);
                            if (pageObj && $.isFunction(pageObj.doMenuItemClick)) {
                                pageObj.setReturnData({});
                                pageObj.doMenuItemClick({
                                    id: 'dialogClose',
                                    opcode: 'close',
                                    dialogCloseCallback: function (r) {
                                        if (r && r.cancel) {
                                            if (r.simpleMessage) {
                                                yiDialog.warn(r.simpleMessage);
                                            }
                                            return false;
                                        }
                                        layer.close(index);
                                    }
                                });
                            }
                            return false;
                        }
                    });
                });
            });
        });
    }

    //构造创建页面实例所需的参数对象
    function getCreatePageInstanceOpts(r, fsp) {
        var opts = {
            pkid: fsp.pkId,
            formId: fsp.formId,
            formLayoutId: fsp.formLayoutId,
            pageId: fsp.pageId,
            parentPageId: r.pageId,
            domainType: fsp.domainType,
            openStyle: fsp.openStyle,
            pi: fsp.pi,
            status: fsp.status,
            uiBillTypeParam: fsp.uiBillTypeParam,
            uiComboData: fsp.uiComboData,
            uiData: fsp.uiData,
            uiMeta: fsp.uiMeta,
            uiRule: fsp.uiRule,
            formTopMenu: fsp.formTopMenu,
            formBottomMenu: fsp.formBottomMenu,
            customParameter: fsp.customParameter,
            isLock: fsp.isLock || false,
            ignoreBillLockMenus: fsp.ignoreBillLockMenus || [],
            lockTokenInfo: fsp.lockTokenInfo
        };
        opts.formUserProfile = fsp.formUserProfile;

        switch (opts.domainType) {
            case Consts.domainType.list:
            case Consts.domainType.listTree:
            case Consts.domainType.listReport:
                opts.filterSchemes = fsp.filterSchemes;
                opts.filterString = fsp.filterString;
                opts.dynamicParam = fsp.dynamicParam;
                opts.listColumns = fsp.listColumns;
                opts.listMode = fsp.listMode;
                opts.filterViewStyle = fsp.filterViewStyle;
                opts.maxPageSize = fsp.maxPageSize;
                opts.enableSrvPager = fsp.enableSrvPager;
                opts.filterFieldKeys = fsp.filterFieldKeys;
                break;
            case Consts.domainType.bill:
                break;
            case Consts.domainType.dynamic:
                break;
            case Consts.domainType.report:
                opts.rptModelDesc = fsp.rptModelDesc;
                break;
            case Consts.domainType.parameter:
                break;
            default:
                break;
        }

        return opts;
    }

    //创建页面实例
    function createPageInstance(opts) {

        switch (opts.domainType) {

            case Consts.domainType.list:

                //实例化一个列表页面对象
                return new ListView(opts);

            case Consts.domainType.listTree:

                //实例化一个树形列表页面对象
                return new ListTreeView(opts);

            case Consts.domainType.listReport:

                //实例化一个报表式列表页面对象
                return new ListReportView(opts);

            case Consts.domainType.bill:

                //实例化一个编辑页面对象
                return new BillView(opts);

            case Consts.domainType.dynamic:

                //实例化一个动态表单页面对象
                return new BaseView(opts);

            case Consts.domainType.report:

                //实例化一个自由式报表页面对象
                return new ReportView(opts);

            case Consts.domainType.parameter:

                //实例化一个参数配置页面对象
                return new ParameterView(opts);

            default:
                return null;
        }
    }

    //加载页面 js 文件
    function loadPageJs(opts, callback) {
        var plugInstances = [];

        //页面可能没有插件，所以无论是否有js插件，都必须执行callback回调
        if (opts.pi && opts.pi.pagePlugins) {

            //todo:这里会有多个插件文件，必须要等所有插件js文件加载完后，才能进行callback回调
            for (var plugIn in opts.pi.pagePlugins) {

                plugIn = $.trim(plugIn).toLowerCase();
                if (!plugIn) continue;

                //加入 env 环境变量后，在修改 js 插件时，只要重新打开一个新增或编辑页面就可以立即看到修改后的效果，而不用刷新整个浏览器
                if (Consts.env !== 'dev') {

                    //如果插件已经注册到window上，则无需再次请求
                    if (window[plugIn]) {

                        //创建插件实例
                        plugInstances.push(new window[plugIn]());
                        continue;
                    }
                }

                //此处采用同步请求
                yiCacheScript.g(opts.pi.pagePlugins[plugIn], function () {
                    if (window[plugIn]) {
                        //创建插件实例
                        plugInstances.push(new window[plugIn]());
                    }
                },
                    //配置为同步请求
                    { async: false });
            }
            //将所有插件实例传递到回调函数中执行
            callback(plugInstances);
        }
        else {
            callback(plugInstances);
        }
    }

    //获取页面 html 内容
    function getPageHtml(opts, pageObj, callback) {

        yiAjax.gf(opts.pi.pageViewFile, null, function (r) {

            callback(r);

        }, null, true, opts.panel);
    }

    //获取 bill.html 文件内容
    function getBillHtml(opts, pageId, html, callback) {

        //如果是标准表单，则存在右侧记录面板
        if (opts.domainType === Consts.domainType.bill) {

            yiAjax.gf('/views/bill.html', {}, function (billHtml) {

                if (billHtml) {

                    //将 bill.html 中的占位符 {0} 替换成 pageId
                    billHtml = billHtml.format(pageId);

                    //以前在页面中定义的菜单容器，现在这种通用代码已经移到 bill.html 中，所以为了兼容以前的，需要把以前页面中定义的菜单容器给干掉
                    html = html
                        .replace('class="Btn-menu"', 'style="display:none;"')
                        .replace('class="page-menu-list tab_title"', 'style="display:none;"');

                    //如果表单配置了相关信息，并且是编辑状态，则需要显示相关信息页签
                    var show = false;
                    if (opts.uiMeta.billRelated && opts.uiMeta.billRelated.setRelated && opts.pkid) {
                        show = true;
                    }
                    //如果系统启用了审批流，并且表单模型文件中也设置了启用审批流，并且是编辑状态下，才显示审批流页签
                    if (opts.uiMeta.sysFlow && opts.uiMeta.sysFlow.enable && opts.uiMeta.approvalFlow && opts.pkid && $.trim(opts.uiData.fflowinstanceid)) {
                        show = true;
                    }
                    if (show) {

                        yiAjax.gf('/views/related.html', {}, function (relatedHtml) {
                            if (relatedHtml) {
                                //将 related.html 中的占位符 {0} 替换成 pageId
                                relatedHtml = relatedHtml.format(pageId).replace('{bill-page-detailinfo}', html);
                            }

                            html = billHtml.replace('{bill-page-content}', relatedHtml);

                            callback(html);
                        });

                    } else {

                        //将页面中的占位符 {0} 替换成 pageId
                        html = billHtml.replace('{bill-page-content}', html);

                        callback(html);
                    }
                }
            });

        } else if (opts.domainType === Consts.domainType.parameter) {

            //参数设置表单
            html = '\
            <div class="row">\
                <div class="col-md-12">\
                    <div class="page-menu-list tab_title"></div>\
                    <div class="leftTab page-mtop">' + html + '</div>\
                </div>\
            </div>';
            callback(html);

        } else if (opts.domainType === Consts.domainType.list
            || opts.domainType === Consts.domainType.listTree
            || opts.domainType === Consts.domainType.listReport) {

            //普通列表，树形列表，报表式列表
            var _filterHtml = '<div id="{0}_filterpanel" class="filterpanel" style="margin-bottom: 10px;"></div>'.format(opts.pageId);

            //如果存在自定义过滤页面，则显示成页签形式
            if ($.trim(opts.uiMeta && opts.uiMeta.customFilterForm)) {
                _filterHtml = '\
                <div class="tabbable-line">\
                    <ul class="nav nav-tabs">\
                        <li class="active"><a href="#{0}_filter_basic" data-toggle="tab" aria-expanded="false">基本过滤</a></li>\
                        <li><a href="#{0}_filter_advanced" data-toggle="tab" aria-expanded="false">高级过滤</a></li>\
                    </ul>\
                    <div class="tab-content">\
                        <div id="{0}_filter_basic" class="tab-pane active">\
                        </div>\
                        <div id="{0}_filter_advanced" class="tab-pane">\
                            <div id="{0}_filterpanel" class="filterpanel"></div>\
                        </div>\
                    </div>\
                </div>'.format(opts.pageId);
            }

            html = html.replace('{filter-content}', _filterHtml);

            callback(html);

        } else {

            callback(html);
        }
    }

    window.Showform = window.Showform || Showform;
})();