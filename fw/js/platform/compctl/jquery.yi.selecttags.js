/*
    1.插件用途：用于表头下拉框多选
    
    2.使用方法：$(selector).selecttags();
    
    4。<input group="基本信息" el="155" ek="fbillhead" visible="-1" id="ftest" fn="ftest" pn="ftest" cn="测试" copy="0" />
    
    3,案例： <input type="hidden" name="fsource" class="form-control select-tags" max="10"> 
*/

; (function ($, window, document, undefined) {

    //默认参数
    var defaults = { domainType: '', formId: '', pageId: '' };

    //所有对外开放的方法
    var methods = {
		//设置控件配置项
        setOptions: function (jq, opt) {
            if ($.isPlainObject(opt)) {
                var $one = jq.eq(0);
                var state = $one.data('selecttags');
                if (state) {
                    $.extend(state, opt || {});
                    $one.data('selecttags', state);
                }
            }
            return jq;
        },

        //获取控件配置项
        getOptions: function (jq) {
            return jq.eq(0).data('selecttags');
        },
        //设置控件可用性
        setEnable: function (jq, param) {

            if (jq.length>0&& typeof param === 'boolean') {
                var state = jq.data('selecttags');
                
                if (!state) return;
                state.$select.prop('disabled',!param);
                
                
            }
        },
        
        
		setValue: function (jq,id) {
			
			var state = jq.data('selecttags');
            if (!state) return;
            //select2初始化默认选中值（多选），只能用下面的方法
			state.$select.select2('val', (id&&id.length>0)?id.split(','):[]);
			
			
		},
		getValue: function (jq,id) {
			
			var state = jq.data('selecttags');
			if (!state) return;
			return state.$select.select2('val');
			
			
			
		}
    };

    //插件代码
    $.fn.selecttags = function (options,param) {

        if (typeof options === 'string') {
			
            if (methods[options]) {
            	
                return methods[options](this, param);
            } else {
                throw new Error('jquery.yi.selecttags.js 插件不存在方法：' + options);
            }
        }
        //遍历当前所选择的 Dom 元素，并提供链式操作
        return this.each(function () {

            //合并默认参数，但是不影响默认参数（保护好默认参数）
            var settings = $.extend({}, defaults, options),

                //当前的 select 元素
        		$select = $(this),

        	    //获取 fieldkey
        		key = $select.attr('name');
			var values=$select.attr('vals');
			var maximumSelectionSize=$select.attr('max')?$select.attr('max'):10;//默认最多十个标签。
			var maximumInputLength=$select.attr('maxlength')?$select.attr('maxlength'):100;//标签默认长度。
			var reSetValue=[];
			if($.trim(values)){
	        	var setValue=[];
	        	setValue=values.split(',');
	        	
	        	for(var i=0,l=setValue.length; i<l; i++){
	        		reSetValue.push({id:setValue[i],text:setValue[i]});
	        	}
	        	
	        }
			
			
            //将下拉框设置为可多选，并且初始化  minimumSelectionLength  maximumSelectionLength
            $select.select2({ tags: reSetValue ,
            	maximumSelectionSize:maximumSelectionSize,
            	maximumInputLength:maximumInputLength
            });
            
            
            //数据渲染
            renderer($select, settings ,reSetValue);
        });
    };

    //渲染下拉框控件
    function renderer($select, settings,reSetValue) {
    	
    	//初始化的预置值
    	if(settings.nature && settings.nature.valueList){
    		var arr=[];
    		var preSet=settings.nature.valueList.split(',');
    		for(var i=0,l=preSet.length; i<l ;i++){
    			arr.push({id:preSet[i],text:preSet[i]});
    		}
    		
    		for (var i = 0 ; i < arr.length ; i ++ ){
				for(var j = 0 ; j < reSetValue.length ; j ++ ){
					
				    if (arr[i] && arr[i].id == reSetValue[j].id){
				     	arr.splice(i,1); //利用splice函数删除元素，从第i个位置，截取长度为1的元素
				    }
			    }
			}
			//
			for(var i = 0; i <reSetValue.length; i++){
			  	arr.push(reSetValue[i]);
			}

    		
    		
    		$select.select2({ tags: arr});
    	}
        var values=$select.attr('vals');
        
        if($.trim(values)){
        	var setValue=[];
        	setValue=values.split(',');
        	
        	$select.select2('val', setValue);
        }
        $select.data('selecttags',{
            $select:$select

        });
        //选择下拉框项触发的事件
        $select.on('change', function (e) {
            if ($.isFunction(settings.change)) {
                var selData = $(e.target).select2('data'), vals = '', txts = '';
                if (selData) {
                    for (var i = 0; i < selData.length; i++) {
                        if (i < selData.length - 1) {
                            vals += selData[i].id + ',';
                            txts += selData[i].text + ',';
                        } else {
                            vals += selData[i].id;
                            txts += selData[i].text;
                        }
                    }
                }
                e.name = $select.attr('name');
                e.vals = vals;
                e.txts = txts;
                var _option=$select.data('selectmult');
                e.row=(_option&& _option.rowId)?_option.rowId:'';
                settings.change(e);
            }
        });
    }

})(jQuery, window, document);