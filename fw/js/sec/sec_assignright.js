///<reference path="/fw/js/consts.js" />
/*
@ sourceURL=/fw/js/sec/sec_assignright.js
*/
; (function () {
    var sec_assignright = (function (_super) {
        var _child = function (args) {
            var that = this;
            that.roles = [];
            that.role = {};
            that.currentNode;
            _super.call(that, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************
        _child.prototype.entryId = 'fentity';
        _child.prototype.roleTree = 'roletree';

        //初始化动态表单插件
        _child.prototype.onInitialized = function (e) {
            var that = this;

            //加载角色
            that.Model.invokeFormOperation({
                id: 'getpermrole',
                opcode: 'getpermrole'
            });
        };

        //创建明细表格
        _child.prototype.onCreateGrid = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.entryId:
                    e.result = { multiselect: false };
                    break;
            }
        };

        //处理树形控件的初始化过程
        _child.prototype.onCreateTreeList = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case this.roleTree:
                    e.result = {
                        view: {
                            selectedMulti: false
                        },
                        edit: {
                            enable: true,
                            editNameSelectAll: true,
                            renameTitle: "编辑",
                            showRenameBtn: function (treeId, treeNode) {
                                if (treeNode.isParent) {
                                    return false;
                                }
                                return true;
                            },
                            removeTitle: '删除',
                            showRemoveBtn: function (treeId, treeNode) {
                                if (treeNode.isParent) {
                                    return false;
                                }
                                return true;
                            }
                        },
                        data: {
                            simpleData: {
                                enable: true
                            }
                        }
                    };
                    break;
            }
        };

        //表单元素被单击后
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            if (!e.opcode) return;
            switch (e.opcode) {
                case 'rolenew':
                    e.result = true;
                    that.Model.showBill({
                        formId: 'sec_role',
                        openStyle: "modal"
                    });
                    break;
                case 'permsave':
                    e.result = true;
                    that.save();
                    break;
                case 'permfresh':
                    e.result = true;
                    that.loadUserPermission('true');

                    //加载角色
                    that.Model.invokeFormOperation({
                        id: 'getpermrole',
                        opcode: 'getpermrole'
                    });
                    break;
                case 'allot':
                    e.result = true;
                    that.allotRole();
                    break;
            }
        };

        //处理树形控件节点名称的编辑事件
        _child.prototype.onTreeNodeEditing = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case that.roleTree:
                    e.result = false;
                    that.Model.showBill({
                        openStyle: Consts.openStyle.modal,
                        formId: 'sec_role',
                        pkids: [e.node.fbillhead_id]
                    });
                    break;
            }
        };

        //处理树形控件节点的删除事件
        _child.prototype.onTreeNodeDeleting = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case that.roleTree:
                    e.result = false;
                    that.Model.invokeFormOperation({
                        id: 'delete',
                        opcode: 'delete',
                        opctx: { node: e.node.id },
                        selectedRows: [{ PKValue: e.node.fbillhead_id }],
                        param: {
                            domainType: Consts.domainType.list,
                            formId: 'sec_role'
                        }
                    });
                    break;
            }
        };

        //处理树形控件节点的点击事件 
        _child.prototype.onTreeNodeClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case that.roleTree:
                    if (!e.node.isParent) {
                        that.currentNode = e.node;
                        that.loadUserPermission();
                    }
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case "fuserid":
                    var userCompany = $.trim(e.value.id) ? Consts.loginCompany.name : '';
                    that.Model.setValue({ id: 'fusercompany', value: userCompany, row: e.row });
                    break;
            }
        };

        //加载角色用户权限项
        _child.prototype.loadUserPermission = function (forceNoCache) {
            var that = this;
            var node = that.currentNode;
            if (!node) return;
            var roleId = node.fbillhead_id;
            that.role = { fbillhead_id: roleId };
            for (var i = 0; i < that.roles.length; i++) {
                that.roles[i].isEdit = false;
                if (that.roles[i].fbillhead_id == roleId) {
                    that.role = that.roles[i];
                }
            }
            that.Model.invokeFormOperation({
                id: 'getrolepermission',
                opcode: 'getrolepermission',
                param: {
                    domainType: Consts.domainType.dynamic,
                    formId: 'sec_role',
                    roleId: roleId,
                    forceNoCache: forceNoCache || ''
                }
            });
        };

        //填充用户明细列表
        _child.prototype.fillUserEntry = function () {
            var that = this;
            var users = that.Model.getEntryData({ id: that.entryId });
            if (users) {
                users.length = 0;
                var roleUsers = that.role.perm.roleUsers;
                //如果后端没有返回数据，则默认加一空行
                if ($.isEmptyObject(roleUsers)) {
                    that.Model.addRow({ id: that.entryId });
                } else {
                    var userInfos = that.role.perm.userInfo;
                    for (var i = 0; i < userInfos.length; i++) {
                        var userInfo = userInfos[i];
                        var userEntry = {
                            fuserid: { id: userInfo.userid, fnumber: userInfo.usernumber, fname: userInfo.username },
                            fusername: userInfo.username,
                            fusercompany: userInfo.orgname
                        };
                        users.push(userEntry);
                    }
                }
                that.Model.refreshEntry({ id: that.entryId });
            }

        };


        //获取明细编辑控件状态
        _child.prototype.onGetEditorState = function (e) {
            var that = this;
            if (that.currentNode.fnumber == 'Admin_Agent') {
                e.result.enabled = false;
            }
            else {
                e.result.enabled = true;
            }
            //用户不允许编辑
            switch (e.id.toLowerCase()) {
                case 'fuserid':
                    e.result.enabled = false;
                    return;
            }
        };
        //表格行删除前事件：设置 e.result=true 表示不让删除
        _child.prototype.onEntryRowDeleting = function (e) {
            var that = this;
            debugger;
            if (that.currentNode.fnumber == 'Admin_Agent') {
                e.result = true;
                yiDialog.error('系统预设的经销商角色用户，不能进行删除！');
                return;
            }

        };

        _child.prototype.onEntryRowCreating = function (e) {
            var that = this;
            if (that.currentNode.fnumber == 'Admin_Agent') {
                e.result = true;
                yiDialog.error('系统预设的经销商角色用户，不能手工添加！');
                return;
            }
        };

        //渲染模块菜单权限项
        _child.prototype.drewPermPanel = function () {
            var that = this;
            var htmlModule = '';
            //遍历模块
            var mdlPerms = that.role.perm.mdlPermission;
            for (var i = 0; i < mdlPerms.length; i++) {
                var mdlPerm = mdlPerms[i];
                var bizObjs = mdlPerm.bizObjPermission;
                var _flag = true;
                for (var _i = 0; _i < bizObjs.length; _i++) {
                    var fucACL = bizObjs[_i].fucntionACL;
                    if (fucACL) {
                        for (var _m = 0; _m < fucACL.length; _m++) {
                            if (fucACL[_m].isAllow === false) {
                                _flag = false;
                                break;
                            }
                        }
                    }
                }
                htmlModule += '<div class="portlet col-md-12 box yellow-casablanca"><div class="portlet-title">';
                htmlModule += '<label class="checkbox-inline"><div class="input-icon right"><div class="checker">';
                if (_flag) {
                    htmlModule += '<span class=" {0} checked" flag="{0}">'.format(mdlPerm.moduleName);
                } else {
                    htmlModule += '<span class="left {0}" flag="{0}">'.format(mdlPerm.moduleName);
                }
                htmlModule += '<input type="checkbox" style="margin-left: -10px;" optype="modelcheck"  data-param="dataBiz:\'{0}\'">'.format(mdlPerm.moduleName);
                htmlModule += '</span></div>{0}</div></label>'.format(mdlPerm.moduleName);
                htmlModule += '<div class="tools">';
                //第一个默认展开，其他模块默认收起来
                if (i == 0) {
                    htmlModule += '<a class="collapse"></a></div></div><div class="portlet-body col-md-12 form" style="display: block;"><div class="form-body col-md-12">';
                } else {
                    htmlModule += '<a class="expand"></a></div></div><div class="portlet-body col-md-12 form" style="display: none;"><div class="form-body col-md-12">';
                }
                htmlModule += '<table class="sec-box">';

                //遍历业务对象
                var htmlBiz = '';
                var bizPerms = mdlPerm.bizObjPermission;
                for (var j = 0; j < bizPerms.length; j++) {
                    var bizPerm = bizPerms[j];

                    //遍历权限项
                    var acl = bizPerm.fucntionACL;
                    var isAllow = true;
                    for (var k = 0; k < acl.length; k++) {
                        if (!acl[k].isAllow) {
                            isAllow = false;
                        }
                    }

                    var $allCheck = '<label class="checkbox-inline"><div class="input-icon right"><div class="checker">';
                    if (isAllow) {
                        $allCheck += '<span class="left checked ' + mdlPerm.moduleName + ' ' + bizPerm.bizObjId + ' ' + i + '-' + j + '">';
                    } else {
                        $allCheck += '<span class="left ' + mdlPerm.moduleName + ' ' + bizPerm.bizObjId + ' ' + i + '-' + j + '">';
                    }
                    $allCheck += '<input type="checkbox" style="margin-left: -10px;" optype="allcheck" data-param="dataBiz:\'{0}\',dataPerm:\'{1}\',dataInxs:\'{2}\',dataModel:\'{3}\'">'.format(bizPerm.bizObjId, "biz", i + '-' + j, mdlPerm.moduleName);
                    $allCheck += '</span></div>全选</div></label>';
                    htmlBiz += '<tr><td class="first-td">' + bizPerm.bizObjName + $allCheck + '</td><td><div class="checkbox-list">';

                    for (var k = 0; k < acl.length; k++) {
                        var htmAcl = '<label class="checkbox-inline ass-col no-pad"><div class="input-icon right">';
                        var _checked = '';
                        if (!acl[k].isAllow) {
                            isAllow = false;
                        } else {
                            _checked = 'checked ';
                        }
                        htmAcl += '<div class="checker"><span class="' + _checked + mdlPerm.moduleName + ' ' + bizPerm.bizObjId + ' ' + i + '-' + j + '-' + k + ' right">';
                        htmAcl += '<input type="checkbox" style="margin-left: -10px;" optype="onecheck" data-param="dataModule:\'{0}\',dataBiz:\'{1}\',dataItem:\'{2}\',dataInxs:\'{3}\',dataLen:\'{4}\',left:\'{5}\',dataModel:\'{6}\'" class="{1}_{2}">'.format(mdlPerm.moduleId, bizPerm.bizObjId, acl[k].itemId, i + '-' + j + '-' + k, acl.length, i + '-' + j, mdlPerm.moduleName);
                        htmAcl += '</span></div>' + acl[k].itemName + '</div></label>';
                        htmlBiz += htmAcl;
                    }
                    htmlBiz += '</div></td>';

                    htmlBiz += '<td class="last-td">';
                    htmlBiz += '<a optype="range" data-param="dataBiz:\'{0}\',dataInxs:\'{1}\'" style="display:block;">数据范围</a>'.format(bizPerm.bizObjId, i + '-' + j);
                    htmlBiz += '<a optype="permit" data-param="dataBiz:\'{0}\',dataInxs:\'{1}\'" style="display:block;">字段授权</a>'.format(bizPerm.bizObjId, i + '-' + j);
                    htmlBiz += '</td></tr><tr class="space"></tr>';
                }
                htmlModule += htmlBiz;
                htmlModule += '</table></div></div></div>';
            }
            that.Model.setHtml({ id: '.ver-perm', value: htmlModule });
        };

        //表单元素单击事件
        _child.prototype.onElementClick = function (e) {
            var that = this;
            if (!e.id) return;
            switch (e.id) {
                case 'modelcheck':
                    //模块复选框点击时
                    that.procModuleCheck(e);
                    break;
                case 'allcheck':
                    //业务对象复选框点击时
                    that.procAllCheck(e);
                    break;
                case 'onecheck':
                    //权限项复选框点击时
                    that.procOneCheck(e);
                    break;
                case 'range':
                    //数据范围
                    that.dataRange(e);
                    break;
                case 'permit':
                    //字段授权
                    that.Model.invokeFormOperation({
                        id: 'queryfield',
                        opcode: 'queryfield',
                        opctx: { dataBiz: e.param.dataBiz },
                        param: {
                            domainType: Consts.domainType.dynamic,
                            fieldKey: 'feditfield',
                            bizFormId: e.param.dataBiz
                        }
                    });
                    break;
            }
            //阻止事件冒泡
            e.e.stopPropagation();
            e.e.preventDefault();
        };

        //模块复选框点击事件的处理函数
        _child.prototype.procModuleCheck = function (e) {
            var that = this;
            var isAllow = false;
            var _classOpt = { id: '.' + e.param.dataBiz, value: 'checked' };
            var _class = that.Model.getAttr({ id: '.' + e.param.dataBiz, random: 'class' });
            if (_class.indexOf('checked') == -1) {
                isAllow = true;
                that.Model.addClass(_classOpt);
            } else {
                that.Model.removeClass(_classOpt);
            }
            var mdlPerms = that.role.perm.mdlPermission;
            for (var i = 0, l = mdlPerms.length; i < l; i++) {
                var mdlPerm = mdlPerms[i];
                if (mdlPerm && mdlPerm.moduleId == e.param.dataBiz) {
                    for (var j = 0, k = mdlPerm.bizObjPermission.length; j < k; j++) {
                        var bizObj = mdlPerm.bizObjPermission[j];
                        var fucACL = bizObj.fucntionACL;
                        if (fucACL) {
                            for (var m = 0; m < fucACL.length; m++) {
                                fucACL[m].isAllow = isAllow;

                                //设置与权限项相同的复选框的选中状态
                                that.setAlikeCheck({ dataBiz: bizObj.bizObjId, dataItem: fucACL[m].itemId }, isAllow, i);
                            }

                            //设置业务对象复选框的选中状态
                            that.setBizObjCheck({
                                dataModule: mdlPerm.moduleId,
                                dataBiz: bizObj.bizObjId,
                                dataLen: fucACL.length
                            });
                        }
                    }
                    break;
                }
            }
        };

        //业务对象复选框点击事件的处理函数
        _child.prototype.procAllCheck = function (e) {
            var that = this;
            if (!that.role || !that.role.perm) return;
            var $bizs = that.Model.getEleMent({ id: '.left.' + e.param.dataBiz });
            if (!$bizs || $bizs.length < 1) return;

            var isAllow = false;
            var _classOpt = { id: '.' + e.param.dataBiz, value: 'checked' };
            var _class = that.Model.getAttr({ id: '.' + e.param.dataBiz + '.' + e.param.dataInxs, random: 'class' });
            if (_class.indexOf('checked') == -1) {
                isAllow = true;
                that.Model.addClass(_classOpt);
            } else {
                that.Model.removeClass(_classOpt);
            }

            var mdlPerms = that.role.perm.mdlPermission;
            for (var i = 0; i < $bizs.length; i++) {
                var bizClass = $bizs.eq(i).attr('class').split(' ');
                if (!bizClass || bizClass.length < 4) continue;

                var dataInxs = bizClass[3].split('-');
                var mdlPerm = mdlPerms[dataInxs[0]];
                var fucACL = mdlPerm.bizObjPermission[dataInxs[1]].fucntionACL;
                if (!fucACL) continue;

                for (var j = 0; j < fucACL.length; j++) {
                    fucACL[j].isAllow = isAllow;
                }

                //设置模块复选框的选中状态
                that.setModuleCheck(mdlPerm);
            }
        };

        //权限项复选框点击事件的处理函数
        _child.prototype.procOneCheck = function (e) {
            var that = this;
            if (!that.role || !that.role.perm) return;
            var _class = that.Model.getAttr({ id: '.' + e.param.dataInxs, random: 'class' });
            var dataInxs = e.param.dataInxs.split('-');
            var mdlPerms = that.role.perm.mdlPermission;
            var mdlPerm = mdlPerms[dataInxs[0]];
            var fucACL = mdlPerm.bizObjPermission[dataInxs[1]].fucntionACL;
            if (!fucACL) return;

            //设置当前点击的复选框的选中状态
            var isAllow = false;
            var _classOpt = { id: '.' + e.param.dataInxs, value: 'checked' };
            if (_class.indexOf('checked') == -1) {
                isAllow = true;
                that.Model.addClass(_classOpt);
            } else {
                that.Model.removeClass(_classOpt);
            }
            fucACL[dataInxs[2]].isAllow = isAllow;

            //设置权限项相同的复选框的选中状态
            that.setAlikeCheck(e.param, isAllow, dataInxs[0]);

            //设置业务对象复选框的选中状态
            that.setBizObjCheck(e.param);

            //设置模块复选框的选中状态
            that.setModuleCheck(mdlPerm);
        };

        //设置权限项相同的复选框的选中状态
        _child.prototype.setAlikeCheck = function (param, isAllow, moduleSeq) {
            var that = this;
            var mdlPerms = that.role.perm.mdlPermission;
            var _alikes = that.Model.getEleMent({ id: '.' + param.dataBiz + '_' + param.dataItem });
            _alikes.each(function () {
                var $ck = $(this);
                var _param = eval('({' + ($ck.attr('data-param') || '') + '})');
                var _dataInxs = _param.dataInxs.split('-');
                var $sp = $ck.parent('span');
                if (isAllow) {
                    $sp.addClass('checked');
                } else {
                    $sp.removeClass('checked');
                }
                var _mdlPerm = mdlPerms[_dataInxs[0]];
                var _fucACL = _mdlPerm.bizObjPermission[_dataInxs[1]].fucntionACL;
                if (_fucACL) {
                    _fucACL[_dataInxs[2]].isAllow = isAllow;
                }
            });
        };

        //设置业务对象复选框的选中状态
        _child.prototype.setBizObjCheck = function (param) {
            var that = this;
            //重名的业务对象个数
            var bizCount = that.Model.getEleMent({ id: '.left.' + param.dataModule + '.' + param.dataBiz }).length;
            //重名的业务对象的权限项个数
            var bizPermCount = bizCount * param.dataLen;
            var _checked = that.Model.getEleMent({ id: '.checked.right.' + param.dataModule + '.' + param.dataBiz });
            if (_checked && _checked.length == bizPermCount) {
                that.Model.addClass({ id: '.left.' + param.dataBiz, value: 'checked' });
            } else {
                that.Model.removeClass({ id: '.left.' + param.dataBiz, value: 'checked' });
            }
        };

        //设置模块复选框的选中状态
        _child.prototype.setModuleCheck = function (mdlPerm) {
            var that = this;
            var bizObjPerm = mdlPerm.bizObjPermission;
            var _flag = true;
            for (var _i = 0, _l = bizObjPerm.length; _i < _l; _i++) {
                var _fucACL = bizObjPerm[_i].fucntionACL;
                if (_fucACL) {
                    for (var _m = 0; _m < _fucACL.length; _m++) {
                        if (_fucACL[_m].isAllow === false) {
                            _flag = false;
                            break;
                        }
                    }
                }
            }
            var _flagAttr = { id: '[flag={0}]'.format(mdlPerm.moduleName), value: 'checked' };
            if (_flag) {
                that.Model.addClass(_flagAttr);
            } else {
                that.Model.removeClass(_flagAttr);
            }
        };

        //打开数据范围对话框
        _child.prototype.dataRange = function (e) {
            var that = this;
            var bizObjPerms = that.findBizOjbPerms(e.param.dataBiz);
            that.Model.showForm({
                openStyle: 'modal',
                formId: 'sec_datarange',
                cp: {
                    roleId: that.role.fbillhead_id,
                    dataBiz: e.param.dataBiz,
                    dataRowPerm: bizObjPerms[0].dataRowACL,
                    callback: function (result) {
                        if (!$.isEmptyObject(result)) {
                            for (var i = 0; i < bizObjPerms; i++) {
                                bizObjPerms[i].dataRowACL.fw_view = result.dataRow;
                            }
                        }
                    }
                }
            });
        };

        //根据业务对象Id查找业务对象
        _child.prototype.findBizOjbPerms = function (bizObjId) {
            var that = this;
            var bizObjPerms = [];
            var mdlPerms = that.role.perm.mdlPermission;
            for (var i = 0; i < mdlPerms.length; i++) {
                for (var j = 0; j < mdlPerms[i].bizObjPermission.length; j++) {
                    if (mdlPerms[i].bizObjPermission[j].bizObjId === bizObjId) {
                        bizObjPerms.push(mdlPerms[i].bizObjPermission[j]);
                    }
                }
            }
            return bizObjPerms;
        };

        //分配角色
        _child.prototype.allotRole = function () {
            var that = this;
            var node = that.currentNode;
            if (!node) {
                yiDialog.error('请选择要分配的角色！');
                return;
            }
            if (node.fispreset !== '1' || node.fmainorgid !== '0') {
                yiDialog.error('您选择的角色【' + node.name + '】没有手工设为共享，不允许分配！');
                return;
            }
            var perm = that.packPermission();
            that.Model.showForm({
                domainType: Consts.domainType.dynamic,
                openStyle: Consts.openStyle.modal,
                formId: 'sec_assignrightallot',
                cp: {
                    role: node,
                    perm: perm,
                    callback: function (result) {
                        if (result && result.isSuccess) {

                        }
                    }
                }
            });
        };

        //组装角色授权信息
        _child.prototype.packPermission = function () {
            var that = this;
            if ($.isEmptyObject(that.role)) {
                yiDialog.a('请选择一个角色后再执行该操作！');
                return;
            }
            //相同的业务对象只存一份，此时需要去除重复的业务对象
            var _mdlPerms = [];
            var _bizObjIds = [];
            var clonePerm = $.extend(true, {}, that.role.perm);
            var mdlPerms = clonePerm.mdlPermission;
            for (var i = 0; i < mdlPerms.length; i++) {
                var _bizObjs = [];
                var bizObjs = mdlPerms[i].bizObjPermission;
                for (var j = 0; j < bizObjs.length; j++) {
                    var _bizObjId = bizObjs[j].bizObjId;
                    if ($.inArray(_bizObjId, _bizObjIds) === -1) {
                        _bizObjIds.push(_bizObjId);
                        _bizObjs.push(bizObjs[j]);
                    }
                }
                _mdlPerms.push({
                    moduleId: mdlPerms[i].moduleId,
                    moduleName: mdlPerms[i].moduleName,
                    bizObjPermission: _bizObjs
                });
            }
            clonePerm.mdlPermission = _mdlPerms;

            return clonePerm;
        };

        //保存
        _child.prototype.save = function () {
            var that = this;
            var perm = that.packPermission();

            var roleUsers = [];
            var users = that.Model.getEntryData({ id: that.entryId });
            for (var i = 0; i < users.length; i++) {
                roleUsers.push(users[i].fuserid.id);
                roleUsers.push(users[i].fuserid.fname);
            }
            that.role.perm.roleUsers = roleUsers;
            perm.roleUsers = roleUsers;

            that.Model.invokeFormOperation({
                id: 'saverolepermission',
                opcode: 'saverolepermission',
                param: {
                    domainType: Consts.domainType.dynamic,
                    formId: 'sec_role',
                    roleId: that.role.fbillhead_id,
                    permission: JSON.stringify(perm)
                }
            });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            if (!e || !e.opcode) return;
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            var optData = e.result.operationResult.optionData;
            switch (e.opcode) {
                case 'getpermrole':
                    if (isSuccess && srvData && srvData.data) {
                        var nodes = [];
                        var roles = srvData.data;
                        for (var i = 0; i < roles.length; i++) {
                            roles[i].id = i + 2;
                            roles[i].pId = 1;
                            roles[i].name = roles[i].fname;
                            nodes.push(roles[i]);
                        }
                        if (nodes.length > 0) {
                            //设置角色树控件数据源
                            that.Model.setTreeData({ id: that.roleTree, nodes: nodes });
                            //默认选中第一个节点
                            that.Model.selectTreeNode({ id: that.roleTree, node: 2 });
                            that.currentNode = that.Model.getTreeNode({ id: that.roleTree, node: 2 })
                            that.loadUserPermission();
                        }
                    }
                    break;
                case 'delete':
                    if (isSuccess) {
                        that.Model.deleteTreeNode({ id: that.roleTree, node: e.opctx.node });
                    }
                    break;
                case 'getrolepermission':
                    if (srvData) {
                        that.role.perm = JSON.parse(srvData);
                        that.role.isEdit = true;
                        that.fillUserEntry();
                        that.drewPermPanel();
                    }
                    break;
                case 'queryfield':
                    if (isSuccess) {
                        var bizObjPerms = that.findBizOjbPerms(e.opctx.dataBiz);
                        that.Model.showForm({
                            openStyle: 'modal',
                            formId: 'sec_fields_permit',
                            cp: {
                                fieldList: srvData,
                                fieldPerm: bizObjPerms[0].fieldACL,
                                callback: function (result) {
                                    for (var i = 0; i < bizObjPerms; i++) {
                                        bizObjPerms[i].fieldACL = result.fieldACL;
                                    }
                                }
                            }
                        });
                    }
                    break;
            }
        };



        //获取基础资料字段动态过滤条件
        _child.prototype.onQueryFilterString = function (e) {
            if (!e.id) return;
            var that = this;
            var roleId = that.role.fbillhead_id;
            switch (e.id.toLowerCase()) {
                //用户
                case 'fuserid':
                    var srcPara = {
                        roleId: roleId,
                        fromFormId: 'sec_assignright'
                    };
                    e.result.simpleData = {
                        srcPara: JSON.stringify(srcPara)
                    };
                    break;
            }
        }




        return _child;

    })(BasePlugIn);
    window.sec_assignright = window.sec_assignright || sec_assignright;
})();