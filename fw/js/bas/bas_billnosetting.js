/*
 * 编码规则
 * @ sourceURL=/fw/js/bas/bas_billnosetting.js
 */
; (function () {
    var bas_billnosetting = (function (_super) {
        var _child = function (args) {
            _super.call(this, args);
        };
        __extends(_child, _super);

        //编辑页面初始化后触发
        _child.prototype.onBillInitialized = function (e) {
            var that = this;

            //新增时，设置默认值
            var pkid = that.Model.pkid;
            if (!pkid) {
                that.Model.setValue({ id: 'fconstart', value: false });
                that.Model.setValue({ id: 'fdstart', value: false });
                that.Model.setValue({ id: 'fisdefault', value: true });
            }

            //加载日期来源下拉框数据源
            that.loadDateFields();

            //初始常量组合设置
            that.toggleConstCombine();

            //初始日期组合设置
            that.toggleDateCombine();
        };

        //表单字段值变化时触发
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fworkobject':
                    that.loadDateFields(e.value.id);
                    break;
                case 'fconstart':
                    that.toggleConstCombine(e.value);
                    break;
                case 'fdstart':
                    that.toggleDateCombine(e.value);
                    break;
                case 'fsetvalue':
                    //设置常量的长度
                    that.Model.setValue({ id: 'fclength', value: e.value.length });
                    break;
                case 'fdataformat':
                    //设置日期来源的长度
                    var length = 0;
                    if (e.value && e.value.fnumber) {
                        length = e.value.fnumber.length;
                    }
                    that.Model.setValue({ id: 'fdlength', value: length });
                    break;
            }
        };

        //表单菜单点击时触发
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                case 'save':
                    var fcnum = that.Model.getValue({ id: 'fclength' }), //常量的长度
                        fdnum = that.Model.getValue({ id: 'fdlength' }), //日期字段的长度
                        fwnum = that.Model.getValue({ id: 'fserlength' }); //流水号的长度
                    var num = Number(fcnum) + Number(fdnum) + Number(fwnum);
                    if (num > 20) {
                        yiDialog.a('编码总长度大于20位，不符合要求！');
                        e.result = true;
                    }
                    break;
            }
        };

        //常量组合切换
        _child.prototype.toggleConstCombine = function (enable) {
            var that = this;
            if (enable === undefined) {
                enable = that.Model.getValue({ id: 'fconstart' });
            }
            that.Model.setEnable({ id: 'fsetvalue', value: enable });
            if (!enable) {
                that.Model.setValue({ id: 'fsetvalue', value: '' });
            }
        };

        //日期组合切换
        _child.prototype.toggleDateCombine = function (enable) {
            var that = this;
            if (enable === undefined) {
                enable = that.Model.getValue({ id: 'fdstart' });
            }
            that.Model.setEnable({ id: 'fdataformat', value: enable });
            that.Model.setEnable({ id: 'fdatasource', value: enable });
            if (!enable) {
                that.Model.setValue({ id: 'fdatasource', value: '' });
                that.Model.setValue({ id: 'fdataformat', value: '' });
            }
        };

        //加载日期来源下拉框数据源
        _child.prototype.loadDateFields = function (billFormId, dataSourceId) {
            var that = this;
            if (billFormId === undefined) {
                billFormId = that.Model.getSimpleValue({ id: 'fworkobject' });
            }
            if (dataSourceId === undefined) {
                dataSourceId = that.Model.getSimpleValue({ id: 'fdatasource' });
            }
            if (!billFormId) {
                that.Model.setComboData({ id: 'fdatasource', data: [] });
                return;
            }
            that.Model.invokeFormOperation({
                id: 'getfielduimeta',
                opcode: 'getfielduimeta',
                opctx: { dataSourceId: dataSourceId },
                param: {
                    domainType: Consts.domainType.dynamic,
                    formId: 'sys_mainfw',
                    billFormId: billFormId,
                    fieldFilter: '112,113,119,121'
                }
            });
        };

        //操作成功后触发的事件
        _child.prototype.onAfterDoOperation = function (e) {
            var that = this;
            var isSuccess = e.result.operationResult.isSuccess;
            var srvData = e.result.operationResult.srvData;
            switch (e.opcode) {
                case 'getfielduimeta':
                    var comboItems = [];
                    if (srvData) {
                        //去除特定的平台日期字段
                        var dateKeys = ['fapprovedate', 'fsenddate', 'fdownloaddate', 'freminddate', 'fprintdate'];
                        for (var i = 0; i < srvData.length; i++) {
                            var fieldKey = srvData[i].id.toLowerCase();
                            if (dateKeys.indexOf(fieldKey) === -1) {
                                comboItems.push({ id: srvData[i].id, name: srvData[i].caption });
                            }
                        }
                    }
                    that.Model.setComboData({ id: 'fdatasource', data: comboItems, value: e.opctx.dataSourceId });
                    // 重新初始化日期值，要拿旧值，因为在加载日期来源时，被重新赋值
                    that.Model.setValue({ id: 'fdatasource', value: that.Model.uiDataOld.fdatasource });
                    that.Model.setValue({ id: 'fdataformat', value: that.Model.uiDataOld.fdataformat });
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.bas_billnosetting = window.bas_billnosetting || bas_billnosetting;
})();