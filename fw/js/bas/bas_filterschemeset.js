/*
 <reference path="/fw/js/basepage.js" />
 @ sourceURL=/fw/js/bas/bas_filterschemeset.js
 */
; (function () {
    var bas_filterschemeset = (function (_super) {
        var _child = function (args) {
            _super.call(this, args);
        };
        __extends(_child, _super);

        //属性（对于所有实例的属性值都是一样的情况下，在原型上定义）*********************************************************************************************************************************************************************************


        //初始化动态表单插件
        _child.prototype.onInitialized = function (e) {
            var that = this;

            var cp = that.formContext.cp;
            if (cp) {
                if ($.trim(cp.id)) {
                    that.Model.setVisible({ id: '[opcode=filterdelete]', value: true });
                }
                that.Model.setEnable({ id: 'fshareuser', way: 2, value: cp.fshare });

                //是否显示“将当前方案设置为快捷过滤方案”选项
                if (cp.isShowPreset) {
                    that.Model.setVisible({ id: '.y-ispreset', value: true });
                    that.Model.setVisible({ id: '.y-special-filter', value: true });
                }

                //设置页面标题
                var pageCaption = '';
                switch (cp.opType) {
                    case 'saveas':
                        pageCaption = '另存过滤方案';
                        break;
                    case 'edit':
                        pageCaption = '编辑过滤方案';
                        break;
                }
                that.Model.setPageCaption({ caption: pageCaption });
            }
        };

        //自身特有的操作
        _child.prototype.onMenuItemClick = function (e) {
            var that = this;
            switch (e.opcode.toLowerCase()) {
                    //删除
                case 'filterdelete':
                    e.result = true;
                    yiDialog.c('您确定要删除吗？', function () {
                        //设置对话框的返回数据
                        that.Model.setReturnData({
                            isSuccess: true,
                            opType: 'delete'
                        });
                        that.Model.close();
                    });
                    break;
                    //取消
                case 'filtercancel':
                    e.result = true;
                    that.Model.close();
                    break;
                    //保存
                case 'filtersave':
                    e.result = true;

                    var cloneData = that.Model.clone();
                    cloneData.id = $.trim(cloneData.id);
                    cloneData.fname = $.trim(cloneData.fname);
                    if (!cloneData.fname) {
                        yiDialog.error('请填写方案名称！');
                        return;
                    }
                    var filterSchemes = cloneData.filterSchemes;
                    if (filterSchemes) {
                        for (var i = 0, l = filterSchemes.length; i < l; i++) {
                            if (cloneData.id) {
                                if ($.trim(filterSchemes[i].id) !== cloneData.id && $.trim(filterSchemes[i].name) === cloneData.fname) {
                                    yiDialog.error('方案名称已存在，请重新填写！');
                                    return;
                                }
                            } else {
                                if ($.trim(filterSchemes[i].name) === cloneData.fname) {
                                    yiDialog.error('方案名称已存在，请重新填写！');
                                    return;
                                }
                            }
                        }
                    }
                    if (cloneData.fshare) {
                        if (!cloneData.fshareuser || !$.trim(cloneData.fshareuser.id)) {
                            yiDialog.error('当勾选共享时，则必须要选择共享的用户！');
                            return;
                        }
                    }
                    //设置对话框的返回数据
                    that.Model.setReturnData({
                        isSuccess: true,
                        opType: cloneData.opType,
                        data: {
                            id: cloneData.id,
                            name: cloneData.fname,
                            isPreset: cloneData.fispreset,
                            isShare: cloneData.fshare,
                            shareUser: cloneData.fshareuser.id,
                            shareUser_txt: cloneData.fshareuser.name,
                            order: cloneData.forder,
                            sumExpression: cloneData.fsumexpr,
                            filterString: cloneData.ffilterstring
                        }
                    });
                    that.Model.close();
                    break;
            }
        };

        //字段值改变后
        _child.prototype.onFieldValueChanged = function (e) {
            var that = this;
            switch (e.id.toLowerCase()) {
                case 'fshare':
                    that.Model.setEnable({ id: 'fshareuser', way: 2, value: e.value });
                    break;
            }
        };

        return _child;
    })(BasePlugIn);
    window.bas_filterschemeset = window.bas_filterschemeset || bas_filterschemeset;
})();