/// <reference path="/fw/js/BasePlugIn.js" />
/*
    @ sourceURL=/fw/js/bas/bas_executepara.js
*/
; (function () {
    var bas_executepara = (function (_super) {
        //构造函数
        var _child = function (args) {
            var that = this;
            _super.call(this, args);

        };
        //继承 BasePlugIn
        __extends(_child, _super);

        _child.prototype.onMenuItemClick = function (args) {
            var that = this;
            switch (args.opcode) {
                case "rightexecute"://执行
                    args.result = true;
                    //var fbegintime = that.Model.getValue({ id: "fbegintime" });
                    //var finterval = that.Model.getValue({ id: "finterval" });
                    //var fexecuteunit = that.Model.getSimpleValue({ id: "fexecuteunit" });
                    //var packet = { simpledata: { fbegintime: fbegintime, finterval: finterval, fexecuteunit: fexecuteunit } };
                    //yiAjax.p('/dynamic/bas_executepara?operationno=execute', packet, function (r) {
                    //    var data = r.operationResult.srvData;
                    //    if (data) {
                    //        var fbegintime = that.Model.setValue({ id: "fcron", value: data.cron });
                    //        that.Model.deleteEntryData({ id: "fentity" });
                    //        for (var i = 0; i < data.tasktime.length; i++) {
                    //            that.Model.addRow({ id: "fentity" });
                    //            that.Model.setValue({ id: "fexecutetime", row: i, value: data.tasktime[i] });
                    //        }
                    //    }

                    //});
                    var fsecond = that.Model.getValue({ id: "fsecond" });
                    var fminute = that.Model.getValue({ id: "fminute" });
                    var fhour = that.Model.getValue({ id: "fhour" });
                    var fdays = that.Model.getValue({ id: "fdays" });
                    var fmonth = that.Model.getValue({ id: "fmonth" });
                    var fweek = that.Model.getValue({ id: "fweek" });
                    var fyear = that.Model.getValue({ id: "fyear" });
                    var packet = { simpledata: { fsecond: fsecond, fminute: fminute, fhour: fhour, fdays: fdays, fmonth: fmonth, fweek: fweek, fyear: fyear } };
                    yiAjax.p('/dynamic/bas_executepara?operationno=execute', packet, function (r) {
                        var data = r.operationResult.srvData;
                        if (data) {
                            var fbegintime = that.Model.setValue({ id: "fcron", value: data.cron });
                            $('[name="ftranslatecron"]').text(data.translatecron);
                            that.Model.deleteEntryData({ id: "fentity" });
                            for (var i = 0; i < data.tasktime.length; i++) {
                                that.Model.addRow({ id: "fentity" });
                                that.Model.setValue({ id: "fexecutetime", row: i, value: data.tasktime[i] });
                            }
                        }

                    });
                    break;
                case "executeback": //返回
                    args.result = true;
                    var fcron=that.Model.getValue({ id: "fcron" });
                    if (!fcron) {
                        yiDialog.a("未设置执行计划！");
                        return;
                    }
                    var parentViewModel = Index.getPage(that.Model.viewModel.parentPageId);
                    if (parentViewModel) {
                        parentViewModel.Model.setValue({ id: "fexecuteplan", value: fcron });
                        that.Model.close();
                    }
                    break;

            }
        };

        return _child;
    })(BasePlugIn);
    window.bas_executepara = window.bas_executepara || bas_executepara;
})();