using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormMeta.ConvertElement;
using JieNor.Framework.MetaCore.FormOp.ConvertService;
using JieNor.Framework.MetaCore.FormOp.OpData;
using JieNor.Framework.SuperOrm;
using System.Collections.Generic;

namespace JieNor.Framework.Interface
{
    /// <summary>
    /// 单据转换接口定义
    /// </summary>
    public interface IConvertService
    {
        /// <summary>
        /// 单据转换--下推操作
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="convertArgs"></param>
        /// <returns></returns>
        IOperationResult Push(UserContext ctx, BillConvertContext convertArgs);

        /// <summary>
        /// 单据转换--选单操作
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="convertArgs"></param>
        /// <returns></returns>
        IOperationResult Pull(UserContext ctx, BillConvertContext convertArgs);

        /// <summary>
        /// 检验单据转换规则并报错
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="rule"></param>
        void ValidAndThrowError(UserContext ctx, ConvertRule rule);

        /// <summary>
        /// 判断指定数据是否有下游关联数据存在
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="htmlForm">当前表单模型</param>
        /// <param name="selectedRows">当前需要判断的数据上下文，到表格行明细</param>
        /// <param name="targetFormIds">不传默认判断所有下游，若传则判断指定下游</param>
        /// <param name="option"></param>
        /// <returns></returns>
        bool IsPush(UserContext ctx, HtmlForm htmlForm, IEnumerable<SelectedRow> selectedRows, IEnumerable<string> targetFormIds, OperateOption option);

        /// <summary>
        /// 保存单据转换数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="result"></param>
        /// <param name="isDraft"></param>
        /// <returns></returns>
        IOperationResult SaveConvertData(UserContext ctx, ConvertResult result, bool isDraft = false);

        /// <summary>
        /// 清空格临时关联生成的数据
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="hForm"></param>
        /// <returns></returns>
        IOperationResult ClearTempConvertData(UserContext ctx, HtmlForm hForm);


        /// <summary>
        /// 相关业务数据下载(同步)--下载操作
        /// </summary>
        /// <param name="ctx"></param>
        /// <param name="cf"></param>
        /// <returns></returns>
        IOperationResult Download(UserContext ctx, DownloadConfig cf);

    }
}
