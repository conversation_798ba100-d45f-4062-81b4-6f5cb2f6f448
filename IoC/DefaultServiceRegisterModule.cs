using Autofac;
using Autofac.Core;
using Autofac.Extras.AttributeMetadata;
using log4net;
using ServiceStack;
using ServiceStack.Caching;
using ServiceStack.Redis;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Reflection;

namespace JieNor.Framework.IoC
{
    /// <summary>
    /// 默认服务注册模块
    /// </summary>
    public class DefaultServiceRegisterModule : Autofac.Module
    {  
        log4net.ILog logger = log4net.LogManager.GetLogger("biz");

        /// <summary>
        /// 服务容器
        /// </summary>
        public Funq.Container ServiceContainer { get; set; }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="builder"></param>
        protected override void Load(ContainerBuilder builder)
        {
            base.Load(builder);

            builder.RegisterModule<AttributedMetadataModule>();

            //对通用服务行为进行注册，此处仅注册JieNor的组件
            //var assemblyList = AppDomain.CurrentDomain.GetAssemblies();
            var allAssemblyPrefix = HostConfigView.Site.IoCPrefix.Split(',', ';');

            logger?.Info($"ioc load pattern:{allAssemblyPrefix}");

            var allLoadedAssemblyList = AssemblyUtils.AppCodeAssemblies;

            foreach (var assembly in allLoadedAssemblyList)
            {
                logger?.Info($"web load assembly:{assembly.FullName}");
            }

            var allAssemblyList = allLoadedAssemblyList
                .Where(t => allAssemblyPrefix.Any(o=> t.FullName.StartsWith(o)))
                .Distinct()
                .ToArray();
            AssemblyUtils.AssemblyList = allAssemblyList;
                        
            foreach(var assembly in allAssemblyList)
            {
                logger?.Info($"autofac register assembly:{assembly.FullName}");
            }

            //提供统一的服务容器
            builder.RegisterInstance<Funq.Container>(this.ServiceContainer);
             
            ////分布式的物理缓存
            RedisConfig.DefaultMaxPoolSize = HostConfigView.Redis.MaxPoolSize;
            RedisConfig.DefaultConnectTimeout = HostConfigView.Redis.ConnectTimeout * 1000;
            RedisConfig.DefaultReceiveTimeout = HostConfigView.Redis.ReceiveTimeout * 1000;
            RedisConfig.DefaultSendTimeout = HostConfigView.Redis.SendTimeout * 1000;
            RedisConfig.DefaultRetryTimeout = HostConfigView.Redis.RetryTimeout * 1000;
            var masterHost = HostConfigView.Redis.MasterHost;
            var slaveHost = HostConfigView.Redis.SlaveHost;
            var redisMgr = new PooledRedisClientManager();
            if (!masterHost.IsNullOrEmptyOrWhiteSpace())
            {
                redisMgr.RedisResolver.ResetMasters(masterHost.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries));
            }
            if (!slaveHost.IsNullOrEmptyOrWhiteSpace())
            {
                redisMgr.RedisResolver.ResetSlaves(slaveHost.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries));
            }

            builder.RegisterInstance<IRedisClientsManager>(redisMgr)
                .SingleInstance();

            //var registration = builder.RegisterAssemblyTypes(allAssemblyList);


            builder.RegisterAssemblyTypes(allAssemblyList)
                .Where(t => !t.IsGenericType()
                                    && t.FirstAttribute<InjectServiceAttribute>() != null
                                    && (t.FirstAttribute<LifetimeScopeAttribute>() == null || t.FirstAttribute<LifetimeScopeAttribute>().Scope == Enu_LifetimeScope.InstancePerLifetimeScope))
                .AsImplementedInterfaces()
                .AsSelf()
                .PropertiesAutowired(new DelegatePropertySelector((p, instance) =>
                {
                    return p.FirstAttribute<InjectPropertyAttribute>() != null;
                }))
                .WithMetadataFrom<InjectServiceAttribute>()
                .InstancePerLifetimeScope();

            builder.RegisterAssemblyTypes(allAssemblyList)
                .Where(t => !t.IsGenericType()
                                    && t.FirstAttribute<InjectServiceAttribute>() != null
                                    && (t.FirstAttribute<LifetimeScopeAttribute>() != null && t.FirstAttribute<LifetimeScopeAttribute>().Scope == Enu_LifetimeScope.InstancePerDependency))
                .AsImplementedInterfaces()
                .AsSelf()
                .PropertiesAutowired(new DelegatePropertySelector((p, instance) =>
                {
                    return p.FirstAttribute<InjectPropertyAttribute>() != null;
                }))
                .WithMetadataFrom<InjectServiceAttribute>()
                .InstancePerDependency();

            builder.RegisterAssemblyTypes(allAssemblyList)
                .Where(t => !t.IsGenericType()
                                    && t.FirstAttribute<InjectServiceAttribute>() != null
                                    && (t.FirstAttribute<LifetimeScopeAttribute>() != null && t.FirstAttribute<LifetimeScopeAttribute>().Scope == Enu_LifetimeScope.ExternalOwned))
                .AsImplementedInterfaces()
                .AsSelf()
                .PropertiesAutowired(new DelegatePropertySelector((p, instance) =>
                {
                    return p.FirstAttribute<InjectPropertyAttribute>() != null;
                }))
                .WithMetadataFrom<InjectServiceAttribute>()
                .ExternallyOwned();

            builder.RegisterAssemblyTypes(allAssemblyList)
                .Where(t => !t.IsGenericType()
                                    && t.FirstAttribute<InjectSingletonServiceAttribute>() != null)
                .AsImplementedInterfaces()
                .AsSelf()
                .PropertiesAutowired(new DelegatePropertySelector((p, instance) =>
                {
                    return p.FirstAttribute<InjectPropertyAttribute>() != null;
                }))
                .SingleInstance();

            var allRegisterTypes = allAssemblyList.SelectMany(o => o.ExportedTypes)
                .Where(t => t.IsGenericType() && (t.FirstAttribute<InjectServiceAttribute>() != null
            || t.FirstAttribute<InjectSingletonServiceAttribute>() != null));
            foreach (var type in allRegisterTypes)
            {

                if (type.FirstAttribute<InjectSingletonServiceAttribute>() != null)
                {
                    builder.RegisterGeneric(type)
                        .AsSelf()
                        .AsImplementedInterfaces()
                        .PropertiesAutowired(new DelegatePropertySelector((p, instance) =>
                        {
                            return p.FirstAttribute<InjectPropertyAttribute>() != null;
                        }))
                        .SingleInstance();
                }
                else
                {
                    var lifeTimeAttr = type.FirstAttribute<LifetimeScopeAttribute>();
                    if (lifeTimeAttr != null)
                    {
                        switch (lifeTimeAttr.Scope)
                        {
                            case Enu_LifetimeScope.ExternalOwned:
                                builder.RegisterGeneric(type)
                                    .AsSelf()
                                    .AsImplementedInterfaces()
                                    .PropertiesAutowired(new DelegatePropertySelector((p, instance) =>
                                    {
                                        return p.FirstAttribute<InjectPropertyAttribute>() != null;
                                    }))
                                    .ExternallyOwned();
                                break;
                            case Enu_LifetimeScope.InstancePerLifetimeScope:
                                builder.RegisterGeneric(type)
                                    .AsSelf()
                                    .AsImplementedInterfaces()
                                    .PropertiesAutowired(new DelegatePropertySelector((p, instance) =>
                                    {
                                        return p.FirstAttribute<InjectPropertyAttribute>() != null;
                                    }))
                                    .InstancePerLifetimeScope();
                                break;
                        }
                    }
                    else
                    {
                        builder.RegisterGeneric(type)
                            .AsSelf()
                            .AsImplementedInterfaces()
                            .PropertiesAutowired(new DelegatePropertySelector((p, instance) =>
                            {
                                return p.FirstAttribute<InjectPropertyAttribute>() != null;
                            }))
                            .InstancePerDependency();
                    }
                }
            }


            builder.Register((c) =>
            {
                return LogManager.GetLogger(this.GetType());
            });

            //注册dto模块 
            builder.RegisterModule(new DtoIoCModule());

            //允许对特定行为注册进行自定义处理
            builder.RegisterAssemblyModules<CustomeServiceRegisterModule>(allAssemblyList.Except(new Assembly[] { ThisAssembly }).ToArray());
            
        }


        private static void InjectLoggerProperties(ActivatedEventArgs<object> e)
        {
            var instanceType = e.Instance.GetType();

            // Get all the injectable properties to set.
            // If you wanted to ensure the properties were only UNSET properties,
            // here's where you'd do it.
            var properties = instanceType
              .GetProperties(BindingFlags.Public | BindingFlags.Instance)
              .Where(p => p.PropertyType == typeof(ILog) && p.CanWrite && p.GetIndexParameters().Length == 0);

            // Set the properties located.
            foreach (var propToSet in properties)
            {
                propToSet.SetValue(e.Instance, LogManager.GetLogger(instanceType), null);
            }
        }

        private static void OnComponentPreparing(object sender, PreparingEventArgs e)
        {
            e.Parameters = e.Parameters.Union(
              new[]
              {
                    new ResolvedParameter(
                        (p, i) => p.ParameterType == typeof(ILog),
                        (p, i) => LogManager.GetLogger(p.Member.DeclaringType)
                    ),
              });
        }

        protected override void AttachToComponentRegistration(IComponentRegistry componentRegistry, IComponentRegistration registration)
        {
            // Handle constructor parameters.
            registration.Preparing += OnComponentPreparing;

            // Handle properties.
            registration.Activated += (sender, e) => InjectLoggerProperties(e);
        }
    }
}
