using Autofac;
using Autofac.Features.Metadata;
using JieNor.Framework.DataEntity.Sync;
using JieNor.Framework.DataTransferObject;
using JieNor.Framework.DataTransferObject.Meta;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.DataEntity;
using JieNor.Framework.SuperOrm.DataManager;
using Newtonsoft.Json.Linq;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.WebController.Meta
{
    /// <summary>
    /// 查看表单结构的接口
    /// </summary>
    public class FormDataStructureService:ServiceStack.Service
    {
        /// <summary>
        /// 强制协同的字段
        /// </summary>
        private List<string> ForceSyncFieldKeys = new List<string>
        {
            "ftranid"
        };

        /// <summary>
        /// 不需要返回的字段标识
        /// </summary>
        private List<string> NotRequiredFieldKeys = new List<string>
        {
            "fcreatorid","fcreatedate","fmodifierid","fmodifydate","fstatus","fapproveid","fapprovedate",
            "fcancelstatus","fcancelid","fcanceldate","fcloseid","fclosedate","fmainorgid","fbizruleid",
            "fflowinstanceid","fsourcetype","fsourcenumber","fpublishcid","fispreset","fforbidstatus",
            "fforbidid","fforbiddate","fsendstatus","fdataorigin","fsenddate","fdownloaddate","ftranid",
            "fcloguserid", "fclogcheckdate", "fclogisagree", "fclogcheckreason", "fchecklevelid", "fcheckleveldesc", "fcloghandler"
        };

        /// <summary>
        /// 查看表单数据结构
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        [AddHeader(ContentType = "text/html")]
        public object Get(FormDataStructureDTO dto)
        {
            var company = this.GetDefaultCompany();
            var userCtx = this.CreateContextByCompanyInfo(company);
            var container = this.TryResolve<IServiceContainer>().BeginLifetimeScope(Guid.NewGuid().ToString());
            var metaService = container.GetService<IMetaModelService>();
            var formMeta = metaService?.LoadFormModel(userCtx, dto.FormId);
            if (formMeta == null)
            {
                return $"表单{dto.FormId}模型不存在！";
            }
            var billTempObj = this.LoadExistBillObject(userCtx, formMeta);

            OperateOption option = OperateOption.Create();
            var uiService = container.GetService<IUiDataConverter>();

            if (string.IsNullOrWhiteSpace(dto.OperationNo) == false)
            {
                userCtx.Container = container;
                var list = userCtx.Container.GetService<IEnumerable<Meta<Lazy<IFormDataStructureService>>>>()
                    .Where(o => o.Metadata.GetString("AliasName").EqualsIgnoreCase(dto.OperationNo));
                object result = null;
                if (list != null)
                {
                    foreach(var item in list)
                    {
                        item.Value.Value.HtmlForm = formMeta;
                        item.Value.Value.UiDataConverter = uiService;
                        item.Value.Value.Context = userCtx;
                        var itemResult = item.Value.Value.BuildingFormDataStructure(billTempObj, option);
                        if (itemResult != null)
                        {
                            result = itemResult;
                        }
                    }
                }
                if (result == null)
                {
                    result= @"<html><title>错误页面</title><body><div><h4>无此页面</h4></div></body></html>";
                }
                return result;
            }

            List<Tuple<HtmlOperation, HtmlBizService, string>> lstTplData = new List<Tuple<HtmlOperation, HtmlBizService, string>>();

            var allSyncFieldKeys = new List<string>();

            foreach (var hOpItem in formMeta.FormOperations)
            {
                foreach(var hBizItem in hOpItem.OperationServices)
                {
                    if (hBizItem.ServiceId.EqualsIgnoreCase(HtmlElementType.HtmlBizService_DataSyncSwopService))
                    {
                        //todo:反序列化服务配置的参数，取得要打包的字段，然后调用打包服务生成
                        var servicePara = hBizItem.Parameter.FromJson<DataSyncSwopSetting>(true);
                        option.SetOptionFlag(Enu_OpFlags.TPSRequest);
                        var syncFieldKeys = this.ForceSyncFieldKeys;
                        syncFieldKeys.AddRange(servicePara.SyncFieldKeys);
                        option.SetSyncFieldKeys(syncFieldKeys);
                        option.SetAuxPropSyncFieldKeys(servicePara.SyncAuxPropFieldKeys);

                        allSyncFieldKeys.AddRange(syncFieldKeys);

                        var jsonBillData = uiService.CreateUIDataObject(userCtx, formMeta, billTempObj, option);
                        var uiDataObj = jsonBillData.GetJsonValue("uidata", new JObject());
                        lstTplData.Add(Tuple.Create(hOpItem, hBizItem, uiDataObj.ToJson(true)));
                    }
                }
            }

            StringBuilder sbHtmlBuilder = new StringBuilder();
            sbHtmlBuilder.Append(@"
<html lang=""en"">
<head>
    <title>业务对象协同数据结构</title>
    <style>
        ul,li{margin:0;padding:0;}
        ul{list-style:none;height:100%;clear:both;}
        ul li{float:left;}
    </style>
</head>
<body>
    <ul>");

            sbHtmlBuilder.Append($"<li style=\"width:45%;\"><h4>表单（{formMeta.Caption}/{formMeta.Id}）发生协同时发送的数据包结构按操作关联性列示如下：</h4>");
            foreach (var tplItem in lstTplData)
            {
                sbHtmlBuilder
                    .Append($"<p>操作({tplItem.Item1.Caption}/{tplItem.Item1.Id})配置的协同服务（{tplItem.Item2.Caption}/{tplItem.Item2.ServiceId}）打包数据结构如下：</p>")
                    .Append(tplItem.Item3.Replace("\r\n", "\r\n<br>\r\n").Replace(" ", "&nbsp;"));
            }
            sbHtmlBuilder.Append("</li>");

            //生成所有协同字段的模型信息
            var strMeta = this.BuildAllSyncFieldMeta(formMeta, allSyncFieldKeys);
            sbHtmlBuilder.Append($"<li style=\"width:25%;\">{strMeta}</li>");

            //顺便把所有字段标识也返回，方便开发用于配置操作服务参数
            var strBizFieldKeys = this.BuildFormBizFieldKeys(formMeta);
            sbHtmlBuilder.Append($"<li style=\"width:30%;\">{strBizFieldKeys}</li>");

            sbHtmlBuilder.Append(@"
    </ul>
</body>
</html>");

            return sbHtmlBuilder.ToString();
        }

        /// <summary>
        /// 生成所有协同字段的模型信息
        /// </summary>
        /// <param name="formMeta"></param>
        /// <param name="allSyncFieldKeys"></param>
        /// <param name="sbHtmlBuilder"></param>
        private string BuildAllSyncFieldMeta(HtmlForm formMeta, List<string> allSyncFieldKeys)
        {
            allSyncFieldKeys = allSyncFieldKeys.Distinct().ToList();
            var syncFieldMate = new Dictionary<string, object>();
            foreach (var syncFieldKey in allSyncFieldKeys)
            {
                var syncField = formMeta.GetField(syncFieldKey);
                if (syncField == null) continue;

                var syncFieldMeta = new Dictionary<string, object>();
                syncFieldMeta["caption"] = syncField.Caption;
                syncFieldMeta["apiPropertyName"] = syncField.ApiPropertyName;
                syncFieldMeta["type"] = syncField.GetFieldPropertyType().Name;
                syncFieldMeta["length"] = syncField.Length;
                syncFieldMeta["entityKey"] = syncField.EntityKey;
                syncFieldMeta["description"] = syncField.Description;

                syncFieldMate[syncFieldKey] = syncFieldMeta;
            }

            StringBuilder sbMeta = new StringBuilder();
            sbMeta.Append($@"<h4>{formMeta.Caption}/{formMeta.Id}所有业务字段信息：</h4>");
            sbMeta.Append(syncFieldMate.ToJson(true)?.Replace("\r\n", "\r\n<br>\r\n").Replace(" ", "&nbsp;")).Append("<br><br>");

            return sbMeta.ToString();
        }

        /// <summary>
        /// 生成表单业务字段标识
        /// </summary>
        /// <param name="formMeta"></param>
        /// <returns></returns>
        private string BuildFormBizFieldKeys(HtmlForm formMeta)
        {
            var allFieldKeys = formMeta.GetFieldList().Where(f =>
            {
                //排除基类模型中一些不需要的字段
                return !NotRequiredFieldKeys.Contains(f.Id, StringComparer.OrdinalIgnoreCase) && !f.Id.EndsWithIgnoreCase("_ftranid");

            }).Select(f => f.Id).ToList();

            StringBuilder sbFieldKeys = new StringBuilder();
            sbFieldKeys.Append($@"<h4>{formMeta.Caption}/{formMeta.Id}所有业务字段标识（便于mdl文件中配置操作服务参数）：</h4>");
            sbFieldKeys.Append(allFieldKeys.ToJson(true).Replace("\"", "'")).Append("<br><br>");

            return sbFieldKeys.ToString();
        }

        private DynamicObject LoadExistBillObject(UserContext userCtx, HtmlForm formMeta)
        {
            var strSql = $"select top 1 {formMeta.BillPKFldName} from {formMeta.BillHeadTableName} ";
            var dbService = userCtx.Container.GetService<IDBService>();
            var dm = userCtx.Container.GetService<IDataManager>();
            dm.InitDbContext(userCtx, formMeta.GetDynamicObjectType(userCtx));
            var reader = dbService.ExecuteReader(userCtx, strSql);
            var billObj = dm.SelectBy(reader)
                .OfType<DynamicObject>()
                .FirstOrDefault();

            if (billObj != null)
            {
                //加载引用数据
                userCtx.Container.GetService<LoadReferenceObjectManager>()
                    ?.Load(userCtx, formMeta.GetDynamicObjectType(userCtx), billObj, false);

                foreach (var entity in formMeta.EntryList)
                {
                    if (entity is HtmlSubEntryEntity) continue;

                    //只保留一行明细
                    var entryObj = entity.DynamicObjectType.CreateInstance() as DynamicObject;
                    var entryObjs = entity.DynamicProperty.GetValue<DynamicObjectCollection>(billObj);
                    if (entryObjs.Count > 0) entryObj = entryObjs[0];
                    entryObjs.Clear();
                    entryObjs.Add(entryObj);

                    if (entity.SubEntryList != null)
                    {
                        foreach (var subEntity in entity.SubEntryList)
                        {
                            //只保留一行明细
                            var subEntryObj = subEntity.DynamicObjectType.CreateInstance() as DynamicObject;
                            var subEntryObjs = subEntity.DynamicProperty.GetValue<DynamicObjectCollection>(entryObj);
                            if (subEntryObjs.Count > 0) subEntryObj = subEntryObjs[0];
                            subEntryObjs.Clear();
                            subEntryObjs.Add(subEntryObj);
                        }
                    }
                }

                return billObj;
            }

            var billTempObj = formMeta.GetDynamicObjectType(userCtx).CreateInstance() as DynamicObject;
            foreach (var entity in formMeta.EntryList)
            {
                if (entity is HtmlSubEntryEntity) continue;

                var entryObjs = entity.DynamicProperty.GetValue<DynamicObjectCollection>(billTempObj);
                var entryObj = entity.DynamicObjectType.CreateInstance() as DynamicObject;
                entryObjs.Add(entryObj);

                if (entity.SubEntryList != null)
                {
                    foreach (var subEntity in entity.SubEntryList)
                    {
                        var subEntryObjs = subEntity.DynamicProperty.GetValue<DynamicObjectCollection>(entryObj);
                        var subEntryObj = subEntity.DynamicObjectType.CreateInstance() as DynamicObject;
                        subEntryObjs.Add(subEntryObj);
                    }
                }
            }

            return billTempObj;
        }
    }
}
