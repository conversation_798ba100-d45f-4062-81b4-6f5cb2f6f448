using JieNor.Framework.Consts;
using JieNor.Framework.Interface;
using JieNor.Framework.IoC;
using JieNor.Framework.SuperOrm.DataEntity;
using System.Collections.Generic;
using System;
using JieNor.Framework.SuperOrm.DataManager;
using JieNor.Framework.DataTransferObject.Report;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.SuperOrm.Drivers;
using JieNor.Framework.CustomException;
using System.Linq;
using JieNor.Framework.DataTransferObject.Print;
using JieNor.Framework.MetaCore.FormModel.Office;
using JieNor.Framework.DataTransferObject.Poco;
using JieNor.Framework.Interface.BizTask;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;

namespace JieNor.Framework.AppService.OperationService
{
    /// <summary>
    /// 套打
    /// </summary>
    [InjectService("PrintTmpl")]
    public class PrintTmpl : AbstractOperationService
    {

        protected override string OperationName
        {
            get
            {
                return "套打";
            }
        }

        /// <summary>
        /// 权限项
        /// </summary>
        protected override string PermItem
        {
            get
            {
                return PermConst.PermssionItem_Print;
            }
        }

        static bool _isUpdateMdl = false;


        protected override void DoExecute(ref DynamicObject[] dataEntities)
        {
            var svc = this.Container.GetService<IPrintService>();
            if (svc == null)
            {
                this.OperationContext.Result.IsSuccess = false;
                this.OperationContext.Result.IsShowMessage = true;
                this.OperationContext.Result.SimpleMessage = "套打服务未实现";
                return;
            }
            //自动补偿关联资料的表结构
            if (_isUpdateMdl == false)
            {
                _isUpdateMdl = true;
                var meta = this.MetaModelService.LoadFormModel(this.UserCtx, "bas_officetmpl");
                var dm = this.GetDataManager();
                dm.InitDbContext(this.UserCtx, meta.GetDynamicObjectType(this.UserCtx));

                var mdl = this.MetaModelService.LoadFormModel(this.UserCtx, "bas_officetmplsetting");
                dm.InitDbContext(this.UserCtx, mdl.GetDynamicObjectType(this.UserCtx));
            }

            var tmplId = this.GetQueryOrSimpleParam<string>("templateId", "");
            PrintOption templete = svc.GetPrintTmpl(UserCtx, this.OperationContext.HtmlForm.Id, tmplId);
            if (templete == null)
            {
                throw new Exception(string.Format("{0} 未定义套打模板，无法进行预览及打印", this.OperationContext.HtmlForm.Caption));
            }
            templete.IsExport = this.GetQueryOrSimpleParam<bool>("exportExcel", false);
            //获取表单勾选的明细数据，如果存在则过滤未勾选明细行
            var simSelData = this.GetQueryOrSimpleParam<string>("simSelData", "");
            var htmlType = this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx);
            List<DynamicObject> copyEntities =new List<DynamicObject>();
            foreach (var item in dataEntities)
            {
                copyEntities.Add(item.Clone(false,false) as DynamicObject);
            }
            if (!simSelData.IsNullOrEmptyOrWhiteSpace())
            {
                Dictionary<string, List<string>> selData = simSelData.FromJson<Dictionary<string, List<string>>>();
                if (selData.Count > 0)
                {
                    foreach (var entry in OperationContext.HtmlForm.EntryList)
                    {
                        if (entry is HtmlSubEntryEntity || !selData.Keys.Contains(entry.Id))
                        {
                            continue;
                        }
                        List<string> selRowIds = selData[entry.Id];
                        foreach (var dataEntity in copyEntities)
                        {
                            var enRows = dataEntity[entry.Id] as DynamicObjectCollection;
                            var beDel = new List<DynamicObject>();
                            if (enRows == null || enRows.Count == 0) continue;
                            foreach (var item in enRows.Where(x => !selRowIds.Contains(x["id"])))
                            {
                                beDel.Add(item);//不满足条件，不打印
                            }
                            foreach (var item in beDel)
                            {
                                enRows.Remove(item);
                            }
                        }
                    }
                }
            }

            //允许业务插件自行设置打印的数据及mdl
            var printDatas = from p in copyEntities
                             select new PrintData() { BizData = p };
            var printContext = new PrintContext(this.OperationContext.UserContext)
            {
                BizDatas = printDatas.ToList(),
                FormMeta = this.OperationContext.HtmlForm,
                PrintOption = templete
            };
            var ae = new OnCustomServiceEventArgs()
            {
                EventName = OnCustomServiceEventArgs.BeforPrintParameter ,
                EventData = printContext, 
                DataEntities=dataEntities ,                 
            };
            this.PlugInProxy.InvokePlugInMethod("OnCustomServiceEvent", ae);

            var offCtx = svc.BuildPrintContext(this.UserCtx, printContext.FormMeta , printContext.BizDatas , printContext.PrintOption);
            var url = svc.PrintWithTemplete(offCtx);
            if (url.IsNullOrEmptyOrWhiteSpace())
            {
                this.OperationContext.Result.ComplexMessage.WarningMessages.Add("打印失败，请检查打印模版是否配置正确！");
                this.OperationContext.Result.IsShowMessage = true;
                this.OperationContext.Result.IsSuccess = false;
                return;
            }

            url = url.Replace("\\", "/");
            url = url.Substring(url.LastIndexOf("/prints"));
            this.OperationContext.Result.IsShowMessage = false;
            this.OperationContext.Result.IsSuccess = true;
            this.OperationContext.Result.SrvData = url;

        }

        protected override void AfterExecute(ref DynamicObject[] dataEntities)
        {
            base.AfterExecute(ref dataEntities);

            if (dataEntities == null || dataEntities.Length <= 0)
            {
                return;
            }

            var fprintcountkey = "fprintcount";
            var fprintidkey = "fprintid";
            var fprintdatekey = "fprintdate";
            var fheadfields = OperationContext.HtmlForm.GetFieldList().Where(x => x.IsBillHeadField);
            var fprintcount = fheadfields.FirstOrDefault(x => string.Equals(x.Id, fprintcountkey, StringComparison.OrdinalIgnoreCase)) as HtmlPrintCountField;
            var fprintid = fheadfields.FirstOrDefault(x => string.Equals(x.Id, fprintidkey, StringComparison.OrdinalIgnoreCase)) as HtmlUserField;
            var fprintdate = fheadfields.FirstOrDefault(x => string.Equals(x.Id, fprintdatekey, StringComparison.OrdinalIgnoreCase)) as HtmlDateTimeField;

            if (fprintcount == null || fprintid == null || fprintdate == null || fprintcount.Entity.IsView)
            {
                return;
            }
            List<string> sqllist = new List<string>();
            foreach (var dataEntity in dataEntities)
            {
                //dataEntity[fprintcountkey] = Convert.ToInt32(dataEntity[fprintcountkey]) + 1;
                //dataEntity[fprintidkey] = OperationContext.UserContext.UserId;
                //dataEntity[fprintdatekey] = DateTime.Now;

                // 用平台的ORM来保存打印记录时，会携带其他字段进行保存，所以这里改为用sql只更新打印记录相关字段

                string strsql = $@"
update {fprintcount.Entity.TableName} 
set {fprintcount.FieldName}={Convert.ToInt32(dataEntity[fprintcountkey]) + 1},
{fprintid.FieldName}='{OperationContext.UserContext.UserId}',
{fprintdate.FieldName}='{DateTime.Now}'
where fid='{dataEntity["Id"]}'
";
                sqllist.Add(strsql);
            }

            if (sqllist.Count <= 0)
            {
                return;
            }


            var dbService = this.Container.GetService<IDBServiceEx>();
            dbService.ExecuteBatch(this.UserCtx, sqllist);


            //var dm = this.Container.GetService<IDataManager>();
            //dm.InitDbContext(this.UserCtx, this.OperationContext.HtmlForm.GetDynamicObjectType(this.UserCtx));
            //dm.Save(dataEntities);
        }

        private OfficeContext BuildPrintContext(DynamicObject[] dataEntities)
        {
            //加载引用数据
            var dt = this.OperationContext.HtmlForm.GetDynamicObjectType(UserCtx);
            this.OperationContext.Container.GetService<LoadReferenceObjectManager>()?.Load(this.UserCtx, dt, dataEntities, false);

            OfficeContext offCtx = new OfficeContext(this.UserCtx);
            PrintDataGroupInfor printData = new PrintDataGroupInfor();
            foreach (var item in dataEntities)
            {
                printData.AddBizData(new PrintDataInfor(this.OperationContext.HtmlForm,new PrintData() { BizData = item }));
            }
            offCtx.AddBizData(printData);
            return offCtx;
        }





    }
}
