using JieNor.Framework.DataTransferObject.Report;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.Interface.QueryBuilder
{

    /// <summary>
    /// 数据查询规则：主要应用在各业务系统自定义的数据隔离（数据授权）功能
    /// </summary>
    public interface IDataQueryRule
    {

        /// <summary>
        /// 业务单据自定义的数据隔离条件
        /// </summary>
        /// <param name="ctx">上下文信息</param>
        /// <param name="para">数据查询规则参数信息</param> 
        /// <returns></returns>
        IEnumerable<FilterRowObject> GetDataRowACLFilter(UserContext ctx, DataQueryRuleParaInfo para);

    }



    /// <summary>
    /// 数据查询规则参数信息
    /// </summary>
    public class DataQueryRuleParaInfo
    {

        /// <summary>
        /// 当前要进行查询的业务对象标识
        /// </summary>
        public string FormId { get; set; }


        /// <summary>
        /// 来源业务对象标识（从哪个业务界面查找的数据，比如从采购订单上选择商品，则srcformid=采购订单，formId=商品）
        /// </summary>
        public string SrcFormId { get; set; }


        /// <summary>
        /// 从业务界面上的哪个字段查找的数据，比如从采购订单上选择商品，则srcformid=采购订单，formId=商品，SrcFldId=商品字段
        /// </summary>
        public string SrcFldId { get; set; }


        /// <summary>
        /// 来源单的相关数据
        /// </summary>
        public Dictionary<string, string> SrcPara { get; set; } = new Dictionary<string, string>( StringComparer.OrdinalIgnoreCase);

    }


}
