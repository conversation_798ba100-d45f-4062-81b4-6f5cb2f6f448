using JieNor.Framework.DataTransferObject;
using JieNor.Framework.MetaCore.MetaAttribute;

namespace JieNor.Framework.MetaCore.FormMeta
{
    /// <summary>
    /// 校验对象定义
    /// </summary>
    public class HtmlValidation : HtmlElement
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public HtmlValidation() : this(string.Empty) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id"></param>
        public HtmlValidation(string id) : base(id)
        {
            this.ElementType = HtmlElementType.HtmlForm_OpValidation;
            this.EntityKey = "fbillhead";
        }

        /// <summary>
        /// 校验器Id，关联IoC处理服务的别名
        /// </summary>
        [HtmlAttribute(HtmlAttributeKey.ValidationId)]
        public string ValidationId { get; set; }

        /// <summary>
        /// 校验器关联配置参数
        /// </summary>
        [HtmlAttribute(HtmlAttributeKey.Parameter)]
        public string Parameter { get; set; }


        /// <summary>
        /// 校验执行的前置条件
        /// </summary>
        [HtmlAttribute(HtmlAttributeKey.Precondition)]
        public string Precondition { get; set; }

        /// <summary>
        /// 作用分录标识
        /// </summary>
        [HtmlAttribute(HtmlAttributeKey.EntityKey)]
        public string EntityKey { get; set; }
    }
}
