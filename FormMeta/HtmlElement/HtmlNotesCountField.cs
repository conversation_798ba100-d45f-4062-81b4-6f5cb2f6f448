using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.MetaCore.FormMeta.ValueConverter;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.MetaCore.FormMeta
{
    /// <summary>
    /// 批注数 字段
    /// </summary>
    [Serializable]
    [HtmlDesignerElement]
    [Caption("批注数")]
    [Group("常规字段")]
    [ElementType(130)]
    [Icon("/images/designer/notescount.png")]
    [DesignTemplate(@"
<div class=""form-group unit"">
    <label class=""col-md-3 control-label"">整数</label>
    <div class=""col-md-8 cols"">
        <div class=""input-icon right"">
            <input type=""text"" class=""form-control"" autocomplete=""off"" name="""" placeholder="""" maxlength=""50"">
        </div>
    </div>
</div>
", "")]
    public class HtmlNotesCountField : HtmlIntegerField
    {
        public HtmlNotesCountField() : this(string.Empty)
        {
        }

        public HtmlNotesCountField(string id) : base(id)
        {
            this.ElementType = HtmlElementType.HtmlField_NotesCount;
            CanImport = 0;
        }
        

    }
}
