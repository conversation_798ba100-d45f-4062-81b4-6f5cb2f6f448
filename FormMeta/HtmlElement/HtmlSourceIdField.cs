using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Meta.Designer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.MetaCore.FormMeta
{
    /// <summary>
    /// 源单编号字段
    /// </summary>
    [HtmlDesignerElement]
    [Caption("源单id")]
    [Group("常规字段")]
    [ElementType(165)]
    [Icon("/images/designer/sourceid.png")]
    [DesignTemplate("", "")]
    public class HtmlSourceIdField : HtmlTextField
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public HtmlSourceIdField() : this(string.Empty) { }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id"></param>
        public HtmlSourceIdField(string id) : base(id)
        {
            this.ElementType = HtmlElementType.HtmlField_SourceIdField;
            this.Lock = -1;
        }
    }
}
