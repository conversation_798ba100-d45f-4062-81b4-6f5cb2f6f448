using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Meta.Designer;

namespace JieNor.Framework.MetaCore.FormMeta
{
    /// <summary>
    /// 表单多选字段的字段模型
    /// </summary>
    [HtmlDesignerElement]
    [Caption("多选模型字段")]
    [Group("常规字段")]
    [ElementType(142)]
    [Icon("/images/designer/mulfieldmodel.png")]
    [DesignTemplate("", "")]
    public class HtmlMulFieldModelField : HtmlFieldModelField
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public HtmlMulFieldModelField() : this(string.Empty)
        {

        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="id"></param>
        public HtmlMulFieldModelField(string id) : base(id)
        {
            this.ElementType = HtmlElementType.HtmlField_MulFieldModelField;
        }
    }
}