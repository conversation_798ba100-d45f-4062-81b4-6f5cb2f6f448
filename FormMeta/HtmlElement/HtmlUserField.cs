using JieNor.Framework.DataTransferObject;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.SuperOrm.Metadata.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace JieNor.Framework.MetaCore.FormMeta
{
    /// <summary>
    /// 用户字段
    /// </summary>
    [Serializable]
    [HtmlDesignerElement]
    [Caption("用户")]
    [Group("常规字段")]
    [ElementType(124)]
    [Icon("/images/designer/user.png")]
    [DesignTemplate(@"
<div class=""form-group unit"">
    <label class=""col-md-3 control-label"">用户</label>
    <div class=""col-md-8 cols"">
        <div class=""input-icon right"">
            <input type=""text"" class=""form-control"" autocomplete=""off"" name="""" />
        </div>
    </div>
</div>
", "")]
    public class HtmlUserField : HtmlBaseDataField
    {
        public HtmlUserField() : this(string.Empty) { }

        public HtmlUserField(string id) : base(id)
        {
            this.ElementType = HtmlElementType.HtmlField_UserField;
        }

        /// <summary>
        /// 用户字段只能指向sec_user
        /// </summary>
        public override string RefFormId
        {
            get
            {
                return "sec_user";
            }

            set
            {
                base.RefFormId = value;
            }
        }
    }
}
