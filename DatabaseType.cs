namespace JieNor.Framework
{
    /// <summary>
    /// 数据库类型定义
    /// <seealso cref="JieNor.Framework.DatabaseType"/> 
    /// </summary>
    public enum DatabaseType
    {
        MS_SQL_Server = 3,
        Oracle = 2,
        Oracle9 = 7,
        Oracle10 = 8
    }

    /// <summary>
    /// 数据中心类型
    /// </summary>
    public enum DataCenterType
    {
        /// <summary>
        /// 管理中心
        /// </summary>
        ManagementCenter = 1,
        /// <summary>
        /// 业务数据中心
        /// </summary>
        BusinessDataCenter = 2,
        /// <summary>
        /// 多语言中心
        /// </summary>
        MultiLanguageCenter = 3
    }

    /// <summary>
    /// 脚本执行列表中注册的脚本分组类型
    /// </summary>
    public enum DBScriptListAccountType
    {
        /// <summary>
        /// PDM中Table、View脚本
        /// </summary>
        PDM = 100,

        /// <summary>
        /// BOS运行平台所需数据
        /// </summary>
        BOS = 200,

        /// <summary>
        /// 管理中心所需数据
        /// </summary>
        MC = 300,

        /// <summary>
        /// 标准业务数据中心所需数据
        /// </summary>
        BusinessDataCenter = 400,

        /// <summary>
        /// 多语言中心所需数据
        /// </summary>
        ML = 500,

        /// <summary>
        /// 开发类型的帐套所需数据
        /// </summary>
        DEV = 800,

        /// <summary>
        /// DEMO类型的帐套所需数据
        /// </summary>
        DEMO = 900,

        /// <summary>
        /// PDM中的Constraint脚本
        /// </summary>
        PDM_Constraint = 10000
    }


    public enum DataBaseBackupType
    {
        /// <summary>
        /// 完整数据库备份
        /// </summary>
        FullDatabaseBackups = 1,

        /// <summary>
        /// 增量数据库备份
        /// </summary>
        PartialBackups = 2,

        /// <summary>
        /// 账簿备份
        /// </summary>
        BookBackups = 3
    }

    /// <summary>
    /// 元数据文件解析类型
    /// </summary>
    public enum ParseMetaDataFileType
    {
        /// <summary>
        /// 所有
        /// </summary>
        ALL = 0x00,

        /// <summary>
        ///业务对象元数据
        /// </summary>
        BusinessObjectMetaData = 0x01,

        /// <summary>
        /// 业务对象依赖项
        /// </summary>
        BusinessObjectDependencyItem = 0x02
    }

    /// <summary>
    /// 数据库业务类型
    /// </summary>
    public enum DatabaseBusinessType
    {
        DataCenter = 0,

        QueryDatabase = 1
    }
}
